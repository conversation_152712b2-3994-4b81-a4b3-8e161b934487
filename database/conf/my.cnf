[mysqld]
# 字符编码设置
character-set-server=utf8mb4
collation-server=utf8mb4_unicode_ci
init-connect='SET NAMES utf8mb4'

# 基本配置
default-storage-engine=INNODB
max_connections=200
max_connect_errors=6000
open_files_limit=65535
table_open_cache=1024
max_allowed_packet=500M
binlog_cache_size=1M
max_heap_table_size=8M
tmp_table_size=16M
read_buffer_size=2M
read_rnd_buffer_size=8M
sort_buffer_size=8M
join_buffer_size=8M
key_buffer_size=4M
thread_cache_size=8
ft_min_word_len=4

# 日志配置
slow_query_log=1
long_query_time=1
slow_query_log_file=/var/lib/mysql/slow.log
performance_schema=0

# 跳过域名解析
skip-name-resolve

[client]
default-character-set=utf8mb4

[mysql]
default-character-set=utf8mb4 