// 现代化侧边栏样式 - 仅支持light模式
#app {
  .main-container {
    min-height: 100%;
    width: calc(100% - 240px);
    transition: none;
  }

  .sidebarHide {
    margin-left: 0 !important;
  }

  .sidebar-container {
    transition: none;
    width: 240px !important;
    height: 100%;
    background: linear-gradient(180deg, #ffffff 0%, #f8fafc 100%);
    border-right: 1px solid #e2e8f0;
    box-shadow: 0 1px 3px 0 rgba(0, 0, 0, 0.1), 0 1px 2px 0 rgba(0, 0, 0, 0.06);
    overflow: hidden;

    // 重置element-ui样式
    .horizontal-collapse-transition {
      transition: none;
    }

    .scrollbar-wrapper {
      overflow-x: hidden !important;
    }

    .el-scrollbar__bar.is-vertical {
      right: 0px;
    }

    .el-scrollbar {
      height: 100%;
    }

    &.has-logo {
      .el-scrollbar {
        height: calc(100% - 60px);
      }
    }

    .is-horizontal {
      display: none;
    }

    a {
      display: inline-block;
      width: 100%;
      overflow: hidden;
    }

    .svg-icon {
      margin-right: 12px;
      font-size: 18px;
      transition: none;
    }

    .el-menu {
      border: none;
      height: 100%;
      width: 100% !important;
      background: transparent !important;
      padding: 8px 0;
    }

    .el-menu-item, .menu-title {
      overflow: hidden !important;
      text-overflow: ellipsis !important;
      white-space: nowrap !important;
      height: 48px !important;
      line-height: 48px !important;
      margin: 4px 12px;
      border-radius: 8px;
      transition: none;
      font-weight: 500;
      color: #64748b;
      position: relative;

      &:hover {
        color: #409eff !important;

        .svg-icon {
          color: #409eff;
        }
      }

      &.is-active {
        background: #e8f0fe !important;
        color: #409eff !important;
        box-shadow: 0 2px 8px rgba(64, 158, 255, 0.15);

        &::before {
          content: '';
          position: absolute;
          left: 0;
          top: 50%;
          transform: translateY(-50%);
          width: 4px;
          height: 20px;
          background: #409eff;
          border-radius: 0 2px 2px 0;
        }

        .svg-icon {
          color: #409eff;
        }
      }
    }

    .el-menu-item .el-menu-tooltip__trigger {
      display: inline-block !important;
    }

    // 子菜单样式
    .el-sub-menu {
      .el-sub-menu__title {
        height: 48px !important;
        line-height: 48px !important;
        margin: 4px 12px;
        border-radius: 8px;
        transition: none;
        font-weight: 500;
        color: #64748b;

        &:hover {
          color: #409eff !important;

          .svg-icon {
            color: #409eff;
          }
        }

        .svg-icon {
          margin-right: 12px;
          font-size: 18px;
          transition: none;
        }
      }

      .el-menu {
        background: #f8fafc !important;
        margin: 0 12px;
        border-radius: 8px;
        padding: 8px 0;

        .el-menu-item {
          margin: 2px 8px;
          height: 40px !important;
          line-height: 40px !important;
          border-radius: 6px;
          font-size: 14px;
          color: #64748b;

          &:hover {
            color: #409eff !important;
          }

          &.is-active {
            background: #e8f0fe !important;
            color: #409eff !important;
            box-shadow: 0 2px 8px rgba(64, 158, 255, 0.15);

            &::before {
              content: '';
              position: absolute;
              left: 0;
              top: 50%;
              transform: translateY(-50%);
              width: 3px;
              height: 16px;
              background: #409eff;
              border-radius: 0 2px 2px 0;
            }
          }
        }
      }
    }
  }

  // 折叠状态
  .hideSidebar {
    .sidebar-container {
      width: 64px !important;
    }

    .main-container {
      margin-left: 64px;
    }

    .el-menu-item, .el-sub-menu__title {
      margin: 4px 8px;
      padding: 0 16px !important;
      text-align: center;

      .svg-icon {
        margin: 0;
        font-size: 20px;
      }
    }

    .el-sub-menu {
      .el-menu {
        margin: 0 4px;
        padding: 4px 0;
      }
    }
  }

  .withoutAnimation {
    .main-container,
    .sidebar-container {
      transition: none;
    }
  }
}

// 弹出菜单样式
.el-menu--vertical {
  .el-menu {
    .svg-icon {
      margin-right: 12px;
    }
  }

  .el-menu-item {
    &:hover {
      color: #409eff !important;
    }
  }

  .el-sub-menu__title {
    &:hover {
      color: #409eff !important;
    }
  }

  > .el-menu--popup {
    max-height: 100vh;
    overflow-y: auto;
    border-radius: 8px;
    box-shadow: 0 10px 25px rgba(0, 0, 0, 0.15);
    border: 1px solid #e2e8f0;

    &::-webkit-scrollbar-track-piece {
      background: #f1f5f9;
      border-radius: 4px;
    }

    &::-webkit-scrollbar {
      width: 6px;
    }

    &::-webkit-scrollbar-thumb {
      background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
      border-radius: 3px;
    }
  }
}
