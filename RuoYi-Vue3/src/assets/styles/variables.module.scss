// 基础颜色
$blue: #324157;
$light-blue: #333c46;
$red: #C03639;
$pink: #E65D6E;
$green: #30B08F;
$tiffany: #4AB7BD;
$yellow: #FEC171;
$panGreen: #30B08F;

// 现代化主题变量 - 仅支持light模式
$menuText: #64748b;
$menuActiveText: #ffffff;
$menuBg: transparent;
$menuHover: linear-gradient(135deg, #667eea 0%, #764ba2 100%);

// 基础变量
$base-sidebar-width: 240px;
$sideBarWidth: 240px;

// 组件颜色
$--color-primary: #667eea;
$--color-success: #67C23A;
$--color-warning: #E6A23C;
$--color-danger: #F56C6C;
$--color-info: #909399;

:export {
  menuText: $menuText;
  menuActiveText: $menuActiveText;
  menuBg: $menuBg;
  menuHover: $menuHover;
  sideBarWidth: $sideBarWidth;
  
  // 导出基础颜色
  blue: $blue;
  lightBlue: $light-blue;
  red: $red;
  pink: $pink;
  green: $green;
  tiffany: $tiffany;
  yellow: $yellow;
  panGreen: $panGreen;
  
  // 导出组件颜色
  colorPrimary: $--color-primary;
  colorSuccess: $--color-success;
  colorWarning: $--color-warning;
  colorDanger: $--color-danger;
  colorInfo: $--color-info;
}

// CSS变量定义 - 仅支持light模式
:root {
  /* 侧边栏变量 */
  --sidebar-bg: #ffffff;
  --sidebar-text: #64748b;
  --menu-hover: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  --menu-active: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  
  /* 导航栏变量 */
  --navbar-bg: #ffffff;
  --navbar-text: #303133;
  
  /* 标签栏变量 */
  --tags-bg: #ffffff;
  --tags-item-bg: #f8fafc;
  --tags-item-border: #e2e8f0;
  --tags-item-text: #64748b;
  --tags-item-hover: #f1f5f9;
  --tags-close-hover: #94a3b8;
  
  /* 分割窗格变量 */
  --splitpanes-bg: #ffffff;
  --splitpanes-border: #e2e8f0;
  --splitpanes-splitter-bg: #f8fafc;
  --splitpanes-splitter-hover-bg: #f1f5f9;
  
  /* 引用块变量 */
  --blockquote-bg: #f8fafc;
  --blockquote-border: #e2e8f0;
  --blockquote-text: #64748b;
  
  /* Cron时间表达式变量 */
  --cron-border: #e2e8f0;
  
  /* 默认背景 */
  --splitpanes-default-bg: #ffffff;
}

