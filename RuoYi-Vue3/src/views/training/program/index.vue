<template>
  <div class="app-container">
    <!-- 搜索区域 -->
    <el-form :model="queryParams" ref="queryRef" :inline="true" v-show="showSearch" label-width="100px">
      <el-form-item label="方案名称:" prop="programName">
        <el-input
          v-model="queryParams.programName"
          placeholder="请输入方案名称"
          clearable
          style="width: 200px"
          @keyup.enter="handleQuery"
        />
      </el-form-item>
      <el-form-item>
        <el-button type="primary" icon="Search" @click="handleQuery">查询</el-button>
        <el-button icon="Refresh" @click="resetQuery">重置</el-button>
      </el-form-item>
    </el-form>

    <!-- 操作按钮区域 -->
    <el-row :gutter="10" class="mb8">
      <el-col :span="1.5">
        <el-button
          type="primary"
          plain
          icon="Plus"
          @click="handleAdd"
          v-hasPermi="['training:program:add']"
        >新增</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="danger"
          plain
          icon="Delete"
          :disabled="multiple"
          @click="handleDelete"
          v-hasPermi="['training:program:remove']"
        >批量删除</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="info"
          plain
          icon="Upload"
          @click="handleImport"
          v-hasPermi="['training:program:import']"
        >导入</el-button>
      </el-col>
    </el-row>

    <!-- 表格区域 -->
    <el-table v-loading="loading" :data="programList" @selection-change="handleSelectionChange">
      <el-table-column type="selection" width="55" align="center" />
      <el-table-column label="序号" type="index" width="50" align="center" />
      <el-table-column label="方案名称" align="center" prop="programName" width="150" :show-overflow-tooltip="true" />
      <el-table-column label="方案简介" align="center" prop="programIntro" :show-overflow-tooltip="true">
        <template #default="scope">
          <span v-if="scope.row.programIntro">{{ scope.row.programIntro }}</span>
          <span v-else>-</span>
        </template>
      </el-table-column>
      <el-table-column label="培养时间" align="center" prop="trainingTime" width="100">
        <template #default="scope">
          <span v-if="scope.row.trainingTime">{{ scope.row.trainingTime }}年</span>
          <span v-else>-</span>
        </template>
      </el-table-column>
      <el-table-column label="培养人数" align="center" prop="traineeCount" width="100">
        <template #default="scope">
          <span v-if="scope.row.traineeCount">{{ scope.row.traineeCount }}人</span>
          <span v-else>-</span>
        </template>
      </el-table-column>
      <el-table-column label="修订次数" align="center" prop="revisionCount" width="100">
        <template #default="scope">
          <span v-if="scope.row.revisionCount !== undefined">{{ scope.row.revisionCount }}</span>
          <span v-else>-</span>
        </template>
      </el-table-column>
      <el-table-column label="创建人" align="center" prop="createBy" width="100" />
      <el-table-column label="创建时间" align="center" prop="createTime" width="180">
        <template #default="scope">
          <span>{{ parseTime(scope.row.createTime, '{y}-{m}-{d} {h}:{i}:{s}') }}</span>
        </template>
      </el-table-column>
      <el-table-column label="操作" align="center" width="180" class-name="small-padding fixed-width">
        <template #default="scope">
          <el-button link type="primary" @click="handleView(scope.row)">查看</el-button>
          <el-button link type="primary" @click="handleUpdate(scope.row)">编辑</el-button>
          <el-button link type="primary" @click="handleDelete(scope.row)">删除</el-button>
        </template>
      </el-table-column>
    </el-table>

    <!-- 分页组件 -->
    <pagination
      v-show="total > 0"
      :total="total"
      :page="queryParams.pageNum"
      :limit="queryParams.pageSize"
      @update:page="queryParams.pageNum = $event"
      @update:limit="queryParams.pageSize = $event"
      @pagination="getList"
    />

    <!-- 人才培养方案导入对话框 -->
    <ImportDialog
      v-model="importDialogVisible"
      :title="importTitle"
      :import-url="importUrl"
      :template-url="templateUrl"
      @success="handleImportSuccess"
      @error="handleImportError"
      @download-template="handleDownloadTemplate"
    />

    <!-- 人才培养方案详情对话框 -->
    <el-dialog :title="detailTitle" v-model="detailDialogVisible" width="600px" append-to-body>
      <div class="detail-content">
        <div class="detail-item">
          <span class="detail-label">方案名称</span>
          <span class="detail-value">{{ programDetail.programName || '-' }}</span>
        </div>
        
        <div class="detail-item">
          <span class="detail-label">培训时间</span>
          <span class="detail-value">{{ programDetail.trainingTime ? `${programDetail.trainingTime}年` : '-' }}</span>
        </div>
        
        <div class="detail-item">
          <span class="detail-label">预计培养人数</span>
          <span class="detail-value">{{ programDetail.traineeCount ? `${programDetail.traineeCount}人` : '-' }}</span>
        </div>
        
        <div class="detail-item">
          <span class="detail-label">方案简介</span>
          <span class="detail-value">{{ programDetail.programIntro || '-' }}</span>
        </div>
        
        <div class="detail-item">
          <span class="detail-label">培养目标</span>
          <span class="detail-value">{{ programDetail.trainingGoal || '-' }}</span>
        </div>
        
        <div class="detail-item">
          <span class="detail-label">培养规格</span>
          <span class="detail-value">{{ programDetail.trainingSpec || '-' }}</span>
        </div>
      </div>
    </el-dialog>
  </div>
</template>

<script setup name="TrainingProgram">
import { listProgram, delProgram, getProgram } from "@/api/training/program"
import ImportDialog from "@/components/ImportDialog/index.vue"
import { useImport, createImportConfig } from "@/composables/useImport"

const { proxy } = getCurrentInstance()

const programList = ref([])
const loading = ref(true)
const showSearch = ref(true)
const ids = ref([])
const single = ref(true)
const multiple = ref(true)
const total = ref(0)

// 详情弹框相关
const detailDialogVisible = ref(false)
const detailTitle = ref('人才培养方案详情')
const programDetail = reactive({
  programId: undefined,
  programName: '',
  programIntro: '',
  trainingTime: '',
  traineeCount: '',
  trainingGoal: '',
  trainingSpec: '',
  revisionCount: 0,
  createBy: '',
  createTime: '',
  updateBy: '',
  updateTime: ''
})

// 使用导入组合式函数
const importConfig = createImportConfig(
  '/training/program',
  '人才培养方案',
  getList,
  proxy.download
)

const {
  importDialogVisible,
  importTitle,
  openImportDialog,
  handleImportSuccess,
  handleImportError,
  handleDownloadTemplate,
  importUrl,
  templateUrl
} = useImport(importConfig)

const data = reactive({
  queryParams: {
    pageNum: 1,
    pageSize: 10,
    programName: undefined
  }
})

const { queryParams } = toRefs(data)

/** 查询人才培养方案列表 */
function getList() {
  loading.value = true
  listProgram(queryParams.value).then(response => {
    programList.value = response.rows
    total.value = response.total
    loading.value = false
  })
}

/** 搜索按钮操作 */
function handleQuery() {
  queryParams.value.pageNum = 1
  getList()
}

/** 重置按钮操作 */
function resetQuery() {
  proxy.resetForm("queryRef")
  handleQuery()
}

/** 多选框选中数据 */
function handleSelectionChange(selection) {
  ids.value = selection.map(item => item.programId)
  single.value = selection.length !== 1
  multiple.value = !selection.length
}

/** 新增按钮操作 */
function handleAdd() {
  // 跳转到新增页面
  proxy.$router.push('/training/program/form')
}

/** 查看按钮操作 */
function handleView(row) {
  // 获取详情数据并显示弹框
  getProgram(row.programId).then(response => {
    Object.assign(programDetail, response.data)
    detailDialogVisible.value = true
  })
}

/** 修改按钮操作 */
function handleUpdate(row) {
  // 跳转到编辑页面
  proxy.$router.push(`/training/program/form?id=${row.programId}`)
}

/** 删除按钮操作 */
function handleDelete(row) {
  const programIds = row.programId || ids.value
  const programNames = row.programName || programList.value.filter(item => ids.value.includes(item.programId)).map(item => item.programName).join('、')

  proxy.$modal.confirm('是否确认删除人才培养方案"' + programNames + '"？').then(function() {
    return delProgram(programIds)
  }).then(() => {
    getList()
    proxy.$modal.msgSuccess("删除成功")
  }).catch(() => {})
}

/** 导入按钮操作 */
function handleImport() {
  openImportDialog()
}

onMounted(() => {
  getList()
})

// 监听路由变化，当从表单页面返回时刷新列表
onActivated(() => {
  getList()
})
</script>

<style scoped>
.detail-content {
  padding: 20px;
}

.detail-item {
  display: flex;
  margin-bottom: 15px;
  padding-bottom: 10px;
  border-bottom: 1px solid #f0f0f0;
}

.detail-item:last-child {
  border-bottom: none;
  margin-bottom: 0;
}

.detail-label {
  width: 120px;
  font-weight: 600;
  color: #606266;
  flex-shrink: 0;
}

.detail-value {
  flex: 1;
  color: #303133;
  word-break: break-all;
  line-height: 1.6;
}
</style> 