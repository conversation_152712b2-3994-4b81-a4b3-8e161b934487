<template>
  <div class="app-container">
    <!-- 头部导航 -->
    <div class="page-header">
      <el-button @click="goBack" type="text" icon="ArrowLeft" class="back-btn">返回</el-button>
      <h2 class="page-title">{{ isEdit ? '编辑' : '新建' }}</h2>
    </div>

    <!-- 表单内容 -->
    <el-form
      ref="formRef"
      :model="form"
      :rules="rules"
      label-width="120px"
      class="program-form"
    >
      <!-- 基础信息 -->
      <div class="form-section">
        <div class="section-header">
          <div class="section-title">基础信息</div>
        </div>
        
        <el-row>
          <el-col :span="24">
            <el-form-item label="方案名称" prop="programName">
              <el-input
                v-model="form.programName"
                placeholder="请输入"
                maxlength="60"
                show-word-limit
                style="width: 800px"
                @input="validateField('programName')"
              />
            </el-form-item>
          </el-col>
        </el-row>

        <el-row>
          <el-col :span="24">
            <el-form-item label="方案简介" prop="programIntro">
              <el-input
                v-model="form.programIntro"
                type="textarea"
                placeholder="请输入"
                :rows="4"
                maxlength="600"
                show-word-limit
                style="width: 800px"
              />
            </el-form-item>
          </el-col>
        </el-row>

        <el-row>
          <el-col :span="12">
            <el-form-item label="培训时间" prop="trainingTime">
              <el-input
                v-model="form.trainingTime"
                placeholder="请输入"
                style="width: 200px"
                data-field="trainingTime"
                @input="handleNumberInput('trainingTime')"
                @paste="handlePaste"
              />
              <span style="margin-left: 10px;">年</span>
            </el-form-item>
          </el-col>
  
        </el-row>

        <el-row>    
            <el-col :span="12">
            <el-form-item label="预计培养人数" prop="traineeCount">
              <el-input
                v-model="form.traineeCount"
                placeholder="请输入"
                style="width: 200px"
                data-field="traineeCount"
                @input="handleNumberInput('traineeCount')"
                @paste="handlePaste"
              />
              <span style="margin-left: 10px;">人</span>
            </el-form-item>
          </el-col>
        </el-row>
      </div>

      <!-- 成果导向矩阵 -->
      <div class="form-section">
        <div class="section-header">
          <div class="section-title">成果导向矩阵</div>
        </div>
        
        <el-row>
          <el-col :span="24">
            <el-form-item label="培养目标:" prop="trainingGoal">
              <el-input
                v-model="form.trainingGoal"
                type="textarea"
                placeholder="请输入"
                :rows="6"
                maxlength="600"
                show-word-limit
                style="width: 800px"
              />
            </el-form-item>
          </el-col>
        </el-row>

        <el-row>
          <el-col :span="24">
            <el-form-item label="培养规格:" prop="trainingSpec">
              <el-input
                v-model="form.trainingSpec"
                type="textarea"
                placeholder="请输入"
                :rows="6"
                maxlength="600"
                show-word-limit
                style="width: 800px"
              />
            </el-form-item>
          </el-col>
        </el-row>
      </div>

      <!-- 操作按钮 -->
      <div class="form-actions">
        <el-button @click="goBack">取消</el-button>
        <el-button type="primary" @click="submitForm" :loading="submitLoading">确定</el-button>
      </div>
    </el-form>
  </div>
</template>

<script setup name="TrainingProgramForm">
import { getProgram, addProgram, updateProgram } from "@/api/training/program"

const { proxy } = getCurrentInstance()
const route = useRoute()
const router = useRouter()

const formRef = ref()
const submitLoading = ref(false)
const isEdit = ref(false)
const programId = ref(null)

const form = reactive({
  programId: undefined,
  programName: '',
  programIntro: '',
  trainingTime: '',
  traineeCount: '',
  trainingGoal: '',
  trainingSpec: '',
  revisionCount: 0
})

const rules = reactive({
  programName: [
    { required: true, message: '方案名称不能为空', trigger: ['blur', 'change'] },
    { min: 1, max: 60, message: '方案名称长度必须介于 1 和 60 之间', trigger: ['blur', 'change'] }
  ],
  trainingTime: [
    { required: true, message: '培训时间不能为空', trigger: ['blur', 'change'] },
    { 
      validator: (rule, value, callback) => {
        const num = Number(value)
        if (isNaN(num)) {
          callback(new Error('请输入有效的数字'))
        } else if (num < 1 || num > 10) {
          callback(new Error('培训时间必须在1-10年之间'))
        } else {
          callback()
        }
      }, 
      trigger: ['blur', 'change'] 
    }
  ],
  traineeCount: [
    { required: true, message: '预计培养人数不能为空', trigger: ['blur', 'change'] },
    { 
      validator: (rule, value, callback) => {
        const num = Number(value)
        if (isNaN(num)) {
          callback(new Error('请输入有效的数字'))
        } else if (num < 1 || num > 1000) {
          callback(new Error('预计培养人数必须在1-1000人之间'))
        } else {
          callback()
        }
      }, 
      trigger: ['blur', 'change'] 
    }
  ]
})

/** 返回上一页 */
function goBack() {
  router.go(-1)
}

/** 验证单个字段 */
function validateField(fieldName) {
  proxy.$refs.formRef.validateField(fieldName)
}

/** 处理数字输入 */
function handleNumberInput(fieldName) {
  // 只保留数字，过滤掉所有非数字字符
  let value = form[fieldName] || ''
  value = value.toString().replace(/[^0-9]/g, '')
  form[fieldName] = value
  // 触发验证
  validateField(fieldName)
}

/** 处理粘贴事件 */
function handlePaste(event) {
  event.preventDefault()
  const pastedText = (event.clipboardData || window.clipboardData).getData('text')
  const numbersOnly = pastedText.replace(/[^0-9]/g, '')
  if (numbersOnly) {
    const target = event.target
    const start = target.selectionStart
    const end = target.selectionEnd
    const currentValue = target.value
    const newValue = currentValue.substring(0, start) + numbersOnly + currentValue.substring(end)
    target.value = newValue
    // 更新表单数据
    const fieldName = target.getAttribute('data-field') || 'trainingTime'
    form[fieldName] = newValue
    // 触发验证
    validateField(fieldName)
  }
}

/** 获取人才培养方案详情 */
function getProgramDetail(id) {
  getProgram(id).then(response => {
    Object.assign(form, response.data)
  })
}

/** 提交表单 */
function submitForm() {
  formRef.value.validate((valid) => {
    if (valid) {
      submitLoading.value = true
      
      const submitData = {
        ...form,
        trainingTime: parseInt(form.trainingTime),
        traineeCount: parseInt(form.traineeCount)
      }

      if (isEdit.value) {
        // 编辑时增加修订次数
        submitData.revisionCount = (form.revisionCount || 0) + 1
        updateProgram(submitData).then(() => {
          proxy.$modal.msgSuccess("修改成功")
          goBack()
        }).finally(() => {
          submitLoading.value = false
        })
      } else {
        addProgram(submitData).then(() => {
          proxy.$modal.msgSuccess("新增成功")
          // 通知列表页面刷新
          proxy.$router.go(-1)
        }).finally(() => {
          submitLoading.value = false
        })
      }
    }
  })
}

/** 初始化 */
onMounted(() => {
  const id = route.query.id
  if (id) {
    isEdit.value = true
    programId.value = id
    getProgramDetail(id)
  }
})
</script>

<style scoped>
.page-header {
  display: flex;
  align-items: center;
  margin-bottom: 20px;
  padding-bottom: 15px;
  border-bottom: 1px solid #ebeef5;
}

.back-btn {
  margin-right: 15px;
  font-size: 16px;
}

.page-title {
  margin: 0;
  font-size: 20px;
  font-weight: 600;
  color: #303133;
}

.program-form {
  margin: 0 auto;
}

.form-section {
  margin-bottom: 30px;
  padding: 20px;
  background: #fff;
  border-radius: 8px;
  box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
}

.section-header {
  margin-bottom: 20px;
  padding-bottom: 10px;
  border-bottom: 2px solid #409eff;
}

.section-title {
  font-size: 16px;
  font-weight: 600;
  color: #409eff;
  position: relative;
  padding-left: 12px;
}

.section-title::before {
  content: '';
  position: absolute;
  left: 0;
  top: 50%;
  transform: translateY(-50%);
  width: 4px;
  height: 16px;
  background: #409eff;
  border-radius: 2px;
}

.form-actions {
  text-align: center;
  margin-top: 30px;
  padding-top: 20px;
  border-top: 1px solid #ebeef5;
}

.form-actions .el-button {
  margin: 0 10px;
  min-width: 100px;
}
</style> 