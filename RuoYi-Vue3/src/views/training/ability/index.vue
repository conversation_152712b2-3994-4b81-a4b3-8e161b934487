<template>
  <div class="app-container">
    <!-- 搜索区域 -->
    <el-form :model="queryParams" ref="queryRef" :inline="true" v-show="showSearch" label-width="100px">
      <el-form-item label="职业能力名称:" prop="abilityName">
        <el-input
          v-model="queryParams.abilityName"
          placeholder="请输入技能名称"
          clearable
          style="width: 200px"
          @keyup.enter="handleQuery"
        />
      </el-form-item>
      <el-form-item>
        <el-button type="primary" icon="Search" @click="handleQuery">查询</el-button>
        <el-button icon="Refresh" @click="resetQuery">重置</el-button>
      </el-form-item>
    </el-form>

    <!-- 操作按钮区域 -->
    <el-row :gutter="10" class="mb8">
      <el-col :span="1.5">
        <el-button
          type="primary"
          plain
          icon="Plus"
          @click="handleAdd"
          v-hasPermi="['training:ability:add']"
        >新增</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="danger"
          plain
          icon="Delete"
          :disabled="multiple"
          @click="handleDelete"
          v-hasPermi="['training:ability:remove']"
        >批量删除</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="info"
          plain
          icon="Upload"
          @click="handleImport"
          v-hasPermi="['training:ability:import']"
        >导入</el-button>
      </el-col>
    </el-row>

    <!-- 表格区域 -->
    <el-table v-loading="loading" :data="abilityList" @selection-change="handleSelectionChange">
      <el-table-column type="selection" width="55" align="center" />
      <el-table-column label="序号" type="index" width="50" align="center" />
      <el-table-column label="职业能力名称" align="center" prop="abilityName" width="150" :show-overflow-tooltip="true" />
      <el-table-column label="职业能力描述" align="center" prop="abilityDesc" :show-overflow-tooltip="true" />
      <el-table-column label="操作" align="center" width="180" class-name="small-padding fixed-width">
        <template #default="scope">
          <el-button link type="primary" @click="handleUpdate(scope.row)">编辑</el-button>
          <el-button link type="primary" @click="handleDelete(scope.row)">删除</el-button>
        </template>
      </el-table-column>
    </el-table>

    <!-- 分页组件 -->
    <pagination
      v-show="total > 0"
      :total="total"
      :page="queryParams.pageNum"
      :limit="queryParams.pageSize"
      @update:page="queryParams.pageNum = $event"
      @update:limit="queryParams.pageSize = $event"
      @pagination="getList"
    />

    <!-- 职业能力新增/编辑对话框 -->
    <AbilityDialog
      :visible="open"
      :title="title"
      :ability-data="form"
      @update:visible="open = $event"
      @success="handleSuccess"
      @close="cancel"
    />

    <!-- 职业能力导入对话框 -->
    <ImportDialog
      v-model="importDialogVisible"
      :title="importTitle"
      :import-url="importUrl"
      :template-url="templateUrl"
      @success="handleImportSuccess"
      @error="handleImportError"
      @download-template="handleDownloadTemplate"
    />
  </div>
</template>

<script setup name="ProfessionalAbility">
import { listAbility, getAbility, delAbility, addAbility, updateAbility } from "@/api/training/ability"
import ImportDialog from "@/components/ImportDialog/index.vue"
import AbilityDialog from "@/components/AbilityDialog/index.vue"
import { useImport, createImportConfig } from "@/composables/useImport"

const { proxy } = getCurrentInstance()

const abilityList = ref([])
const open = ref(false)
const loading = ref(true)
const showSearch = ref(true)
const ids = ref([])
const single = ref(true)
const multiple = ref(true)
const total = ref(0)
const title = ref("")

// 使用导入组合式函数
const importConfig = createImportConfig(
  '/training/ability',
  '职业能力',
  getList,
  proxy.download
)

const {
  importDialogVisible,
  importTitle,
  openImportDialog,
  handleImportSuccess,
  handleImportError,
  handleDownloadTemplate,
  importUrl,
  templateUrl
} = useImport(importConfig)

const data = reactive({
  form: {},
  queryParams: {
    pageNum: 1,
    pageSize: 10,
    abilityName: undefined
  },
  rules: {
    abilityName: [
      { required: true, message: "职业能力名称不能为空", trigger: "blur" },
      { min: 1, max: 12, message: "职业能力名称长度必须介于 1 和 12 之间", trigger: "blur" }
    ],
    thresholdValue: [
      { required: true, message: "达标阈值不能为空", trigger: "blur" },
      { type: 'number', min: 0, max: 100, message: "达标阈值必须在0-100之间", trigger: "blur" }
    ]
  }
})

const { queryParams, form, rules } = toRefs(data)

/** 查询职业能力列表 */
function getList() {
  loading.value = true
  listAbility(queryParams.value).then(response => {
    abilityList.value = response.rows
    total.value = response.total
    loading.value = false
  })
}

/** 取消按钮 */
function cancel() {
  open.value = false
  reset()
}

/** 表单重置 */
function reset() {
  form.value = {
    abilityId: undefined,
    abilityName: undefined,
    abilityDesc: undefined,
    thresholdValue: 60
  }
  proxy.resetForm("abilityRef")
}

/** 搜索按钮操作 */
function handleQuery() {
  queryParams.value.pageNum = 1
  getList()
}

/** 重置按钮操作 */
function resetQuery() {
  proxy.resetForm("queryRef")
  handleQuery()
}

/** 多选框选中数据 */
function handleSelectionChange(selection) {
  ids.value = selection.map(item => item.abilityId)
  single.value = selection.length !== 1
  multiple.value = !selection.length
}

/** 新增按钮操作 */
function handleAdd() {
  reset()
  open.value = true
  title.value = "新建"
}

/** 修改按钮操作 */
function handleUpdate(row) {
  reset()
  const abilityId = row.abilityId || ids.value
  getAbility(abilityId).then(response => {
    form.value = response.data
    open.value = true
    title.value = "编辑"
  })
}

/** 处理成功回调 */
function handleSuccess(data) {
  getList()
}

/** 提交按钮 - 已废弃，使用组件内部处理 */
function submitForm() {
  // 此方法已废弃，由AbilityDialog组件内部处理
}

/** 删除按钮操作 */
function handleDelete(row) {
  const abilityIds = row.abilityId || ids.value
  const abilityNames = row.abilityName || abilityList.value.filter(item => ids.value.includes(item.abilityId)).map(item => item.abilityName).join('、')

  proxy.$modal.confirm('是否确认删除职业能力"' + abilityNames + '"？').then(function() {
    return delAbility(abilityIds)
  }).then(() => {
    getList()
    proxy.$modal.msgSuccess("删除成功")
  }).catch(() => {})
}

/** 导入按钮操作 */
function handleImport() {
  openImportDialog()
}

onMounted(() => {
  getList()
})
</script> 