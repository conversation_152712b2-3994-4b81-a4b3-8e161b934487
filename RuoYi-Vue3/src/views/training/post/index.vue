<template>
  <div class="app-container">
    <!-- 搜索区域 -->
    <el-form :model="queryParams" ref="queryRef" :inline="true" v-show="showSearch" label-width="68px">
      <el-form-item label="岗位名称" prop="postName">
        <el-input
          v-model="queryParams.postName"
          placeholder="请输入岗位名称"
          clearable
          style="width: 240px"
          @keyup.enter="handleQuery"
        />
      </el-form-item>
      <el-form-item>
        <el-button type="primary" icon="Search" @click="handleQuery">搜索</el-button>
        <el-button icon="Refresh" @click="resetQuery">重置</el-button>
      </el-form-item>
    </el-form>

    <!-- 操作按钮区域 -->
    <el-row :gutter="10" class="mb8">
      <el-col :span="1.5">
        <el-button type="primary" plain icon="Plus" @click="handleAdd">新增</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button type="danger" plain icon="Delete" :disabled="multiple" @click="handleDelete">批量删除</el-button>
      </el-col>
    </el-row>

    <!-- 表格区域 -->
    <el-table v-loading="loading" :data="postList" @selection-change="handleSelectionChange">
      <el-table-column type="selection" width="55" align="center" />
      <el-table-column label="序号" type="index" width="50" align="center" />
      <el-table-column label="岗位名称" align="center" prop="postName" width="150" :show-overflow-tooltip="true" />
      <el-table-column label="岗位描述" align="center" prop="postDesc" :show-overflow-tooltip="true" />
      <el-table-column label="操作" align="center" width="280" class-name="small-padding fixed-width">
        <template #default="scope">
          <el-button link type="primary"  @click="handleUpdate(scope.row)">编辑</el-button>
          <el-button link type="primary"  @click="handleConfig(scope.row)">配置模型</el-button>
          <el-button link type="primary"  @click="handleCopy(scope.row)">复制</el-button>
          <el-button link type="primary"  @click="handleDelete(scope.row)">删除</el-button>
        </template>
      </el-table-column>
    </el-table>

    <!-- 分页组件 -->
    <pagination
      v-show="total > 0"
      :total="total"
      :page="queryParams.pageNum"
      :limit="queryParams.pageSize"
      @pagination="getList"
    />

    <!-- 新增/编辑岗位对话框 -->
    <el-dialog :title="title" v-model="open" width="600px" append-to-body>
      <el-form ref="postRef" :model="form" :rules="rules" label-width="80px">
        <el-row>
          <el-col :span="24">
            <el-form-item label="岗位名称" prop="postName">
              <el-input
                v-model="form.postName"
                placeholder="请输入岗位名称"
                maxlength="12"
                show-word-limit
              />
            </el-form-item>
          </el-col>
        </el-row>
        <el-row>
          <el-col :span="24">
            <el-form-item label="需求企业" prop="companyIds">
              <el-select
                v-model="form.companyIds"
                multiple
                placeholder="请选择企业（可多选）"
                style="width: 100%"
                collapse-tags
                collapse-tags-tooltip
              >
                <el-option
                  v-for="company in companyOptions"
                  :key="company.id"
                  :label="company.name"
                  :value="company.id"
                />
              </el-select>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row>
          <el-col :span="24">
            <el-form-item label="岗位描述" prop="postDesc">
              <el-input
                v-model="form.postDesc"
                type="textarea"
                placeholder="请输入岗位描述"
                :rows="4"
                maxlength="200"
                show-word-limit
              />
            </el-form-item>
          </el-col>
        </el-row>
      </el-form>
      <template #footer>
        <div class="dialog-footer">
          <el-button type="primary" @click="submitForm">确 定</el-button>
          <el-button @click="cancel">取 消</el-button>
        </div>
      </template>
    </el-dialog>

    <!-- 复制岗位对话框 -->
    <el-dialog title="复制" v-model="copyOpen" width="400px" append-to-body>
      <el-form ref="copyRef" :model="copyForm" :rules="copyRules" label-width="80px">
        <el-form-item label="岗位名称" prop="postName">
          <el-input
            v-model="copyForm.postName"
            placeholder="请输入岗位名称"
            maxlength="12"
            show-word-limit
          />
        </el-form-item>
      </el-form>
      <template #footer>
        <div class="dialog-footer">
          <el-button type="primary" @click="submitCopyForm">确 定</el-button>
          <el-button @click="cancelCopy">取 消</el-button>
        </div>
      </template>
    </el-dialog>
  </div>
</template>

<script setup name="PostModel">
import { listPost, getPost, delPost, addPost, updatePost, copyPost, listCompany } from "@/api/training/post"

const { proxy } = getCurrentInstance()

const postList = ref([])
const open = ref(false)
const copyOpen = ref(false)
const loading = ref(true)
const showSearch = ref(true)
const ids = ref([])
const single = ref(true)
const multiple = ref(true)
const total = ref(0)
const title = ref("")
const companyOptions = ref([])

const data = reactive({
  form: {},
  copyForm: {},
  queryParams: {
    pageNum: 1,
    pageSize: 10,
    postName: undefined
  },
  rules: {
    postName: [
      { required: true, message: "岗位名称不能为空", trigger: "blur" },
      { min: 1, max: 12, message: "岗位名称长度必须介于 1 和 12 之间", trigger: "blur" }
    ]
  },
  copyRules: {
    postName: [
      { required: true, message: "岗位名称不能为空", trigger: "blur" },
      { min: 1, max: 12, message: "岗位名称长度必须介于 1 和 12 之间", trigger: "blur" }
    ]
  }
})

const { queryParams, form, copyForm, rules, copyRules } = toRefs(data)

/** 查询岗位模型列表 */
function getList() {
  loading.value = true
  listPost(queryParams.value).then(response => {
    postList.value = response.rows
    total.value = response.total
    loading.value = false
  })
}

/** 查询企业列表 */
function getCompanyList() {
  listCompany().then(response => {
    companyOptions.value = response.data || []
  })
}

/** 取消按钮 */
function cancel() {
  open.value = false
  reset()
}

/** 取消复制按钮 */
function cancelCopy() {
  copyOpen.value = false
  resetCopy()
}

/** 表单重置 */
function reset() {
  form.value = {
    postId: undefined,
    postName: undefined,
    postDesc: undefined,
    companyIds: []
  }
  proxy.resetForm("postRef")
}

/** 复制表单重置 */
function resetCopy() {
  copyForm.value = {
    postId: undefined,
    postName: undefined
  }
  proxy.resetForm("copyRef")
}

/** 搜索按钮操作 */
function handleQuery() {
  queryParams.value.pageNum = 1
  getList()
}

/** 重置按钮操作 */
function resetQuery() {
  proxy.resetForm("queryRef")
  handleQuery()
}

/** 多选框选中数据 */
function handleSelectionChange(selection) {
  ids.value = selection.map(item => item.postId)
  single.value = selection.length !== 1
  multiple.value = !selection.length
}

/** 新增按钮操作 */
function handleAdd() {
  reset()
  getCompanyList()
  open.value = true
  title.value = "新增岗位"
}

/** 修改按钮操作 */
function handleUpdate(row) {
  reset()
  getCompanyList()
  const postId = row.postId || ids.value
  getPost(postId).then(response => {
    form.value = response.data
    form.value.companyIds = response.data.companyIds || []
    open.value = true
    title.value = "编辑岗位"
  })
}

/** 配置模型按钮操作 */
function handleConfig(row) {
  // 跳转到配置模型页面
  proxy.$router.push({
    path: '/training/post/config',
    query: {
      postId: row.postId,
      postName: row.postName
    }
  })
}

/** 提交按钮 */
function submitForm() {
  proxy.$refs["postRef"].validate(valid => {
    if (valid) {
      if (form.value.postId != undefined) {
        updatePost(form.value).then(() => {
          proxy.$modal.msgSuccess("修改成功")
          open.value = false
          getList()
        })
      } else {
        addPost(form.value).then(() => {
          proxy.$modal.msgSuccess("新增成功")
          open.value = false
          getList()
        })
      }
    }
  })
}

/** 复制按钮操作 */
function handleCopy(row) {
  resetCopy()
  copyForm.value.postId = row.postId
  copyForm.value.postName = ""
  copyOpen.value = true
}

/** 提交复制表单 */
function submitCopyForm() {
  proxy.$refs["copyRef"].validate(valid => {
    if (valid) {
      copyPost(copyForm.value).then(() => {
        proxy.$modal.msgSuccess("复制成功")
        copyOpen.value = false
        getList()
      })
    }
  })
}

/** 删除按钮操作 */
function handleDelete(row) {
  const postIds = row.postId || ids.value
  const postNames = row.postName || postList.value.filter(item => ids.value.includes(item.postId)).map(item => item.postName).join('、')

  proxy.$modal.confirm('是否确认删除岗位"' + postNames + '"？').then(function() {
    // 确保 postIds 是字符串格式（逗号分隔）
    const postIdsStr = Array.isArray(postIds) ? postIds.join(',') : postIds
    return delPost(postIdsStr)
  }).then(() => {
    getList()
    proxy.$modal.msgSuccess("删除成功")
  }).catch(() => {})
}

onMounted(() => {
  getList()
})
</script>