<template>
  <div class="app-container">
    <!-- 头部导航 -->
    <div class="header-nav">
      <el-button @click="handleBack" icon="ArrowLeft" type="text">返回</el-button>
      <h2 class="page-title">新增岗位模型</h2>
      <el-button type="primary" @click="handleSave">保存</el-button>
    </div>

    <!-- 职业能力配置区域 -->
    <div class="ability-config-section">
      <el-card shadow="never" class="config-card">
        <template #header>
          <div class="card-header">
            <div class="header-actions">
              <el-button type="primary" @click="addAbility" :disabled="abilityList.length >= 10">
                <el-icon><Plus /></el-icon>
                新增职业能力
              </el-button>
              <span class="note ml-4">注: 至少3项、至多10项技能</span>
            </div>
            <div class="info-content">
              <span class="info-label">岗位名称:</span>
              <span class="info-value ml-2">{{ postName }}</span>
            </div>
          </div>
        </template>

              <!-- 职业能力配置表格 -->
        <div class="ability-table">
          <div class="table-header">
            <div class="header-cell flex">
              <span>职业能力名称</span>
              <el-button type="text" size="small" @click="handleAddAbility" :disabled="abilityList.length >= 10" >
                ⊕ 新建
              </el-button>
            </div>
            <div class="header-cell header-threshold">能力达标规则</div>
            <div class="header-cell header-course">关联课程</div>
            <div class="header-cell header-weight">能力值评价规则</div>
            <div class="header-cell header-action">操作</div>
          </div>

          <div class="table-body">
            <div v-for="(ability, abilityIndex) in abilityList" :key="abilityIndex" class="ability-group">
              <div class="ability-wrapper">
                <!-- 职业能力名称列 - 跨越所有课程行 -->
                <div class="ability-name-column" :style="{ gridRowEnd: `span ${Math.max(1, ability.courseRows.length)}` }">
                  <el-select 
                    v-model="ability.abilityId" 
                    placeholder="请选择职业能力"
                    @change="(value) => handleAbilityChange(value, abilityIndex)"
                    style="width: 100%"
                  >
                    <el-option
                      v-for="option in abilityOptions"
                      :key="option.abilityId"
                      :label="option.abilityName"
                      :value="option.abilityId"
                    />
                  </el-select>
                </div>

                <!-- 能力达标规则列 - 跨越所有课程行 -->
                <div class="threshold-column" :style="{ gridRowEnd: `span ${Math.max(1, ability.courseRows.length)}` }">
                  <div class="threshold-control">
                    <span>能力值评价分数≥</span>
                    <el-input-number
                      v-model="ability.threshold"
                      :min="0"
                      :max="100"
                      :step="5"
                      size="small"
                      controls-position="right"
                      style="width: 90px; margin: 0 4px;"
                    />
                    <span>分,为达标</span>
                  </div>
                </div>

                <!-- 课程区域 -->
                <div class="courses-area">
                  <!-- 如果没有课程，显示一个默认行 -->
                  <div v-if="ability.courseRows.length === 0" class="course-row">
                    <div class="course-cell">
                      <el-button type="text" @click="selectCourse(abilityIndex)">
                        请选择课程
                      </el-button>
                    </div>
                    <div class="weight-cell">
                      <el-input-number
                        v-model="ability.weight"
                        :min="0"
                        :max="100"
                        :step="10"
                        size="middle"
                      >
                        <template #suffix>
                          <span>%</span>
                        </template>
                      </el-input-number>
                    </div>
                    <div class="action-cell">
                      <el-button 
                        type="primary" 
                        size="middle" 
                        @click="addCourseRow(abilityIndex)"
                        style="margin-right: 8px;"
                      >
                        + 添加关联课程
                      </el-button>
                      <el-button 
                        type="danger" 
                        size="middle" 
                        @click="removeAbility(abilityIndex)"
                        :disabled="abilityList.length <= 3"
                      >
                        <el-icon><Delete /></el-icon>
                      </el-button>
                    </div>
                  </div>

                  <!-- 已选择的课程行 -->
                  <div 
                    v-for="(courseRow, courseIndex) in ability.courseRows" 
                    :key="courseIndex"
                    class="course-row"
                  >
                    <!-- 关联课程 -->
                    <div class="course-cell">
                        <el-button type="text" @click="selectCourseForRow(abilityIndex, courseIndex)">
                          <span v-if="courseRow.courseName" class="course-name">{{ courseRow.courseName }}</span>
                          <span v-else class="course-name">请选择课程</span>
                        </el-button>
                    </div>

                    <!-- 能力值评价规则 -->
                    <div class="weight-cell">
                      <el-input-number
                        v-model="courseRow.weight"
                        :min="0"
                        :max="100"
                        :step="10"
                        size="middle"
                      >
                        <template #suffix>
                          <span>%</span>
                        </template>
                      </el-input-number>
                    </div>

                    <!-- 操作 -->
                    <div class="action-cell">
                      <el-button 
                        type="primary" 
                        size="middle" 
                        @click="addCourseRow(abilityIndex)"
                        style="margin-right: 8px;"
                      >
                        + 添加关联课程
                      </el-button>
                      <el-button 
                        v-if="courseIndex === 0"
                        type="danger" 
                        size="middle" 
                        @click="removeAbility(abilityIndex)"
                        :disabled="abilityList.length <= 3"
                      >
                        <el-icon><Delete /></el-icon>
                      </el-button>
                      <el-button 
                        v-if="courseIndex > 0"
                        type="danger" 
                        size="middle" 
                        @click="removeCourseRow(abilityIndex, courseIndex)"
                      >
                        <el-icon><Delete /></el-icon>
                      </el-button>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </el-card>
    </div>

    <!-- 课程选择对话框 -->
    <el-dialog 
      v-model="courseDialogVisible" 
      title="选择课程" 
      width="800px"
      :before-close="handleCourseDialogClose"
    >
      <div class="course-dialog-content">
        <!-- 搜索栏 -->
        <div class="search-bar">
          <el-input
            v-model="courseSearchKeyword"
            placeholder="请输入课程名称"
            style="width: 300px"
            clearable
            @input="handleCourseSearch"
          >
            <template #prefix>
              <el-icon><Search /></el-icon>
            </template>
          </el-input>
          <el-button @click="resetCourseSearch">重置</el-button>
        </div>

        <!-- 课程类型标签页 -->
        <el-tabs v-model="activeCourseTab" @tab-change="handleCourseTabChange">
          <el-tab-pane label="学校课程" name="school">
            <div class="course-table">
              <div class="course-table-header">
                <div class="course-header-cell">课程名称</div>
                <div class="course-header-cell">课程目标</div>
                <div class="course-header-cell">操作</div>
              </div>
              <div class="course-table-body">
                <div 
                  v-for="course in schoolCourses" 
                  :key="course.courseId"
                  class="course-table-row"
                  @click="selectSingleCourse(course)"
                >
                  <div class="course-table-cell">{{ course.courseName }}</div>
                  <div class="course-table-cell">{{ course.courseObjective }}</div>
                  <div class="course-table-cell">
                    <el-button type="primary" size="small">选择</el-button>
                  </div>
                </div>
              </div>
            </div>
          </el-tab-pane>
          
          <el-tab-pane label="企业课程" name="enterprise">
            <div class="course-table">
              <div class="course-table-header">
                <div class="course-header-cell">课程名称</div>
                <div class="course-header-cell">课程目标</div>
                <div class="course-header-cell">操作</div>
              </div>
              <div class="course-table-body">
                <div 
                  v-for="course in enterpriseCourses" 
                  :key="course.courseId"
                  class="course-table-row"
                  @click="selectSingleCourse(course)"
                >
                  <div class="course-table-cell">{{ course.courseName }}</div>
                  <div class="course-table-cell">{{ course.courseObjective }}</div>
                  <div class="course-table-cell">
                    <el-button type="primary" size="small">选择</el-button>
                  </div>
                </div>
              </div>
            </div>
          </el-tab-pane>
        </el-tabs>

        <!-- 分页 -->
        <div class="pagination-container">
          <el-pagination
            :current-page="coursePageNum"
            :page-size="coursePageSize"
            @update:current-page="coursePageNum = $event"
            layout="prev, pager, next"
          />
        </div>
      </div>

      <template #footer>
        <div class="dialog-footer">
          <el-button @click="courseDialogVisible = false">关闭</el-button>
        </div>
      </template>
    </el-dialog>

    <!-- 职业能力新增弹框 -->
    <AbilityDialog
      :visible="abilityDialogVisible"
      title="新增职业能力"
      @update:visible="abilityDialogVisible = $event"
      @success="handleAbilitySuccess"
      @close="closeAbilityDialog"
    />
  </div>
</template>

<script setup name="PostConfig">
import { ref, reactive, onMounted, computed } from 'vue'
import { useRoute, useRouter } from 'vue-router'
import { ElMessage, ElMessageBox } from 'element-plus'
import { Search, Plus } from '@element-plus/icons-vue'
import { listAbility } from '@/api/training/ability'
import { getPostModelConfig, savePostModelConfig, delPostModelConfig } from '@/api/training/postConfig'
import AbilityDialog from '@/components/AbilityDialog/index.vue'

const route = useRoute()
const router = useRouter()

// 页面数据
const postId = ref(route.query.postId)
const postName = ref(route.query.postName)

// 获取岗位模型配置
const getPostConfig = async () => {
  if (postId.value) {
    try {
      const response = await getPostModelConfig(postId.value)
      if (response.data) {
        const config = response.data
        if (config.abilityConfigs && config.abilityConfigs.length > 0) {
          // 将后端数据转换为前端格式
          abilityList.value = config.abilityConfigs.map(abilityConfig => {
            const courseRows = abilityConfig.courseConfigs.map(course => {
              return {
                courseId: course.courseId,
                courseName: course.courseName,
                courseObjective: course.courseObjective,
                weight: course.weight || 100
              }
            })
            
            return {
              abilityId: abilityConfig.abilityId,
              threshold: abilityConfig.threshold || 60,
              weight: abilityConfig.weight || 100,
              courseRows: courseRows
            }
          })
        } else {
          // 如果没有历史数据，使用默认的3个空职业能力
          initDefaultAbilities()
        }
      } else {
        // 如果没有数据，使用默认的3个空职业能力
        initDefaultAbilities()
      }
    } catch (error) {
      console.error('获取岗位模型配置失败:', error)
      // 出错时使用默认的3个空职业能力
      initDefaultAbilities()
    }
  } else {
    initDefaultAbilities()
  }
}

// 职业能力相关
const abilityOptions = ref([])
const abilityList = ref([])

// 课程选择相关
const courseDialogVisible = ref(false)
const activeCourseTab = ref('school')
const courseSearchKeyword = ref('')
const coursePageNum = ref(1)
const coursePageSize = ref(5)
const courseTotal = ref(0)
const currentAbilityIndex = ref(-1)
const currentCourseRowIndex = ref(-1)
const selectedCourses = ref([])

// 课程数据
const schoolCourses = ref([])
const enterpriseCourses = ref([])

// 职业能力新增弹框相关
const abilityDialogVisible = ref(false)

// 初始化默认职业能力
const initDefaultAbilities = () => {
  abilityList.value = [
    {
      abilityId: null,
      threshold: 60,
      weight: 100,
      courseRows: []
    },
    {
      abilityId: null,
      threshold: 60,
      weight: 100,
      courseRows: []
    },
    {
      abilityId: null,
      threshold: 60,
      weight: 100,
      courseRows: []
    }
  ]
}

// 获取职业能力列表
const getAbilityList = async () => {
  try {
    const response = await listAbility({ pageSize: 1000 })
    abilityOptions.value = response.rows || []
  } catch (error) {
    console.error('获取职业能力列表失败:', error)
    ElMessage.error('获取职业能力列表失败')
  }
}

// 获取学校课程列表（Mock数据）
const getSchoolCourseList = async () => {
  // 模拟API调用延迟
  await new Promise(resolve => setTimeout(resolve, 100))
  
  // 使用Mock数据 - 与学生信息管理-考试成绩保持一致
  schoolCourses.value = [
    { courseId: 1, courseName: '数据结构', courseObjective: '数据结构与算法基础' },
    { courseId: 2, courseName: 'Java程序设计', courseObjective: 'Java语言程序设计基础' },
    { courseId: 3, courseName: '数据库原理', courseObjective: '数据库系统原理与应用' },
    { courseId: 4, courseName: '计算机网络', courseObjective: '计算机网络基础与应用' },
    { courseId: 5, courseName: '软件工程', courseObjective: '软件工程理论与实践' },
    { courseId: 6, courseName: '操作系统', courseObjective: '操作系统原理与应用' },
    { courseId: 7, courseName: '算法设计与分析', courseObjective: '算法设计与分析基础' }
  ]
  courseTotal.value = 7
}

// 获取企业课程列表（Mock数据）
const getEnterpriseCourseList = async () => {
  // 模拟API调用延迟
  await new Promise(resolve => setTimeout(resolve, 100))
  
  // 使用Mock数据 - 与学生信息管理-考试成绩保持一致
  enterpriseCourses.value = [
    { courseId: 101, courseName: '企业项目实训', courseObjective: '企业级项目开发实训' },
    { courseId: 102, courseName: 'Spring Boot开发', courseObjective: 'Spring Boot框架开发实践' },
    { courseId: 103, courseName: 'Vue.js前端开发', courseObjective: 'Vue.js前端框架开发' },
    { courseId: 104, courseName: '微服务架构', courseObjective: '微服务架构设计与实现' },
    { courseId: 105, courseName: '云计算与部署', courseObjective: '云计算平台部署与管理' },
    { courseId: 106, courseName: '团队协作项目', courseObjective: '团队协作与项目管理' },
    { courseId: 107, courseName: '企业级开发规范', courseObjective: '企业级软件开发规范' }
  ]
  courseTotal.value = 7
}

// 返回按钮
const handleBack = () => {
  // 检查是否有未保存的修改
  const hasChanges = checkUnsavedChanges()
  if (hasChanges) {
    ElMessageBox.confirm(
      '当前存在未保存的内容,您希望?',
      '提示',
      {
        confirmButtonText: '保存并退出编辑',
        cancelButtonText: '放弃编辑',
        type: 'warning'
      }
    ).then(() => {
      handleSave()
    }).catch(() => {
      router.back()
    })
  } else {
    router.back()
  }
}

// 检查是否有未保存的修改
const checkUnsavedChanges = () => {
  // 这里可以添加具体的检查逻辑
  return true
}

// 保存按钮
const handleSave = async () => {
  // 验证数据
  if (!validateData()) {
    return
  }

  try {
    // 构建保存数据
    const saveData = {
      postId: postId.value,
      postName: postName.value,
      abilityConfigs: abilityList.value.map(ability => {
        const courseConfigs = ability.courseRows
          .filter(courseRow => courseRow.courseId && courseRow.courseName)
          .map(courseRow => {
            return {
              courseId: courseRow.courseId,
              courseName: courseRow.courseName,
              courseObjective: courseRow.courseObjective,
              courseType: courseRow.courseId > 100 ? 2 : 1,
              weight: courseRow.weight
            }
          })
        
        return {
          abilityId: ability.abilityId,
          abilityName: abilityOptions.value.find(opt => opt.abilityId === ability.abilityId)?.abilityName || '',
          threshold: ability.threshold,
          weight: ability.weight,
          courseConfigs: courseConfigs
        }
      })
    }
    
    await savePostModelConfig(saveData)
    ElMessage.success('保存成功')
    router.back()
  } catch (error) {
    console.error('保存失败:', error)
    ElMessage.error('保存失败')
  }
}

// 验证数据
const validateData = () => {
  // 检查职业能力数量
  if (abilityList.value.length < 3) {
    ElMessage.error('至少需要配置3项职业能力')
    return false
  }

  // 检查是否所有职业能力都已选择
  const unselectedAbilities = abilityList.value.filter(item => !item.abilityId)
  if (unselectedAbilities.length > 0) {
    ElMessage.error('请选择所有职业能力')
    return false
  }

  // 检查每个职业能力的课程配置
  for (let i = 0; i < abilityList.value.length; i++) {
    const ability = abilityList.value[i]
    
    // 检查是否有关联课程
    if (ability.courseRows.length === 0) {
      ElMessage.error(`第${i + 1}项职业能力未选择关联课程`)
      return false
    }
    
    // 检查关联课程行是否都已选择课程
    const emptyCourseRows = ability.courseRows.filter(row => !row.courseName)
    if (emptyCourseRows.length > 0) {
      ElMessage.error(`第${i + 1}项职业能力的关联课程行存在未选择的课程`)
      return false
    }
    
    // 检查关联课程行的总权重是否为100%
    if (ability.courseRows.length > 0) {
      const totalWeight = ability.courseRows.reduce((sum, row) => sum + row.weight, 0)
      if (totalWeight !== 100) {
        const abilityName = abilityOptions.value.find(opt => opt.abilityId === ability.abilityId)?.abilityName || `第${i + 1}项职业能力`
        ElMessage.error(`${abilityName} 关联课程权重不等于100%，请调整！`)
        return false
      }
    }
  }

  return true
}

// 新增职业能力
const addAbility = () => {
  if (abilityList.value.length >= 10) {
    ElMessage.warning('最多只能配置10项职业能力')
    return
  }

  abilityList.value.push({
    abilityId: null,
    threshold: 60,
    weight: 100,
    courseRows: []
  })
}

// 新增职业能力
const handleAddAbility = () => {
  if (abilityList.value.length >= 10) {
    ElMessage.warning('最多只能配置10项职业能力')
    return
  }

  // 打开职业能力新增弹框
  openAbilityDialog()
}

// 打开职业能力新增弹框
const openAbilityDialog = () => {
  abilityDialogVisible.value = true
}

// 关闭职业能力新增弹框
const closeAbilityDialog = () => {
  abilityDialogVisible.value = false
}

// 处理职业能力新增成功
const handleAbilitySuccess = async (newAbility) => {
  // 重新获取职业能力列表
  await getAbilityList()
  
  // 将新增的职业能力添加到当前配置中
  abilityList.value.push({
    abilityId: newAbility.abilityId,
    threshold: newAbility.thresholdValue || 60,
    weight: 100,
    courseRows: []
  })
}

// 删除职业能力
const removeAbility = (index) => {
  if (abilityList.value.length <= 3) {
    ElMessage.warning('至少需要保留3项职业能力')
    return
  }

  abilityList.value.splice(index, 1)
}

// 职业能力选择变化
const handleAbilityChange = (value, index) => {
  const ability = abilityList.value[index]
  const selectedAbility = abilityOptions.value.find(item => item.abilityId === value)
  
  if (selectedAbility) {
    // 自动填入达标阈值
    ability.threshold = selectedAbility.threshold || 60
  }
}

// 选择课程
const selectCourse = (abilityIndex) => {
  currentAbilityIndex.value = abilityIndex
  courseDialogVisible.value = true
  selectedCourses.value = []
}



// 课程搜索
const handleCourseSearch = () => {
  coursePageNum.value = 1
  if (activeCourseTab.value === 'school') {
    getSchoolCourseList()
  } else {
    getEnterpriseCourseList()
  }
}

// 重置课程搜索
const resetCourseSearch = () => {
  courseSearchKeyword.value = ''
  coursePageNum.value = 1
  if (activeCourseTab.value === 'school') {
    getSchoolCourseList()
  } else {
    getEnterpriseCourseList()
  }
}

// 课程标签页切换
const handleCourseTabChange = (tabName) => {
  activeCourseTab.value = tabName
  coursePageNum.value = 1
  if (tabName === 'school') {
    getSchoolCourseList()
  } else {
    getEnterpriseCourseList()
  }
}

// 选择单个课程
const selectSingleCourse = (course) => {
  if (currentAbilityIndex.value >= 0) {
    const ability = abilityList.value[currentAbilityIndex.value]
    
    // 如果是为关联课程行选择课程
    if (currentCourseRowIndex.value >= 0) {
      const courseRow = ability.courseRows[currentCourseRowIndex.value]
      courseRow.courseId = course.courseId
      courseRow.courseName = course.courseName
      courseRow.courseObjective = course.courseObjective
      
      courseDialogVisible.value = false
      currentCourseRowIndex.value = -1
      ElMessage.success('课程添加成功')
      return
    }
    
    // 检查是否已经添加过该课程
    const existingCourse = ability.courseRows.find(item => item.courseId === course.courseId)
    if (existingCourse) {
      ElMessage.warning('该课程已添加')
      return
    }

    // 添加课程到关联课程行列表
    ability.courseRows.push({
      courseId: course.courseId,
      courseName: course.courseName,
      courseObjective: course.courseObjective,
      weight: 100 // 默认权重为100%
    })

    courseDialogVisible.value = false
    ElMessage.success('课程添加成功')
  }
}

// 关闭课程选择弹框
const handleCourseDialogClose = () => {
  courseDialogVisible.value = false
  selectedCourses.value = []
}

// 添加关联课程行
const addCourseRow = (abilityIndex) => {
  const ability = abilityList.value[abilityIndex]
  ability.courseRows.push({
    courseId: null,
    courseName: '',
    courseObjective: '',
    weight: 100
  })
}

// 删除关联课程行
const removeCourseRow = (abilityIndex, courseIndex) => {
  abilityList.value[abilityIndex].courseRows.splice(courseIndex, 1)
}

// 为关联课程行选择课程
const selectCourseForRow = (abilityIndex, courseIndex) => {
  currentAbilityIndex.value = abilityIndex
  currentCourseRowIndex.value = courseIndex
  courseDialogVisible.value = true
  selectedCourses.value = []
}

// 课程权重控制已由 el-input-number 组件自动处理



// 清除课程行
const clearCourseRow = (abilityIndex, courseIndex) => {
  const courseRow = abilityList.value[abilityIndex].courseRows[courseIndex]
  courseRow.courseId = null
  courseRow.courseName = ''
  courseRow.courseObjective = ''
  courseRow.weight = 100
}





// 权重控制已由 el-input-number 组件自动处理



onMounted(() => {
  getAbilityList()
  getPostConfig() // 获取岗位模型配置
  getSchoolCourseList() // 默认加载学校课程
})
</script>

<style scoped>
.app-container {
  padding: 20px;
  background-color: #f5f5f5;
  min-height: 100vh;
}

.header-nav {
  display: flex;
  align-items: center;
  justify-content: space-between;
  background: white;
  padding: 16px 20px;
  border-radius: 8px;
  margin-bottom: 20px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.page-title {
  margin: 0;
  font-size: 18px;
  font-weight: 600;
  color: #333;
}

.post-info {
  background: white;
  padding: 20px;
  border-radius: 8px;
  margin-bottom: 20px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.post-info h3 {
  margin: 0;
  color: #333;
  font-size: 16px;
  font-weight: 600;
}

.ability-config-section {
  background: white;
  border-radius: 8px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
  margin-bottom: 20px;
}

.section-header {
  padding: 20px 20px 0;
  display: flex;
  align-items: center;
  gap: 12px;
}

.note {
  color: #666;
  font-size: 14px;
}

/* 卡片头部布局 */
.card-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  width: 100%;
}

.header-actions {
  display: flex;
  align-items: center;
  gap: 12px;
  width: 400px;
  z-index: 2;
}

.info-content {
  display: flex;
  align-items: center;
  justify-content: center;
  margin-left: -400px;
  flex: 1;
  font-size: 20px;
  font-weight: 600;
  z-index: 1;
  color: #333;
}

.info-label {
  margin-right: 8px;
}

.info-value {
  color: #409eff;
  font-weight: 700;
}

.ability-table {
  padding: 20px;
}

.table-header {
  display: grid;
  grid-template-columns: 20% 20% 20% 20% 20%;
  background: #f5f7fa;
  border: 1px solid #e9ecef;
  border-radius: 8px 8px 0 0;
  border-bottom: none;
}

.header-cell {
  background: #f5f7fa;
  padding: 12px 16px;
  font-weight: 600;
  color: #333;
  display: flex;
  align-items: center;
  justify-content: center;
}

.table-body {
  border: 1px solid #e9ecef;
  border-top: none;
  border-radius: 0 0 8px 8px;
}

/* 能力项组 - 使用2px粗线分割不同能力项 */
.ability-group {
  border-bottom: 2px solid #e9ecef;
  background: white;
}

/* 最后一个能力项不显示底部分割线 */
.ability-group:last-child {
  border-bottom: none;
}

/* 使用grid布局的能力项容器 */
.ability-wrapper {
  display: grid;
  grid-template-columns: 20% 20% 20% 20% 20%;
}

/* 能力项名称列 - 跨越所有课程行，垂直居中 */
.ability-name-column {
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 12px 16px;
  background: white;
}

/* 达标规则列 - 跨越所有课程行，垂直居中 */
.threshold-column {
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 12px 16px;
}

/* 课程区域 - 包含所有课程行，占据后三列 */
.courses-area {
  grid-column: 3 / -1;
  display: grid;
  grid-template-columns: 1fr 1fr 1fr;
}

/* 课程行 - 每行最小高度48px */
.course-row {
  display: grid;
  grid-template-columns: 1fr 1fr 1fr;
  grid-column: 1 / -1;
  min-height: 48px;
  border-bottom: 1px solid #f0f0f0;
}

/* 最后一个课程行不显示底部细线 */
.course-row:last-child {
  border-bottom: none;
}

/* 课程相关的单元格 */
.course-cell,
.weight-cell,
.action-cell {
  width: 100%;
  padding: 12px 16px;
  display: flex;
  align-items: center;
  justify-content: center;
  min-height: 48px;
}

/* .course-cell {
  justify-content: flex-start;
} */

.weight-cell {
  justify-content: center;
}

.action-cell {
  justify-content: center;
}

.threshold-control {
  display: flex;
  align-items: center;
  gap: 4px;
  flex-wrap: nowrap;
}

.course-row {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 4px 8px;
  font-size: 12px;
  margin-bottom: 4px;
}

.course-row:last-child {
  margin-bottom: 0;
}

.course-name {
  flex: 1;
  margin-right: 8px;
}



/* 权重控制样式已由 el-input-number 组件自动提供 */





.course-dialog-content {
  padding: 20px 0;
}

.search-bar {
  display: flex;
  gap: 12px;
  margin-bottom: 20px;
  align-items: center;
}

.course-table {
  border: 1px solid #e9ecef;
  border-radius: 8px;
  overflow: hidden;
  margin-bottom: 20px;
}

.course-table-header {
  display: grid;
  grid-template-columns: 1fr 2fr 100px;
  background: #fafafa;
  border-bottom: 1px solid #e9ecef;
}

.course-header-cell {
  padding: 12px 16px;
  font-weight: 600;
  color: #333;
  border-right: 1px solid #e9ecef;
}

.course-header-cell:last-child {
  border-right: none;
}

.course-table-body {
  background: white;
}

.course-table-row {
  display: grid;
  grid-template-columns: 1fr 2fr 100px;
  border-bottom: 1px solid #e9ecef;
  cursor: pointer;
  transition: background-color 0.2s;
}

.course-table-row:hover {
  background-color: #f8f9fa;
}

.course-table-row:last-child {
  border-bottom: none;
}

.course-table-cell {
  padding: 12px 16px;
  display: flex;
  align-items: center;
  border-right: 1px solid #e9ecef;
}

.course-table-cell:last-child {
  border-right: none;
}

.pagination-container {
  margin-top: 20px;
  display: flex;
  justify-content: center;
}



/* 弹框样式优化 */
.el-dialog {
  border-radius: 8px;
}

.el-dialog__header {
  border-bottom: 1px solid #e9ecef;
  padding: 20px 20px 15px;
}

.el-dialog__body {
  padding: 20px;
}

.el-dialog__footer {
  border-top: 1px solid #e9ecef;
  padding: 15px 20px;
  }
</style> 