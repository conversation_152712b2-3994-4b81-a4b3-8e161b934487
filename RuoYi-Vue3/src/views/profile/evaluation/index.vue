<template>
  <div class="app-container">
    <div class="evaluation-container">
      <!-- 左侧题库管理 -->
      <div class="left-panel">
        <div class="panel-header">
          <h3>评价模型题库</h3>
          <el-button type="primary" @click="handleAddBank" size="small">
              <el-icon><Plus /></el-icon>
            </el-button>
        </div>
        <div class="panel-header">
          <el-input
              v-model="bankSearchKeyword"
              placeholder="请输入关键字"
              clearable
              @input="handleBankSearch"
              style="width: 100%"
            />
      </div>
    
        <div class="bank-list">
          <div
            v-for="bank in filteredBankList"
            :key="bank.bankId"
            class="bank-item"
            :class="{ active: selectedBankId === bank.bankId }"
            @click="selectBank(bank)"
          >
            <span class="bank-name">{{ bank.bankName }}</span>
            <div class="bank-actions">
              <el-button type="text" size="middle" @click.stop="handleEditBank(bank)">
                <el-icon><Edit /></el-icon>
              </el-button>
              <el-button type="text" size="middle" @click.stop="handleDeleteBank(bank)">
                <el-icon><Delete /></el-icon>
              </el-button>
            </div>
          </div>
        </div>
      </div>

      <!-- 右侧题目列表 -->
      <div class="right-panel">
        <div class="panel-header">
          <div class="search-section">
            <span class="mr-8">题干:</span>
            <el-input
              v-model="questionSearchKeyword"
              placeholder="请输入"
              clearable
              @input="handleQuestionSearch"
              style="width: 300px; margin-right: 8px;"
            >
            </el-input>
            <el-button type="primary" @click="handleQuery">查询</el-button>
            <el-button @click="resetQuery">重置</el-button>
          </div>
        </div>
        <el-button class="mb-8 " style="width: 100px;" type="primary" @click="handleAddQuestion">新增</el-button>

        <el-table
          v-loading="loading"
          :data="questionList"
          style="width: 100%"
          @selection-change="handleSelectionChange"
        >
          <el-table-column type="selection" width="55" align="center" />
          <el-table-column label="#" type="index" width="50" align="center" />
          <el-table-column label="题干" prop="questionStem" show-overflow-tooltip>
            <template #default="scope">
              <el-tooltip :content="scope.row.questionStem" placement="top">
                <span>{{ scope.row.questionStem }}</span>
              </el-tooltip>
            </template>
          </el-table-column>
          <el-table-column label="题目类型" prop="questionType" align="center" width="100">
            <template #default="scope">
              <dict-tag :options="questionTypeOptions" :value="scope.row.questionType"/>
            </template>
          </el-table-column>
          <el-table-column label="选项" prop="options" show-overflow-tooltip>
            <template #default="scope">
              <span>{{ formatOptions(scope.row.options) }}</span>
            </template>
          </el-table-column>
          <el-table-column label="职业能力" prop="abilityName" align="center" width="150">
            <template #default="scope">
              <span v-if="scope.row.abilityName">{{ scope.row.postName }} - {{ scope.row.abilityName }}</span>
              <span v-else class="text-muted">未设置</span>
            </template>
          </el-table-column>
          <el-table-column label="操作" align="center" width="200">
            <template #default="scope">
              <el-button
                type="text"
                size="small"
                @click="handleToggleStatus(scope.row)"
              >
                {{ scope.row.status === '0' ? '禁用' : '启用' }}
              </el-button>
              <el-button
                type="text"
                size="small"
                @click="handleEditQuestion(scope.row)"
              >
                编辑
              </el-button>
              <el-button
                type="text"
                size="small"
                @click="handleDeleteQuestion(scope.row)"
              >
                删除
              </el-button>
            </template>
          </el-table-column>
        </el-table>

        <pagination
          v-show="total > 0"
          :total="total"
          :page="queryParams.pageNum"
          :limit="queryParams.pageSize"
          @pagination="handlePagination"
        />
      </div>
    </div>

    <!-- 题库对话框 -->
    <BankDialog ref="bankDialogRef" @refresh="getBankList" />

    <!-- 题目对话框 -->
    <QuestionDialog ref="questionDialogRef" :bank-id="selectedBankId" @refresh="getList" />
  </div>
</template>

<script setup name="EvaluationModel">
import { listBank, delBank } from "@/api/training/bank";
import { listQuestion, delQuestion, updateQuestionStatus } from "@/api/training/question";
import BankDialog from "@/components/BankDialog/index.vue";
import QuestionDialog from "@/components/QuestionDialog/index.vue";
import { Plus, Edit, Delete } from '@element-plus/icons-vue';

const { proxy } = getCurrentInstance();

// 响应式数据
const loading = ref(true);
const total = ref(0);
const questionList = ref([]);
const bankList = ref([]);
const selectedBankId = ref(null);
const bankSearchKeyword = ref('');
const questionSearchKeyword = ref('');

// 查询参数
const queryParams = ref({
  pageNum: 1,
  pageSize: 5,
  bankId: null,
  questionStem: null,
  questionType: null,
  status: null
});

// 题目类型选项
const questionTypeOptions = ref([
  { label: '单选题', value: '0' },
  { label: '多选题', value: '1' }
]);

// 组件引用
const bankDialogRef = ref();
const questionDialogRef = ref();

// 计算属性
const filteredBankList = computed(() => {
  if (!bankSearchKeyword.value) {
    return bankList.value;
  }
  return bankList.value.filter(bank => 
    bank.bankName.includes(bankSearchKeyword.value)
  );
});

// 方法
const getBankList = () => {
  listBank({}).then(response => {
    bankList.value = response.data;
    if (bankList.value.length > 0 && !selectedBankId.value) {
      selectBank(bankList.value[0]);
    }
  });
};

const getList = () => {
  loading.value = true;
  listQuestion(queryParams.value).then(response => {
    questionList.value = response.rows;
    total.value = response.total;
    loading.value = false;
  });
};

const selectBank = (bank) => {
  selectedBankId.value = bank.bankId;
  queryParams.value.bankId = bank.bankId;
  getList();
};

const handleBankSearch = () => {
  // 搜索功能已通过计算属性实现
};

const handleQuestionSearch = () => {
  queryParams.value.questionStem = questionSearchKeyword.value;
};

const handleQuery = () => {
  queryParams.value.pageNum = 1;
  getList();
};

const resetQuery = () => {
  questionSearchKeyword.value = '';
  queryParams.value.questionStem = null;
  handleQuery();
};

const handleAddBank = () => {
  bankDialogRef.value.handleAdd();
};

const handleEditBank = (bank) => {
  bankDialogRef.value.handleUpdate(bank);
};

const handleDeleteBank = (bank) => {
  proxy.$modal.confirm('是否确认删除题库"' + bank.bankName + '"？同时将删除题库内所有题目数据，且无法恢复，请谨慎操作').then(() => {
    return delBank(bank.bankId);
  }).then(() => {
    getBankList();
    proxy.$modal.msgSuccess("删除成功");
  }).catch(() => {});
};

const handleAddQuestion = () => {
  if (!selectedBankId.value) {
    proxy.$modal.msgError("请先选择一个题库");
    return;
  }
  questionDialogRef.value.handleAdd();
};

const handleEditQuestion = (row) => {
  questionDialogRef.value.handleUpdate(row);
};

const handleDeleteQuestion = (row) => {
  proxy.$modal.confirm('是否确认删除当前题目？注：删除后无法恢复，请谨慎操作').then(() => {
    return delQuestion(row.questionId);
  }).then(() => {
    getList();
    proxy.$modal.msgSuccess("删除成功");
  }).catch(() => {});
};

const handleToggleStatus = (row) => {
  const action = row.status === '0' ? '禁用' : '启用';
  const newStatus = row.status === '0' ? '1' : '0';
  proxy.$modal.confirm(`是否确定${action}？`).then(() => {
    const data = {
      questionId: row.questionId,
      status: newStatus
    };
    updateQuestionStatus(data).then(() => {
      proxy.$modal.msgSuccess(`${action}成功`);
      getList();
    });
  }).catch(() => {});
};

const handleSelectionChange = (selection) => {
  // 处理多选
};

const handlePagination = (pagination) => {
  queryParams.value.pageNum = pagination.page;
  queryParams.value.pageSize = pagination.limit;
  getList();
};

const formatOptions = (options) => {
  try {
    const optionsArray = JSON.parse(options);
    return optionsArray.join('; ');
  } catch (e) {
    return options;
  }
};

// 初始化
onMounted(() => {
  getBankList();
});
</script>

<style scoped>
.evaluation-container {
  display: flex;
  height: calc(100vh - 200px);
  gap: 20px;
}

.left-panel {
  width: 300px;
  background: #fff;
  border: 1px solid #e4e7ed;
  border-radius: 4px;
  display: flex;
  flex-direction: column;
}

.right-panel {
  flex: 1;
  background: #fff;
  border: 1px solid #e4e7ed;
  border-radius: 4px;
  display: flex;
  flex-direction: column;
  padding: 20px;
  padding-bottom: 100px;
}

.panel-header {
  padding: 16px 20px;
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.panel-header h3 {
  margin: 0;
  font-size: 16px;
  font-weight: 600;
  color: #303133;
}

.search-add {
  display: flex;
  align-items: center;
}

.search-section {
  display: flex;
  align-items: center;
}

.bank-list {
  flex: 1;
  overflow-y: auto;
  padding: 8px 20px;
}

.bank-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 12px 16px;
  cursor: pointer;
  transition: background-color 0.3s;
}

.bank-item:hover {
  background-color: #f5f7fa;
}

.bank-item.active {
  background-color: #409eff;
  color: #fff;
}

.bank-item.active .bank-actions .el-button {
  color: #fff;
}

.bank-name {
  flex: 1;
  font-size: 14px;
}

.bank-actions {
  display: flex;
  gap: 4px;
  opacity: 0;
  transition: opacity 0.3s;
}

.bank-item:hover .bank-actions,
.bank-item.active .bank-actions {
  opacity: 1;
}

.bank-actions .el-button {
  padding: 2px;
  font-size: 12px;
}

.right-panel .panel-header {
  flex-direction: column;
  align-items: stretch;
  gap: 12px;
}


.el-table {
  flex: 1;
}

.pagination {
  padding: 16px;
  border-top: 1px solid #e4e7ed;
}

.text-muted {
  color: #909399;
  font-style: italic;
}
</style> 