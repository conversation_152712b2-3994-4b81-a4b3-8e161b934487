<template>
  <div class="score-summary">
    <div v-if="student && (schoolScores.length > 0 || enterpriseScores.length > 0)" class="summary-content">
      <!-- Tab切换 -->
      <div class="tab-container">
        <div 
          :class="['tab-item', { active: activeTab === 'school' }]"
          @click="switchTab('school')"
        >
          校内学科成绩
        </div>
        <div 
          :class="['tab-item', { active: activeTab === 'enterprise' }]"
          @click="switchTab('enterprise')"
        >
          实训项目成绩
        </div>
      </div>

      <!-- 校内学科成绩表格 -->
      <div v-if="activeTab === 'school'" class="score-table-container">
        <el-table 
          :data="schoolScores" 
          stripe 
          style="width: 100%"
          :header-cell-style="{ backgroundColor: '#f5f7fa', color: '#606266' }"
        >
          <el-table-column prop="courseName" label="课程名称" min-width="200" />
          <el-table-column prop="teacherName" label="授课教师" min-width="150" />
          <el-table-column prop="score" label="成绩" min-width="100" align="center">
            <template #default="scope">
              <span :class="getScoreClass(scope.row.score)">{{ scope.row.score }}</span>
            </template>
          </el-table-column>
        </el-table>
      </div>

      <!-- 实训项目成绩表格 -->
      <div v-if="activeTab === 'enterprise'" class="score-table-container">
        <el-table 
          :data="enterpriseScores" 
          stripe 
          style="width: 100%"
          :header-cell-style="{ backgroundColor: '#f5f7fa', color: '#606266' }"
        >
          <el-table-column prop="courseName" label="课程名称" min-width="200" />
          <el-table-column prop="mentorName" label="导师" min-width="150" />
          <el-table-column prop="score" label="成绩" min-width="100" align="center">
            <template #default="scope">
              <span :class="getScoreClass(scope.row.score)">{{ scope.row.score }}</span>
            </template>
          </el-table-column>
        </el-table>
      </div>
    </div>
    
    <div v-else-if="student && loading" class="summary-loading">
      <el-skeleton :rows="5" animated />
    </div>
    
    <div v-else-if="student" class="summary-empty">
      <el-empty description="暂无成绩数据" />
    </div>
    
    <div v-else class="no-selection">
      <el-empty description="请选择学生查看成绩汇总" />
    </div>
  </div>
</template>

<script>
import { ref, watch, onMounted } from 'vue'
import { listExamScoreByStudentId } from '@/api/student/examScore'
import { listCourse } from '@/api/training/course'
import { listUser } from '@/api/system/user'

export default {
  name: "ScoreSummary",
  props: {
    student: {
      type: Object,
      default: null
    }
  },
  setup(props) {
    const activeTab = ref('school')
    const schoolScores = ref([])
    const enterpriseScores = ref([])
    const loading = ref(false)
    const courseList = ref([])
    const teacherList = ref([])

    // 切换Tab
    const switchTab = (tab) => {
      activeTab.value = tab
    }

    // 获取成绩样式类
    const getScoreClass = (score) => {
      const numScore = parseFloat(score)
      if (numScore >= 90) return 'score-excellent'
      if (numScore >= 80) return 'score-good'
      if (numScore >= 70) return 'score-average'
      if (numScore >= 60) return 'score-pass'
      return 'score-fail'
    }

    // 加载课程列表
    const loadCourseList = async () => {
      try {
        const response = await listCourse({
          pageNum: 1,
          pageSize: 9999
        })
        courseList.value = response.rows || []
      } catch (error) {
        console.error('加载课程列表失败:', error)
      }
    }

    // 加载教师列表
    const loadTeacherList = async () => {
      try {
        const response = await listUser({
          pageNum: 1,
          pageSize: 9999
        })
        teacherList.value = response.rows || []
      } catch (error) {
        console.error('加载教师列表失败:', error)
      }
    }

    // 根据课程名称获取课程信息
    const getCourseInfo = (courseName) => {
      return courseList.value.find(course => course.courseName === courseName) || {}
    }

    // 根据用户ID获取教师信息
    const getTeacherInfo = (userId) => {
      return teacherList.value.find(user => user.userId === userId) || {}
    }

    // 加载学生成绩数据
    const loadScoreData = async (student) => {
      if (!student) return

      loading.value = true
      schoolScores.value = []
      enterpriseScores.value = []

      try {
        // 获取学生的所有成绩
        const response = await listExamScoreByStudentId(student.studentId)
        const allScores = response.data || []

        // 分类处理成绩数据
        allScores.forEach(scoreRecord => {
          const courseInfo = getCourseInfo(scoreRecord.courseName)
          
          // 根据课程类型分类
          if (courseInfo.courseType === '0') {
            // 校内学科成绩
            const teacherInfo = getTeacherInfo(courseInfo.teacherId)
            schoolScores.value.push({
              courseName: scoreRecord.courseName,
              teacherName: teacherInfo.nickName || teacherInfo.userName || '未知',
              score: scoreRecord.score,
              examDate: scoreRecord.examDate
            })
          } else if (courseInfo.courseType === '1') {
            // 实训项目成绩
            const mentorInfo = getTeacherInfo(courseInfo.mentorId || courseInfo.teacherId)
            enterpriseScores.value.push({
              courseName: scoreRecord.courseName,
              mentorName: mentorInfo.nickName || mentorInfo.userName || '未知',
              score: scoreRecord.score,
              examDate: scoreRecord.examDate
            })
          }
        })

        // 如果没有校内成绩但有企业成绩，默认显示企业成绩
        if (schoolScores.value.length === 0 && enterpriseScores.value.length > 0) {
          activeTab.value = 'enterprise'
        } else {
          activeTab.value = 'school'
        }

      } catch (error) {
        console.error('加载成绩数据失败:', error)
      } finally {
        loading.value = false
      }
    }

    // 监听学生变化
    watch(() => props.student, (newStudent) => {
      if (newStudent) {
        loadScoreData(newStudent)
      } else {
        schoolScores.value = []
        enterpriseScores.value = []
        activeTab.value = 'school'
      }
    }, { immediate: true })

    // 组件挂载时加载基础数据
    onMounted(() => {
      loadCourseList()
      loadTeacherList()
    })

    return {
      activeTab,
      schoolScores,
      enterpriseScores,
      loading,
      switchTab,
      getScoreClass
    }
  }
}
</script>

<style scoped>
.score-summary {
  background: white;
  border-radius: 8px;
  padding: 20px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.summary-content {
  display: flex;
  flex-direction: column;
  gap: 20px;
}

.tab-container {
  display: flex;
  border-bottom: 1px solid #e4e7ed;
}

.tab-item {
  padding: 12px 24px;
  cursor: pointer;
  font-size: 14px;
  color: #606266;
  border-bottom: 2px solid transparent;
  transition: all 0.3s;
}

.tab-item:hover {
  color: #409eff;
}

.tab-item.active {
  color: #409eff;
  border-bottom-color: #409eff;
  font-weight: 500;
}

.score-table-container {
  min-height: 200px;
}

/* 成绩颜色样式 */
.score-excellent {
  color: #67c23a;
  font-weight: 600;
}

.score-good {
  color: #409eff;
  font-weight: 600;
}

.score-average {
  color: #e6a23c;
  font-weight: 600;
}

.score-pass {
  color: #909399;
  font-weight: 600;
}

.score-fail {
  color: #f56c6c;
  font-weight: 600;
}

.summary-loading,
.summary-empty,
.no-selection {
  display: flex;
  align-items: center;
  justify-content: center;
  height: 200px;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .tab-container {
    flex-direction: column;
  }
  
  .tab-item {
    text-align: center;
    border-bottom: 1px solid #e4e7ed;
    border-right: none;
  }
  
  .tab-item.active {
    border-bottom-color: #409eff;
  }
}
</style>
