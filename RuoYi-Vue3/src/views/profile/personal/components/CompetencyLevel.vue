<template>
  <div class="competency-level">
    <h3>岗位能力达标程度</h3>
    <div v-if="student && competencyData" class="competency-content">
      <div class="chart-section">
        <div class="chart-container">
          <div ref="chartRef" class="echarts-chart"></div>
        </div>
        <!-- <div class="chart-info">
          <p class="post-name">目标岗位: {{ competencyData.postName }}</p>
          <p class="achievement-summary">达标能力: {{ competencyData.achievedCount }}/{{ competencyData.totalCount }}</p>
        </div> -->
      </div>

      <!-- 详细能力列表 -->
      <!-- <div class="ability-details">
        <h4>职业能力详情</h4>
        <div class="ability-list">
          <div
            v-for="ability in competencyData.abilityResults"
            :key="ability.abilityId"
            class="ability-item"
          >
            <div class="ability-header">
              <div class="ability-name">
                <el-icon :class="ability.isAchieved ? 'success-icon' : 'fail-icon'">
                  <Check v-if="ability.isAchieved" />
                  <Close v-else />
                </el-icon>
                <span>{{ ability.abilityName }}</span>
              </div>
              <div class="ability-score">
                <span class="score">{{ ability.score }}分</span>
                <span class="threshold">(阈值: {{ ability.threshold }}分)</span>
              </div>
            </div>

            <div v-if="ability.courseDetails && ability.courseDetails.length > 0" class="course-details">
              <div
                v-for="course in ability.courseDetails"
                :key="course.courseName"
                class="course-item"
              >
                <span class="course-name">{{ course.courseName }}</span>
                <span class="course-weight">权重: {{ course.weight }}%</span>
                <span class="course-score">平均分: {{ course.avgScore }}分</span>
                <span class="score-count">({{ course.scoreCount }}次成绩)</span>
              </div>
            </div>
          </div>
        </div>
      </div> -->
    </div>
    
    <div v-else-if="student && loading" class="competency-loading">
      <el-skeleton :rows="3" animated />
    </div>
    
    <div v-else-if="student" class="competency-empty">
      <el-empty description="暂无岗位能力数据" />
    </div>
    
    <div v-else class="no-selection">
      <el-empty description="请选择学生查看能力数据" />
    </div>
  </div>
</template>

<script>
import { ref, computed, watch, nextTick, onMounted, onUnmounted } from 'vue'
import { listPost } from '@/api/training/post'
import { getPostModelConfig } from '@/api/training/postConfig'
import { listExamScoreByStudentId } from '@/api/student/examScore'
import { Check, Close } from '@element-plus/icons-vue'
import * as echarts from 'echarts'

export default {
  name: "CompetencyLevel",
  components: {
    Check,
    Close
  },
  props: {
    student: {
      type: Object,
      default: null
    }
  },
  setup(props) {
    const competencyData = ref(null)
    const loading = ref(false)
    const postList = ref([])
    const chartRef = ref(null)
    let chartInstance = null

    // 计算属性
    const circumference = computed(() => 2 * Math.PI * 80)

    const strokeDashoffset = computed(() => {
      if (!competencyData.value) return circumference.value
      const percentage = competencyData.value.percentage / 100
      return circumference.value * (1 - percentage)
    })

    // 初始化ECharts图表
    const initChart = () => {
      if (!chartRef.value) {
        return
      }
      
      // 如果已经存在实例，先销毁
      if (chartInstance) {
        chartInstance.dispose()
      }
      
      chartInstance = echarts.init(chartRef.value)
      // 设置图表配置
      const option = {
        series: [
          {
            type: 'pie',
            radius: ['60%', '80%'],
            center: ['50%', '50%'],
            startAngle: 90,
            data: [
              {
                value: 0,
                name: '达标',
                itemStyle: {
                  color: '#5B9BD5'
                }
              },
              {
                value: 100,
                name: '未达标',
                itemStyle: {
                  color: '#7ECCC4'
                }
              }
            ],
            label: {
              show: false
            },
            labelLine: {
              show: false
            },
            emphasis: {
              scale: false
            }
          }
        ],
        graphic: [
          {
            type: 'text',
            left: 'center',
            top: 'center',
            style: {
              text: '0%',
              textAlign: 'center',
              fill: '#333',
              fontSize: 24,
              fontWeight: 600
            }
          }
        ]
      }
      
      chartInstance.setOption(option)
    }

    // 更新图表数据
    const updateChart = () => {
      if (!chartInstance || !competencyData.value) {
        return
      }
      
      const percentage = competencyData.value.percentage
      const option = {
        series: [
          {
            data: [
              {
                value: percentage,
                name: '达标',
                itemStyle: {
                  color: '#7ECCC4'
                }
              },
              {
                value: 100 - percentage,
                name: '未达标',
                itemStyle: {
                  color: '#5B9BD5'
                }
              }
            ]
          }
        ],
        graphic: [
          {
            type: 'text',
            left: 'center',
            top: 'center',
            style: {
              text: `${percentage}%`,
              textAlign: 'center',
              fill: '#333',
              fontSize: 24,
              fontWeight: 600
            }
          }
        ]
      }
      
      chartInstance.setOption(option)
    }

    // 销毁图表
    const destroyChart = () => {
      if (chartInstance) {
        chartInstance.dispose()
        chartInstance = null
      }
    }

    const loadPostList = async () => {
      try {
        const response = await listPost({})
        postList.value = response.rows || []
      } catch (error) {
        console.error('加载岗位列表失败:', error)
      }
    }

    /**
     * 计算课程的平均成绩
     * @param {Array} scores - 同一课程的所有成绩
     * @returns {number} 平均成绩
     */
    const calculateCourseAverage = (scores) => {
      if (!scores || scores.length === 0) return 0
      const sum = scores.reduce((total, score) => total + parseFloat(score.score || 0), 0)
      return sum / scores.length
    }

    /**
     * 计算职业能力分值
     * @param {Object} abilityConfig - 职业能力配置
     * @param {Array} studentScores - 学生成绩列表
     * @returns {number} 职业能力分值
     */
    const calculateAbilityScore = (abilityConfig, studentScores) => {
      if (!abilityConfig.courseConfigs || abilityConfig.courseConfigs.length === 0) {
        return 0
      }

      let totalWeightedScore = 0
      let totalWeight = 0

      // 遍历该职业能力关联的所有课程
      abilityConfig.courseConfigs.forEach(courseConfig => {
        // 找到学生该课程的所有成绩
        const courseScores = studentScores.filter(score =>
          score.courseName === courseConfig.courseName
        )

        if (courseScores.length > 0) {
          // 计算该课程的平均成绩
          const avgScore = calculateCourseAverage(courseScores)

          // 按权重累加
          const weight = courseConfig.weight || 100
          totalWeightedScore += avgScore * (weight / 100)
          totalWeight += (weight / 100)
        }
      })

      // 返回加权平均分
      return totalWeight > 0 ? totalWeightedScore / totalWeight : 0
    }

    const calculateCompetencyLevel = async (student) => {
      if (!student) return

      loading.value = true
      competencyData.value = null

      try {
        // 获取学生的岗位名称
        const postName = student.targetPosition || student.postName
        if (!postName) {
          competencyData.value = null
          return
        }

        // 根据岗位名称找到岗位ID
        const post = postList.value.find(p => p.postName === postName)
        if (!post) {
          competencyData.value = null
          return
        }

        // 获取岗位模型配置
        const configResponse = await getPostModelConfig(post.postId)
        const config = configResponse.data

        if (!config || !config.abilityConfigs) {
          competencyData.value = null
          return
        }

        // 获取学生的课程成绩
        const scoresResponse = await listExamScoreByStudentId(student.studentId)
        const studentScores = scoresResponse.data || []

        // 计算每个职业能力的达标情况
        const abilityResults = []
        let achievedCount = 0
        const totalCount = config.abilityConfigs.length

        config.abilityConfigs.forEach(abilityConfig => {
          // 计算该职业能力的分值
          const abilityScore = calculateAbilityScore(abilityConfig, studentScores)

          // 判断是否达标（计算出的分值 >= 设置的阈值）
          const threshold = abilityConfig.threshold || 60
          const isAchieved = abilityScore >= threshold

          if (isAchieved) {
            achievedCount++
          }

          abilityResults.push({
            abilityId: abilityConfig.abilityId,
            abilityName: abilityConfig.abilityName,
            score: Math.round(abilityScore * 100) / 100, // 保留两位小数
            threshold: threshold,
            isAchieved: isAchieved,
            courseDetails: abilityConfig.courseConfigs?.map(courseConfig => {
              const courseScores = studentScores.filter(score =>
                score.courseName === courseConfig.courseName
              )
              const avgScore = calculateCourseAverage(courseScores)
              return {
                courseName: courseConfig.courseName,
                weight: courseConfig.weight || 100,
                avgScore: Math.round(avgScore * 100) / 100,
                scoreCount: courseScores.length
              }
            }) || []
          })
        })

        const percentage = totalCount > 0 ? Math.round((achievedCount / totalCount) * 100) : 0

        competencyData.value = {
          totalCount,
          achievedCount,
          percentage,
          postName,
          abilityResults
        }

        // 更新图表
        await nextTick()
        // 强制重新初始化图表
        initChart()
        updateChart()
      } catch (error) {
        console.error('计算岗位能力达标程度失败:', error)
        competencyData.value = null
      } finally {
        loading.value = false
      }
    }

    // 监听学生变化
    watch(() => props.student, (newStudent) => {
      if (newStudent) {
        calculateCompetencyLevel(newStudent)
      } else {
        competencyData.value = null
        if (chartInstance) {
          // 重置图表为0%
          const option = {
            series: [
              {
                data: [
                  {
                    value: 0,
                    name: '达标',
                    itemStyle: {
                      color: '#7ECCC4'
                    }
                  },
                  {
                    value: 100,
                    name: '未达标',
                    itemStyle: {
                      color: '#5B9BD5'
                    }
                  }
                ]
              }
            ],
            graphic: [
              {
                type: 'text',
                left: 'center',
                top: 'center',
                style: {
                  text: '0%',
                  textAlign: 'center',
                  fill: '#333',
                  fontSize: 24,
                  fontWeight: 600
                }
              }
            ]
          }
          chartInstance.setOption(option)
        }
      }
    }, { immediate: true })

    // 监听窗口大小变化，重新调整图表大小
    const handleResize = () => {
      if (chartInstance) {
        chartInstance.resize()
      }
    }

    // 组件挂载时初始化图表
    onMounted(() => {
      loadPostList()
      window.addEventListener('resize', handleResize)
    })

    // 组件卸载时销毁图表
    onUnmounted(() => {
      destroyChart()
      window.removeEventListener('resize', handleResize)
    })

    return {
      competencyData,
      loading,
      circumference,
      strokeDashoffset,
      chartRef
    }
  }
}
</script>

<style scoped>
.competency-level h3 {
  margin: 0 0 16px 0;
  font-size: 16px;
  font-weight: 600;
  color: #333;
}

.competency-content {
  display: flex;
  flex-direction: column;
  margin-top: -30px;
}

.chart-section {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 16px;
  height: 250px;
}

.chart-container {
  position: relative;
  display: flex;
  justify-content: center;
  align-items: center;
  width: 100%;
  max-width: 250px;
  height: 250px;
  margin: 0 auto;
}

.echarts-chart {
  width: 100%;
  height: 100%;
}

.chart-info {
  text-align: center;
}

.chart-info p {
  margin: 4px 0;
  font-size: 14px;
}

.post-name {
  color: #333;
  font-weight: 500;
}

.achievement-summary {
  color: #666;
}

/* 能力详情部分 */
.ability-details {
  border-top: 1px solid #f0f0f0;
  padding-top: 20px;
}

.ability-details h4 {
  margin: 0 0 16px 0;
  font-size: 14px;
  font-weight: 600;
  color: #333;
}

.ability-list {
  display: flex;
  flex-direction: column;
  gap: 16px;
}

.ability-item {
  border: 1px solid #f0f0f0;
  border-radius: 8px;
  padding: 16px;
  background: #fafafa;
}

.ability-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 12px;
}

.ability-name {
  display: flex;
  align-items: center;
  gap: 8px;
  font-weight: 500;
  color: #333;
}

.success-icon {
  color: #67c23a;
}

.fail-icon {
  color: #f56c6c;
}

.ability-score {
  display: flex;
  align-items: center;
  gap: 8px;
}

.score {
  font-weight: 600;
  color: #333;
}

.threshold {
  font-size: 12px;
  color: #999;
}

/* 课程详情 */
.course-details {
  display: flex;
  flex-direction: column;
  gap: 8px;
  padding-left: 24px;
  border-left: 2px solid #e0e0e0;
}

.course-item {
  display: flex;
  align-items: center;
  gap: 12px;
  font-size: 12px;
  color: #666;
}

.course-name {
  font-weight: 500;
  color: #333;
  min-width: 80px;
}

.course-weight,
.course-score {
  color: #666;
}

.score-count {
  color: #999;
}

.competency-loading,
.competency-empty,
.no-selection {
  display: flex;
  align-items: center;
  justify-content: center;
  height: 200px;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .ability-header {
    flex-direction: column;
    align-items: flex-start;
    gap: 8px;
  }

  .course-item {
    flex-direction: column;
    align-items: flex-start;
    gap: 4px;
  }
}
</style>