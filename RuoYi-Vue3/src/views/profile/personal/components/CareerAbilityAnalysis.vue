<template>
  <div class="career-ability-analysis">
    <h3>职业能力分析</h3>
    <div v-if="student && radarData" class="analysis-content">
      <div class="radar-container">
        <div ref="radarChartRef" class="radar-chart"></div>
      </div>
    </div>
    
    <div v-else-if="student && loading" class="analysis-loading">
      <el-skeleton :rows="3" animated />
    </div>
    
    <div v-else-if="student" class="analysis-empty">
      <el-empty description="暂无职业能力数据" />
    </div>
    
    <div v-else class="no-selection">
      <el-empty description="请选择学生查看职业能力分析" />
    </div>
  </div>
</template>

<script>
import { ref, watch, nextTick, onMounted, onUnmounted } from 'vue'
import { listPost } from '@/api/training/post'
import { getPostModelConfig } from '@/api/training/postConfig'
import { listExamScoreByStudentId } from '@/api/student/examScore'
import * as echarts from 'echarts'

export default {
  name: "CareerAbilityAnalysis",
  props: {
    student: {
      type: Object,
      default: null
    }
  },
  setup(props) {
    const radarData = ref(null)
    const loading = ref(false)
    const postList = ref([])
    const radarChartRef = ref(null)
    let radarChartInstance = null

    // 初始化雷达图
    const initRadarChart = () => {
      if (!radarChartRef.value) {
        return
      }
      
      // 如果已经存在实例，先销毁
      if (radarChartInstance) {
        radarChartInstance.dispose()
      }
      
      radarChartInstance = echarts.init(radarChartRef.value)
      
      // 设置默认配置
      const option = {
        radar: {
          indicator: [
            { name: '职业能力1', max: 100 },
            { name: '职业能力2', max: 100 },
            { name: '职业能力3', max: 100 },
            { name: '职业能力4', max: 100 },
            { name: '职业能力5', max: 100 }
          ],
          radius: '65%',
          center: ['50%', '50%'],
          startAngle: 90,
          axisName: {
            fontSize: 16,
            color: '#333',
            fontWeight: 'normal'
          },
          splitNumber: 5,
          splitArea: {
            show: false
          },
          axisLine: {
            show: true,
            lineStyle: {
              color: '#E5E5E5',
              width: 1
            }
          },
          splitLine: {
            show: true,
            lineStyle: {
              color: '#E5E5E5',
              width: 1,
              type: 'solid'
            }
          },
          axisLabel: {
            show: false
          }
        },
        series: [{
          type: 'radar',
          data: [{
            value: [0, 0, 0, 0, 0],
            name: '职业能力',
            lineStyle: {
              color: '#4A90E2',
              width: 2
            },
            areaStyle: {
              color: 'transparent'
            },
            symbol: 'circle',
            symbolSize: 8,
            itemStyle: {
              color: '#4A90E2',
              borderColor: '#4A90E2',
              borderWidth: 2
            },
            label: {
              show: true,
              fontSize: 12,
              color: '#4A90E2',
              fontWeight: 'normal',
              position: 'outside',
              formatter: function(params) {
                return Math.round(params.value)
              }
            }
          }]
        }]
      }
      
      radarChartInstance.setOption(option)
    }

    // 更新雷达图数据
    const updateRadarChart = () => {
      if (!radarChartInstance || !radarData.value) {
        return
      }
      
      const { indicators, values } = radarData.value
      
      const option = {
        radar: {
          indicator: indicators.map(indicator => ({
            ...indicator,
            max: 100
          })),
          radius: '65%',
          center: ['50%', '50%'],
          startAngle: 90,
          axisName: {
            fontSize: 16,
            color: '#333',
            fontWeight: 'normal'
          },
          splitNumber: 5,
          splitArea: {
            show: false
          },
          axisLine: {
            show: true,
            lineStyle: {
              color: '#E5E5E5',
              width: 1
            }
          },
          splitLine: {
            show: true,
            lineStyle: {
              color: '#E5E5E5',
              width: 1,
              type: 'solid'
            }
          },
          axisLabel: {
            show: false
          }
        },
        series: [{
          type: 'radar',
          data: [{
            value: values,
            name: '职业能力',
            lineStyle: {
              color: '#4A90E2',
              width: 2
            },
            areaStyle: {
              color: 'transparent'
            },
            symbol: 'circle',
            symbolSize: 8,
            itemStyle: {
              color: '#4A90E2',
              borderColor: '#4A90E2',
              borderWidth: 2
            },
            label: {
              show: true,
              fontSize: 12,
              color: '#4A90E2',
              fontWeight: 'normal',
              position: 'outside',
              formatter: function(params) {
                return Math.round(params.value)
              }
            }
          }]
        }]
      }
      
      radarChartInstance.setOption(option)
    }

    // 销毁雷达图
    const destroyRadarChart = () => {
      if (radarChartInstance) {
        radarChartInstance.dispose()
        radarChartInstance = null
      }
    }

    // 加载岗位列表
    const loadPostList = async () => {
      try {
        const response = await listPost({})
        postList.value = response.rows || []
      } catch (error) {
        console.error('加载岗位列表失败:', error)
      }
    }

    /**
     * 计算课程的平均成绩
     */
    const calculateCourseAverage = (scores) => {
      if (!scores || scores.length === 0) return 0
      const sum = scores.reduce((total, score) => total + parseFloat(score.score || 0), 0)
      return sum / scores.length
    }

    /**
     * 计算职业能力分值
     */
    const calculateAbilityScore = (abilityConfig, studentScores) => {
      if (!abilityConfig.courseConfigs || abilityConfig.courseConfigs.length === 0) {
        return 0
      }

      let totalWeightedScore = 0
      let totalWeight = 0

      // 遍历该职业能力关联的所有课程
      abilityConfig.courseConfigs.forEach(courseConfig => {
        // 找到学生该课程的所有成绩
        const courseScores = studentScores.filter(score =>
          score.courseName === courseConfig.courseName
        )

        if (courseScores.length > 0) {
          // 计算该课程的平均成绩
          const avgScore = calculateCourseAverage(courseScores)

          // 按权重累加
          const weight = courseConfig.weight || 100
          totalWeightedScore += avgScore * (weight / 100)
          totalWeight += (weight / 100)
        }
      })

      // 返回加权平均分
      return totalWeight > 0 ? totalWeightedScore / totalWeight : 0
    }

    // 计算职业能力分析数据
    const calculateCareerAbilityAnalysis = async (student) => {
      if (!student) return

      loading.value = true
      radarData.value = null

      try {
        // 获取学生的岗位名称
        const postName = student.targetPosition || student.postName
        if (!postName) {
          radarData.value = null
          return
        }

        // 根据岗位名称找到岗位ID
        const post = postList.value.find(p => p.postName === postName)
        if (!post) {
          radarData.value = null
          return
        }

        // 获取岗位模型配置
        const configResponse = await getPostModelConfig(post.postId)
        const config = configResponse.data

        if (!config || !config.abilityConfigs) {
          radarData.value = null
          return
        }

        // 获取学生的课程成绩
        const scoresResponse = await listExamScoreByStudentId(student.studentId)
        const studentScores = scoresResponse.data || []

        // 计算每个职业能力的分值
        const indicators = []
        const values = []

        config.abilityConfigs.forEach(abilityConfig => {
          // 计算该职业能力的分值
          const abilityScore = calculateAbilityScore(abilityConfig, studentScores)
          
          indicators.push({
            name: abilityConfig.abilityName,
            max: 100
          })
          
          values.push(Math.round(abilityScore * 100) / 100)
        })

        radarData.value = {
          indicators,
          values,
          postName
        }

        // 更新雷达图
        await nextTick()
        initRadarChart()
        updateRadarChart()
      } catch (error) {
        console.error('计算职业能力分析失败:', error)
        radarData.value = null
      } finally {
        loading.value = false
      }
    }

    // 监听学生变化
    watch(() => props.student, (newStudent) => {
      if (newStudent) {
        calculateCareerAbilityAnalysis(newStudent)
      } else {
        radarData.value = null
        if (radarChartInstance) {
          // 重置雷达图
          initRadarChart()
        }
      }
    }, { immediate: true })

    // 监听窗口大小变化
    const handleResize = () => {
      if (radarChartInstance) {
        radarChartInstance.resize()
      }
    }

    // 组件挂载时初始化
    onMounted(() => {
      loadPostList()
      window.addEventListener('resize', handleResize)
    })

    // 组件卸载时销毁
    onUnmounted(() => {
      destroyRadarChart()
      window.removeEventListener('resize', handleResize)
    })

    return {
      radarData,
      loading,
      radarChartRef
    }
  }
}
</script>

<style scoped>
.career-ability-analysis {
  background: white;
  border-radius: 8px;
  padding: 20px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.career-ability-analysis h3 {
  margin: 0 0 20px 0;
  color: #333;
  font-size: 18px;
  font-weight: 600;
}

.analysis-content {
  display: flex;
  justify-content: center;
  align-items: center;
  min-height: 400px;
}

.radar-container {
  width: 100%;
  max-width: 500px;
  height: 400px;
}

.radar-chart {
  width: 100%;
  height: 100%;
}

.analysis-loading,
.analysis-empty,
.no-selection {
  display: flex;
  align-items: center;
  justify-content: center;
  height: 300px;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .radar-container {
    max-width: 100%;
    height: 300px;
  }
  
  .analysis-content {
    min-height: 300px;
  }
}
</style>
