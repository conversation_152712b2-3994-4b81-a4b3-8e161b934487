<template>
  <div class="personal-profile">
    <h3>个人档案</h3>
    <div v-if="student" class="profile-content">
      <div class="profile-avatar">
        <el-avatar :size="120" :src="student.avatar || ''">
          {{ student.studentName?.charAt(0) }}
        </el-avatar>
      </div>

      <div class="profile-info">
        <h4 class="student-name">{{ student.studentName }}</h4>
        <div class="info-grid">
          <div class="info-row">
            <!-- <div class="info-item">
              <span class="label">年龄：</span>
              <span class="value">{{ calculateAge(student) }}岁</span>
            </div> -->
            <div class="info-item ">
              <span class="label">学号：</span>
              <span class="value">{{ student.studentNumber }}</span>
            </div>
            <div class="info-item">
              <span class="label">岗位：</span>
              <span class="value">{{ student.targetPosition || student.postName || '未设置' }}</span>
            </div>
          </div>
          <div class="info-row">
            <div class="info-item">
              <span class="label">性别：</span>
              <span class="value">{{ student.gender === '0' ? '男' : '女' }}</span>
            </div>
            <div class="info-item">
              <span class="label">班级：</span>
              <span class="value">{{ student.className }}</span>
            </div>
          </div>
          <!-- <div class="info-row">
        
          </div> -->
        </div>
      </div>
    </div>

    <div v-else class="no-selection">
      <el-empty description="请选择学生查看个人档案" />
    </div>
  </div>
</template>

<script>
export default {
  name: "PersonalProfile",
  props: {
    student: {
      type: Object,
      default: null
    }
  },
  setup() {
    const calculateAge = (student) => {
      // 由于学生信息中没有生日字段，使用默认年龄
      // 可以根据学号或其他信息推断年龄，这里使用默认值
      return 20
    }

    return {
      calculateAge
    }
  }
}
</script>

<style scoped>
.personal-profile h3 {
  margin: 0 0 16px 0;
  font-size: 16px;
  font-weight: 600;
  color: #333;
}

.profile-content {
  display: flex;
  align-items: flex-start;
  gap: 30px;
  padding: 20px;
  border-radius: 8px;
  height: 100%;
  
}

.profile-avatar {
  flex-shrink: 0;
}

.profile-info {
  flex: 1;
  min-width: 0;
}

.profile-info .student-name {
  margin: 0 0 20px 0;
  font-size: 24px;
  font-weight: 600;
  color: #333;
}

.info-grid {
  display: flex;
  flex-direction: column;
  gap: 16px;
}

.info-row {
  display: flex;
  gap: 40px;
}

.info-item {
  display: flex;
  align-items: center;
  gap: 8px;
  flex: 1;
}

.info-item.full-width {
  flex: none;
}

.info-item .label {
  color: #666;
  font-size: 16px;
  font-weight: 500;
  white-space: nowrap;
}

.info-item .value {
  color: #333;
  font-size: 16px;
  font-weight: 600;
}

.no-selection {
  display: flex;
  align-items: center;
  justify-content: center;
  height: 200px;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .profile-content {
    flex-direction: column;
    align-items: center;
    text-align: center;
  }

  .info-row {
    flex-direction: column;
    gap: 16px;
  }

  .info-item {
    justify-content: center;
  }
}
</style> 