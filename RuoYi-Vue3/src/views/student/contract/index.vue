<template>
  <div class="app-container">
    <el-form :model="queryParams" ref="queryRef" :inline="true" v-show="showSearch" label-width="68px">
      <el-form-item label="合同编号" prop="contractNumber">
        <el-input
          v-model="queryParams.contractNumber"
          placeholder="请输入合同编号"
          clearable
          @keyup.enter="handleQuery"
        />
      </el-form-item>
      <el-form-item label="合同名称" prop="contractName">
        <el-input
          v-model="queryParams.contractName"
          placeholder="请输入合同名称"
          clearable
          @keyup.enter="handleQuery"
        />
      </el-form-item>
      <el-form-item label="学生姓名" prop="studentName">
        <el-input
          v-model="queryParams.studentName"
          placeholder="请输入学生姓名"
          clearable
          @keyup.enter="handleQuery"
        />
      </el-form-item>
      <el-form-item label="企业名称" prop="companyName">
        <el-input
          v-model="queryParams.companyName"
          placeholder="请输入企业名称"
          clearable
          @keyup.enter="handleQuery"
        />
      </el-form-item>
      <el-form-item label="签约日期" style="width: 308px">
        <el-date-picker
          v-model="dateRange"
          value-format="YYYY-MM-DD"
          type="daterange"
          range-separator="-"
          start-placeholder="开始日期"
          end-placeholder="结束日期"
        ></el-date-picker>
      </el-form-item>
      <el-form-item>
        <el-button type="primary" icon="Search" @click="handleQuery">搜索</el-button>
        <el-button icon="Refresh" @click="resetQuery">重置</el-button>
      </el-form-item>
    </el-form>

    <el-row :gutter="10" class="mb8">
      <el-col :span="1.5">
        <el-button
          type="primary"
          plain
          icon="Plus"
          @click="handleAdd"
        >新增</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="danger"
          plain
          icon="Delete"
          :disabled="multiple"
          @click="handleDelete"
        >批量删除</el-button>
      </el-col>
      <right-toolbar :showSearch="showSearch" @update:showSearch="showSearch = $event" @queryTable="getList"></right-toolbar>
    </el-row>

    <el-table v-loading="loading" :data="contractList" @selection-change="handleSelectionChange">
      <el-table-column type="selection" width="55" align="center" />
      <el-table-column label="序号" align="center" prop="contractId" width="80" />
      <el-table-column label="合同编号" align="center" prop="contractNumber" width="120" />
      <el-table-column label="合同名称" align="center" prop="contractName" min-width="120" />
      <el-table-column label="学生姓名" align="center" prop="studentName" width="100" />
      <el-table-column label="学号" align="center" prop="studentNumber" width="120" />
      <el-table-column label="专业" align="center" prop="major" width="100" />
      <el-table-column label="校内教师" align="center" prop="schoolTeacher" width="100" />
      <el-table-column label="签约企业" align="center" prop="companyName" min-width="120" />
      <el-table-column label="企业导师" align="center" prop="companyTeacher" width="100" />
      <el-table-column label="岗位" align="center" prop="postName" width="100" />
      <el-table-column label="薪酬" align="center" prop="salary" width="100">
        <template #default="scope">
          <span v-if="scope.row.salary">{{ scope.row.salary }}元</span>
          <span v-else>-</span>
        </template>
      </el-table-column>
      <el-table-column label="签约日期" align="center" prop="signDate" width="100">
        <template #default="scope">
          <span>{{ parseTime(scope.row.signDate, '{y}-{m}-{d}') }}</span>
        </template>
      </el-table-column>
      <el-table-column label="操作" align="center" class-name="small-padding fixed-width" width="180">
        <template #default="scope">
          <el-button link type="primary" @click="handleDetail(scope.row)">查看</el-button>
          <el-button link type="primary" @click="handleUpdate(scope.row)">编辑</el-button>
          <el-button link type="primary" @click="handleDelete(scope.row)">删除</el-button>
        </template>
      </el-table-column>
    </el-table>
    
    <pagination
      v-show="total>0"
      :total="total"
      :page="queryParams.pageNum"
      :limit="queryParams.pageSize"
      @update:page="queryParams.pageNum = $event"
      @update:limit="queryParams.pageSize = $event"
      @pagination="getList"
    />


  </div>
</template>

<script setup name="Contract">
import { ref, reactive, toRefs, onMounted, getCurrentInstance } from 'vue';
import { listContract, delContract } from "@/api/student/contract";
import { parseTime } from "@/utils/ruoyi";

const { proxy } = getCurrentInstance();

const contractList = ref([]);
const loading = ref(false);
const showSearch = ref(true);
const ids = ref([]);
const single = ref(true);
const multiple = ref(true);
const total = ref(0);
const dateRange = ref([]);

const data = reactive({
  queryParams: {
    pageNum: 1,
    pageSize: 10,
    contractNumber: null,
    contractName: null,
    studentName: null,
    companyName: null,
    beginSignDate: null,
    endSignDate: null
  }
});

const { queryParams } = toRefs(data);

/** 查询学生合同信息列表 */
function getList() {
  loading.value = true;
  queryParams.value.beginSignDate = null;
  queryParams.value.endSignDate = null;
  if (null != dateRange.value && '' != dateRange.value) {
    queryParams.value.beginSignDate = dateRange.value[0];
    queryParams.value.endSignDate = dateRange.value[1];
  }
  listContract(queryParams.value).then(response => {
    contractList.value = response.rows;
    total.value = response.total;
    loading.value = false;
  }).catch(error => {
    console.error('获取合同列表失败:', error);
    loading.value = false;
  });
}

/** 搜索按钮操作 */
function handleQuery() {
  queryParams.value.pageNum = 1;
  getList();
}

/** 重置按钮操作 */
function resetQuery() {
  dateRange.value = [];
  proxy.resetForm("queryRef");
  handleQuery();
}

// 多选框选中数据
function handleSelectionChange(selection) {
  ids.value = selection.map(item => item.contractId);
  single.value = selection.length != 1;
  multiple.value = !selection.length;
}

/** 新增按钮操作 */
function handleAdd() {
  proxy.$tab.openPage("新增合同", "/contract-info/add");
}

/** 修改按钮操作 */
function handleUpdate(row) {
  proxy.$tab.openPage("编辑合同", `/contract-info/edit/${row.contractId}`);
}

/** 查看详情按钮操作 */
function handleDetail(row) {
  proxy.$tab.openPage("合同详情", `/contract-info/detail/${row.contractId}`);
}

/** 删除按钮操作 */
function handleDelete(row) {
  const contractIds = row.contractId || ids.value;
  proxy.$modal.confirm('是否确认删除学生合同信息编号为"' + contractIds + '"的数据项？').then(function() {
    return delContract(contractIds);
  }).then(() => {
    getList();
    proxy.$modal.msgSuccess("删除成功");
  }).catch(() => {});
}





onMounted(() => {
  getList();
});
</script> 