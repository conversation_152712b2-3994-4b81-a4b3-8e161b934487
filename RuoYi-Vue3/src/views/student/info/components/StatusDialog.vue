<template>
  <el-dialog 
    v-model="dialogVisible" 
    :title="title" 
    width="600px" 
    append-to-body
    :close-on-click-modal="false"
  >
    <el-form ref="formRef" :model="form" :rules="rules" label-width="120px">
      <el-form-item label="学生状态" prop="studentStatus" required>
        <el-select v-model="form.studentStatus" placeholder="请选择状态" style="width: 100%">
          <el-option label="未面试" value="0" />
          <el-option label="面试失败" value="1" />
          <el-option label="成功签约" value="2" />
        </el-select>
      </el-form-item>
      
      <!-- 关联合同部分，仅在选择"成功签约"时显示 -->
      <div v-if="form.studentStatus === '2'">
        <div style="margin-bottom: 16px;">
          <span style="font-weight: 500; color: #606266;">关联合同:</span>
          <el-icon style="color: #409EFF; margin-left: 4px;"><InfoFilled /></el-icon>
          <span style="color: #909399; margin-left: 4px;">未创建合同？</span>
          <el-button 
            type="primary" 
            link 
            @click="handleCreateContract"
            style="margin-left: 4px;"
          >
            立即创建
          </el-button>
        </div>
        
        <div 
          v-if="!selectedContract" 
          class="contract-select-box"
          @click="handleSelectContract"
        >
          <el-icon style="color: #409EFF; font-size: 20px;"><Plus /></el-icon>
          <span style="color: #409EFF; margin-left: 8px;">选择合同</span>
        </div>
        
        <div v-else class="contract-display-box">
          <div class="contract-info">
            <div class="contract-item">
              <span class="label">合同名称：</span>
              <span class="value">{{ selectedContract.contractName }}</span>
            </div>
            <div class="contract-item">
              <span class="label">合同编号：</span>
              <span class="value">{{ selectedContract.contractNumber }}</span>
            </div>
            <div class="contract-item">
              <span class="label">签约企业名称：</span>
              <span class="value">{{ selectedContract.companyName }}</span>
            </div>
            <div class="contract-item">
              <span class="label">签订时间：</span>
              <span class="value">{{ selectedContract.signDate }}</span>
            </div>
          </div>
          <el-button type="primary" link @click="handleSelectContract">重选</el-button>
        </div>
      </div>
    </el-form>
    
    <template #footer>
      <div class="dialog-footer">
        <el-button @click="handleCancel">取消</el-button>
        <el-button type="primary" @click="handleConfirm" :loading="loading">确认</el-button>
      </div>
    </template>

    <!-- 选择合同对话框 -->
    <el-dialog 
      v-model="contractSelectDialogVisible" 
      title="选择合同" 
      width="800px" 
      append-to-body
      :close-on-click-modal="false"
    >
      <el-form :model="contractQueryParams" :inline="true" style="margin-bottom: 16px;">
        <el-form-item label="合同编号">
          <el-input v-model="contractQueryParams.contractNumber" placeholder="请输入合同编号" clearable />
        </el-form-item>
        <el-form-item label="合同名称">
          <el-input v-model="contractQueryParams.contractName" placeholder="请输入合同名称" clearable />
        </el-form-item>
        <el-form-item label="企业名称">
          <el-input v-model="contractQueryParams.companyName" placeholder="请输入企业名称" clearable />
        </el-form-item>
        <el-form-item>
          <el-button type="primary" @click="handleContractQuery">搜索</el-button>
          <el-button @click="handleContractReset">重置</el-button>
        </el-form-item>
      </el-form>
      
      <el-table 
        v-loading="contractLoading" 
        :data="contractList" 
        @row-click="handleContractSelect"
        style="cursor: pointer;"
      >
        <el-table-column label="合同编号" prop="contractNumber" />
        <el-table-column label="合同名称" prop="contractName" />
        <el-table-column label="企业名称" prop="companyName" />
        <el-table-column label="签约日期" prop="signDate" />
        <el-table-column label="操作" width="100">
          <template #default="scope">
            <el-button type="primary" link @click="handleContractSelect(scope.row)">选择</el-button>
          </template>
        </el-table-column>
      </el-table>
      
      <pagination
        v-show="contractTotal > 0"
        :total="contractTotal"
        :page="contractQueryParams.pageNum"
        :limit="contractQueryParams.pageSize"
        @pagination="getContractList"
      />
    </el-dialog>
  </el-dialog>
</template>

<script setup>
import { ref, computed, watch } from 'vue'
import { listContract } from "@/api/student/contract"
import { InfoFilled, Plus } from '@element-plus/icons-vue'

const props = defineProps({
  modelValue: {
    type: Boolean,
    default: false
  },
  mode: {
    type: String,
    default: 'interview', // 'interview' | 'modify'
    validator: (value) => ['interview', 'modify'].includes(value)
  },
  studentInfo: {
    type: Object,
    default: () => ({})
  }
})

const emit = defineEmits(['update:modelValue', 'success'])

// 对话框显示状态
const dialogVisible = computed({
  get: () => props.modelValue,
  set: (value) => emit('update:modelValue', value)
})

// 标题
const title = computed(() => {
  return props.mode === 'interview' ? '面试' : '修改状态'
})

// 表单数据
const formRef = ref()
const loading = ref(false)
const form = ref({
  studentStatus: '',
  contractId: null
})

// 表单验证规则
const rules = {
  studentStatus: [
    { required: true, message: "学生状态不能为空", trigger: "change" }
  ]
}

// 合同选择相关数据
const contractSelectDialogVisible = ref(false)
const contractLoading = ref(false)
const contractList = ref([])
const contractTotal = ref(0)
const selectedContract = ref(null)
const contractQueryParams = ref({
  pageNum: 1,
  pageSize: 10,
  contractNumber: '',
  contractName: '',
  companyName: ''
})

// 监听学生信息变化，初始化表单
watch(() => props.studentInfo, (newVal) => {
  if (newVal && Object.keys(newVal).length > 0) {
    form.value = {
      studentStatus: newVal.studentStatus || '',
      contractId: newVal.contractId || null
    }
    selectedContract.value = null
  }
}, { immediate: true })

// 获取合同列表
function getContractList() {
  contractLoading.value = true
  listContract(contractQueryParams.value).then(response => {
    contractList.value = response.rows
    contractTotal.value = response.total
    contractLoading.value = false
  }).catch(() => {
    contractLoading.value = false
  })
}

// 合同查询操作
function handleContractQuery() {
  contractQueryParams.value.pageNum = 1
  getContractList()
}

// 合同查询重置
function handleContractReset() {
  contractQueryParams.value = {
    pageNum: 1,
    pageSize: 10,
    contractNumber: '',
    contractName: '',
    companyName: ''
  }
  getContractList()
}

// 选择合同
function handleContractSelect(row) {
  selectedContract.value = row
  contractSelectDialogVisible.value = false
  form.value.contractId = row.contractId
}

// 选择合同操作
function handleSelectContract() {
  contractSelectDialogVisible.value = true
  getContractList()
}

// 创建合同操作
function handleCreateContract() {
  dialogVisible.value = false
  // 跳转到合同管理的新建页面
  window.open('/contract-info/add', '_blank')
}

// 确认操作
function handleConfirm() {
  formRef.value.validate(valid => {
    if (valid) {
      loading.value = true
      
      // 准备更新数据
      const updateData = {
        ...props.studentInfo,
        studentStatus: form.value.studentStatus,
        contractId: selectedContract.value ? selectedContract.value.contractId : null
      }
      
      // 触发成功事件
      emit('success', updateData)
      loading.value = false
    }
  })
}

// 取消操作
function handleCancel() {
  dialogVisible.value = false
  form.value = {
    studentStatus: '',
    contractId: null
  }
  selectedContract.value = null
}
</script>

<style scoped>
.contract-select-box {
  border: 2px dashed #d9d9d9;
  border-radius: 6px;
  padding: 40px 20px;
  text-align: center;
  cursor: pointer;
  transition: all 0.3s;
}

.contract-select-box:hover {
  border-color: #409EFF;
  background-color: #f0f9ff;
}

.contract-display-box {
  border: 1px solid #e6e6e6;
  border-radius: 6px;
  padding: 16px;
  background-color: #fafafa;
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
}

.contract-info {
  flex: 1;
}

.contract-item {
  margin-bottom: 8px;
  display: flex;
  align-items: center;
}

.contract-item:last-child {
  margin-bottom: 0;
}

.contract-item .label {
  font-weight: 500;
  color: #606266;
  min-width: 100px;
  margin-right: 8px;
}

.contract-item .value {
  color: #303133;
  flex: 1;
}

.dialog-footer {
  text-align: right;
}
</style> 