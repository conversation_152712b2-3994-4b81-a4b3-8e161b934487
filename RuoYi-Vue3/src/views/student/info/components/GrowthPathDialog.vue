<template>
  <el-dialog :title="title" v-model="dialogVisible" width="800px" append-to-body @close="handleClose">
    <!-- 基本信息 -->
    <StudentBasicInfo :student-info="studentInfo" />

    <!-- 成长路径信息 -->
    <div>
      <div class="info-title">
        <h4>成长路径信息</h4>
      </div>
      <el-button class="mb-4" type="primary" @click="handleAddGrowthPath">新增</el-button>
      
      <div class="table-container">
        <el-table :data="growthPathList" border class="responsive-table">
          <el-table-column label="序号" type="index" width="60" align="center" />
          <el-table-column label="学期" align="center" min-width="150">
            <template #default="scope">
              <el-select 
                v-if="scope.row.isEditing" 
                v-model="scope.row.semesterCode" 
                placeholder="请选择学期"
                filterable
                style="width: 100%"
              >
                <el-option
                  v-for="semester in semesterOptions"
                  :key="semester.semesterCode"
                  :label="semester.semesterCode"
                  :value="semester.semesterCode"
                />
              </el-select>
              <span v-else>{{ scope.row.semesterCode }}</span>
            </template>
          </el-table-column>
          <el-table-column label="学习情况描述" align="center" min-width="300">
            <template #default="scope">
              <el-input 
                v-if="scope.row.isEditing" 
                v-model="scope.row.learningSituation" 
                placeholder="请输入学习情况描述"
                type="textarea"
                :rows="2"
                style="width: 100%"
              />
              <span v-else>{{ scope.row.learningSituation }}</span>
            </template>
          </el-table-column>
          <el-table-column label="操作" align="center" width="120" fixed="right">
            <template #default="scope">
              <template v-if="scope.row.isEditing">
                <el-button link type="primary" @click="confirmGrowthPath(scope.row)">确认</el-button>
                <el-button link type="info" @click="cancelGrowthPath(scope.row)">取消</el-button>
              </template>
              <template v-else>
                <el-button link type="primary" @click="deleteGrowthPath(scope.row)">删除</el-button>
              </template>
            </template>
          </el-table-column>
        </el-table>
      </div>

      <!-- 分页 -->
      <pagination
        v-show="growthPathTotal > 0"
        :total="growthPathTotal"
        :page="queryParams.pageNum"
        :limit="queryParams.pageSize"
        @pagination="handlePagination"
      />
    </div>

  </el-dialog>
</template>

<script setup>
import { ref, computed, watch } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import { listGrowthPathByStudentId, addGrowthPath, delGrowthPath } from '@/api/student/growthPath'
import { listSemester } from '@/api/system/semester'
import StudentBasicInfo from './StudentBasicInfo.vue'

const props = defineProps({
  modelValue: {
    type: Boolean,
    default: false
  },
  studentInfo: {
    type: Object,
    default: () => ({})
  }
})

const emit = defineEmits(['update:modelValue', 'success'])

const dialogVisible = computed({
  get: () => props.modelValue,
  set: (val) => emit('update:modelValue', val)
})

const title = ref('成长路径')
const growthPathList = ref([])
const semesterOptions = ref([])
const growthPathTotal = ref(0)
const queryParams = ref({
  pageNum: 1,
  pageSize: 10
})

// 监听弹框显示
watch(() => props.modelValue, (val) => {
  if (val && props.studentInfo.studentId) {
    title.value = `${props.studentInfo.studentName}的成长路径`
    loadGrowthPathList()
    loadSemesterOptions()
  }
})

// 加载成长路径列表
const loadGrowthPathList = async () => {
  if (!props.studentInfo.studentId) return
  
  try {
    const response = await listGrowthPathByStudentId(props.studentInfo.studentId, queryParams.value)
    growthPathList.value = response.rows || []
    growthPathTotal.value = response.total || 0
  } catch (error) {
    console.error('获取成长路径列表失败:', error)
    ElMessage.error('获取成长路径列表失败')
  }
}

// 加载学期选项
const loadSemesterOptions = async () => {
  try {
    const response = await listSemester()
    semesterOptions.value = response.data || []
  } catch (error) {
    console.error('获取学期列表失败:', error)
    ElMessage.error('获取学期列表失败')
  }
}

// 新增成长路径
const handleAddGrowthPath = () => {
  const newPath = {
    studentId: props.studentInfo.studentId,
    semesterCode: '',
    learningSituation: '',
    isEditing: true,
    isNew: true
  }
  growthPathList.value.unshift(newPath)
}

// 确认成长路径
const confirmGrowthPath = async (row) => {
  if (!row.semesterCode) {
    ElMessage.warning('请选择学期')
    return
  }
  if (!row.learningSituation) {
    ElMessage.warning('请输入学习情况描述')
    return
  }

  try {
    if (row.isNew) {
      // 新增
      const pathData = {
        studentId: row.studentId,
        semesterCode: row.semesterCode,
        learningSituation: row.learningSituation
      }
      await addGrowthPath(pathData)
      ElMessage.success('新增成功')
    }
    
    row.isEditing = false
    row.isNew = false
    loadGrowthPathList()
    emit('success')
  } catch (error) {
    console.error('操作失败:', error)
    ElMessage.error('操作失败')
  }
}

// 取消成长路径编辑
const cancelGrowthPath = (row) => {
  if (row.isNew) {
    // 如果是新增的行，直接删除
    const index = growthPathList.value.findIndex(item => item === row)
    growthPathList.value.splice(index, 1)
  } else {
    // 如果是编辑的行，恢复原数据
    row.isEditing = false
    loadGrowthPathList()
  }
}

// 删除成长路径
const deleteGrowthPath = async (row) => {
  try {
    await ElMessageBox.confirm('是否确认删除该成长路径记录？', '警告', {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'warning'
    })
    
    await delGrowthPath(row.pathId)
    ElMessage.success('删除成功')
    loadGrowthPathList()
    emit('success')
  } catch (error) {
    if (error !== 'cancel') {
      console.error('删除失败:', error)
      ElMessage.error('删除失败')
    }
  }
}

// 分页处理
const handlePagination = (pagination) => {
  queryParams.value.pageNum = pagination.page
  queryParams.value.pageSize = pagination.limit
  loadGrowthPathList()
}

// 关闭弹框
const handleClose = () => {
  queryParams.value.pageNum = 1
  growthPathList.value = []
  growthPathTotal.value = 0
}
</script>

<style scoped>


.info-title {
  margin: 0 0 16px 0;
  padding-left: 8px;
  border-left: 4px solid #409eff;
  font-size: 16px;
  font-weight: 600;
  color: #303133;
}

.table-container {
  overflow-x: auto;
  min-height: 400px;
}

.responsive-table {
  min-width: 600px;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .responsive-table {
    font-size: 12px;
  }
  
  .table-container {
    padding: 0 10px;
  }
}

:deep(.el-dialog__body) {
  padding: 20px;
}

:deep(.el-table th) {
  background-color: #fafafa;
}

:deep(.el-dialog) {
  margin-top: 5vh !important;
}

@media (max-width: 768px) {
  :deep(.el-dialog) {
    width: 95% !important;
    margin: 5px auto !important;
  }
}
</style> 