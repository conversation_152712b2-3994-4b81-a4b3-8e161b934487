<template>
  <div class="basic-info">
    <h3 class="info-title">基本信息</h3>
    
    <!-- 使用el-form统一处理所有模式 -->
    <el-form ref="formRef" :model="form" :rules="rules" label-width="120px" class="student-form">
      <el-row :gutter="20">
        <el-col :span="12">
          <el-form-item label="学生姓名" prop="studentName" required>
            <el-input 
              v-model="form.studentName" 
              placeholder="请输入学生姓名" 
              :disabled="mode === 'view'"
            />
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="学号" prop="studentNumber" required>
            <el-input 
              v-model="form.studentNumber" 
              placeholder="请输入学号" 
              :disabled="mode === 'view'"
            />
          </el-form-item>
        </el-col>
      </el-row>

      <el-row :gutter="20">
        <el-col :span="12">
          <el-form-item label="班级" prop="className" required>
            <el-input 
              v-model="form.className" 
              placeholder="请输入班级" 
              :disabled="mode === 'view'"
            />
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="性别" prop="gender" required>
            <el-select 
              v-model="form.gender" 
              placeholder="请选择性别" 
              style="width: 100%"
              :disabled="mode === 'view'"
            >
              <el-option label="男" value="0" />
              <el-option label="女" value="1" />
            </el-select>
          </el-form-item>
        </el-col>
      </el-row>

      <el-row :gutter="20">
        <el-col :span="12">
          <el-form-item label="学生状态" prop="studentStatus" required>
            <el-select 
              v-model="form.studentStatus" 
              placeholder="请选择学生状态" 
              style="width: 100%"
              :disabled="mode === 'view'"
            >
              <el-option label="未面试" value="0" />
              <el-option label="面试失败" value="1" />
              <el-option label="成功签约" value="2" />
            </el-select>
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="专业" prop="major" required>
            <el-select
              v-model="form.major"
              placeholder="请选择专业"
              style="width: 100%"
              :disabled="mode === 'view'"
            >
              <el-option
                v-for="item in majorOptions"
                :key="item.value"
                :label="item.label"
                :value="item.value"
              />
            </el-select>
          </el-form-item>
        </el-col>
      </el-row>

      <el-row :gutter="20">
        <el-col :span="12">
          <el-form-item label="学校导师" prop="schoolTeacher" required>
            <el-select
              v-model="form.schoolTeacher"
              placeholder="请选择学校导师"
              style="width: 100%"
              :disabled="mode === 'view'"
            >
              <el-option
                v-for="item in schoolTeacherOptions"
                :key="item.value"
                :label="item.label"
                :value="item.value"
              />
            </el-select>
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="企业导师" prop="companyTeacher" required>
            <el-select
              v-model="form.companyTeacher"
              placeholder="请选择企业导师"
              style="width: 100%"
              :disabled="mode === 'view'"
            >
              <el-option
                v-for="item in companyTeacherOptions"
                :key="item.value"
                :label="item.label"
                :value="item.value"
              />
            </el-select>
          </el-form-item>
        </el-col>
      </el-row>

      <el-row :gutter="20">
        <el-col :span="12">
          <el-form-item label="目标岗位" prop="targetPosition" required>
            <el-select
              v-model="form.targetPosition"
              placeholder="请选择目标岗位"
              style="width: 100%"
              :disabled="mode === 'view'"
            >
              <el-option
                v-for="item in postOptions"
                :key="item.postId"
                :label="item.postName"
                :value="item.postName"
              />
            </el-select>
          </el-form-item>
        </el-col>
      </el-row>
    </el-form>

    <!-- 合同信息选择（编辑/新增模式下，且选择成功签约时显示） -->
    <div v-if="mode !== 'view' && form.studentStatus === '2'" class="contract-info">
      <h3 class="info-title">合同信息</h3>
    <div class="info-content">
        <div style="margin-bottom: 16px;">
          <span style="font-weight: 500; color: #606266;">关联合同:</span>
          <el-icon style="color: #409EFF; margin-left: 4px;"><InfoFilled /></el-icon>
          <span style="color: #909399; margin-left: 4px;">未创建合同？</span>
          <el-button 
            type="primary" 
            link 
            @click="handleCreateContract"
            style="margin-left: 4px;"
          >
            立即创建
          </el-button>
        </div>
        
        <div 
          v-if="!selectedContract" 
          class="contract-select-box"
          @click="handleSelectContract"
        >
          <el-icon style="color: #409EFF; font-size: 20px;"><Plus /></el-icon>
          <span style="color: #409EFF; margin-left: 8px;">选择合同</span>
      </div>
      
        <div v-else class="contract-display-box">
          <div class="contract-info">
            <div class="contract-item">
              <span class="label">合同名称：</span>
              <span class="value">{{ selectedContract.contractName }}</span>
        </div>
            <div class="contract-item">
              <span class="label">合同编号：</span>
              <span class="value">{{ selectedContract.contractNumber }}</span>
        </div>
            <div class="contract-item">
              <span class="label">签约企业名称：</span>
              <span class="value">{{ selectedContract.companyName }}</span>
      </div>
            <div class="contract-item">
              <span class="label">签订时间：</span>
              <span class="value">{{ selectedContract.signDate }}</span>
        </div>
      </div>
          <el-button type="primary" link @click="handleSelectContract">重选</el-button>
        </div>
      </div>
    </div>

    <!-- 合同信息（查看模式下，只有成功签约时显示） -->
    <div v-if="mode === 'view' && studentInfo.studentStatus === '2'" class="contract-info">
      <h3 class="info-title">合同信息</h3>
      <div class="info-content">
        <div class="contract-item">
          <label>合同信息：</label>
          <div class="contract-details">
            <div v-if="studentInfo.contractName">{{ studentInfo.contractName }}</div>
            <div v-if="studentInfo.contractNumber">合同编号：{{ studentInfo.contractNumber }}</div>
            <div v-if="studentInfo.contractCompanyName">签约企业：{{ studentInfo.contractCompanyName }}</div>
            <div v-if="studentInfo.contractSignDate">签约日期：{{ studentInfo.contractSignDate }}</div>
            <div v-if="!studentInfo.contractName && !studentInfo.contractNumber && !studentInfo.contractCompanyName && !studentInfo.contractSignDate" style="color: #999;">
              暂无合同信息
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, computed, watch, onMounted } from 'vue'
import { addStudentInfo, updateStudentInfo } from "@/api/student/student"
import { listPost } from "@/api/training/post"
import { Plus, InfoFilled } from '@element-plus/icons-vue'

const props = defineProps({
  studentInfo: {
    type: Object,
    default: () => ({})
  },
  mode: {
    type: String,
    default: 'view', // 'view' | 'edit' | 'add'
    validator: (value) => ['view', 'edit', 'add'].includes(value)
  },
  majorOptions: {
    type: Array,
    default: () => []
  },
  schoolTeacherOptions: {
    type: Array,
    default: () => []
  },
  companyTeacherOptions: {
    type: Array,
    default: () => []
  }
})

const emit = defineEmits(['success', 'selectContract', 'createContract'])

// 表单引用
const formRef = ref()

// 岗位选项数据
const postOptions = ref([])

// 表单数据
const form = ref({
  studentName: '',
  studentNumber: '',
  className: '',
  gender: '',
  studentStatus: '0',
  major: '',
  schoolTeacher: '',
  companyTeacher: '',
  targetPosition: '',
  contractId: null
})

// 表单验证规则
const rules = {
  studentName: [
    { required: true, message: "学生姓名不能为空", trigger: "blur" }
  ],
  studentNumber: [
    { required: true, message: "学号不能为空", trigger: "blur" }
  ],
  className: [
    { required: true, message: "班级不能为空", trigger: "blur" }
  ],
  gender: [
    { required: true, message: "性别不能为空", trigger: "change" }
  ],
  studentStatus: [
    { required: true, message: "学生状态不能为空", trigger: "change" }
  ],
  major: [
    { required: true, message: "专业不能为空", trigger: "blur" }
  ],
  schoolTeacher: [
    { required: true, message: "学校导师不能为空", trigger: "blur" }
  ],
  companyTeacher: [
    { required: true, message: "企业导师不能为空", trigger: "blur" }
  ],
  targetPosition: [
    { required: true, message: "目标岗位不能为空", trigger: "change" }
  ]
}

// 选中的合同
const selectedContract = ref(null)

// 获取岗位列表
const getPostList = async () => {
  try {
    const response = await listPost({})
    postOptions.value = response.rows || []
  } catch (error) {
    console.error('获取岗位列表失败:', error)
  }
}

// 重置表单校验
const resetValidation = () => {
  if (formRef.value) {
    formRef.value.clearValidate()
  }
}

// 监听学生信息变化，初始化表单
watch([() => props.studentInfo, () => props.mode], ([newVal, mode]) => {
  console.log('StudentBasicInfo watch triggered:', { newVal, mode, hasKeys: newVal && Object.keys(newVal).length > 0 })

  // 重置校验
  resetValidation()

  if (newVal && Object.keys(newVal).length > 0) {
    form.value = {
      studentName: newVal.studentName || '',
      studentNumber: newVal.studentNumber || '',
      className: newVal.className || '',
      gender: newVal.gender || '',
      studentStatus: newVal.studentStatus || '0',
      major: newVal.major || '',
      schoolTeacher: newVal.schoolTeacher || '',
      companyTeacher: newVal.companyTeacher || '',
      targetPosition: newVal.targetPosition || '',
      contractId: newVal.contractId || null
    }
    // 如果有关联合同，设置选中状态
    if (newVal.contractId && newVal.contractName) {
      selectedContract.value = {
        contractId: newVal.contractId,
        contractName: newVal.contractName,
        contractNumber: newVal.contractNumber,
        companyName: newVal.contractCompanyName,
        signDate: newVal.contractSignDate
      }
    } else {
      selectedContract.value = null
    }
  } else if (mode === 'add') {
    console.log('Initializing form for add mode')
    // 新增模式时重置表单
    form.value = {
      studentName: '',
      studentNumber: '',
      className: '',
      gender: '',
      studentStatus: '0',
      major: '',
      schoolTeacher: '',
      companyTeacher: '',
      targetPosition: '',
      contractId: null
    }
    selectedContract.value = null
  }
}, { immediate: true })

// 处理选择合同
const handleSelectContract = () => {
  emit('selectContract', (contract) => {
    selectedContract.value = contract
    form.value.contractId = contract ? contract.contractId : null
  })
}

// 处理创建合同
const handleCreateContract = () => {
  emit('createContract')
}

// 提交表单
const submit = () => {
  return new Promise((resolve, reject) => {
    formRef.value.validate((valid) => {
      if (valid) {
        const submitData = { 
          ...form.value,
          contractId: selectedContract.value ? selectedContract.value.contractId : null
        }
        
        if (props.mode === 'add') {
          // 新增
          addStudentInfo(submitData).then(response => {
            emit('success', response)
            resolve(response)
          }).catch(error => {
            reject(error)
          })
        } else if (props.mode === 'edit') {
          // 修改
          submitData.studentId = props.studentInfo.studentId
          updateStudentInfo(submitData).then(response => {
            emit('success', response)
            resolve(response)
          }).catch(error => {
            reject(error)
          })
        }
      } else {
        reject(new Error('表单验证失败'))
      }
    })
  })
}

// 暴露方法给父组件
defineExpose({
  submit
})

// 组件挂载时加载岗位列表
onMounted(() => {
  getPostList()
})
</script>

<style scoped>
.basic-info {
  margin-bottom: 24px;
}

.info-title {
  margin: 0 0 16px 0;
  padding-left: 8px;
  border-left: 4px solid #409eff;
  font-size: 16px;
  font-weight: 600;
  color: #303133;
}

.student-form {
  padding: 16px;
  background-color: #f5f7fa;
  border-radius: 6px;
  margin-bottom: 16px;
}

.contract-info {
  margin-top: 24px;
}

.contract-item {
  display: flex;
  align-items: flex-start;
}

.contract-item label {
  font-weight: 500;
  color: #606266;
  margin-right: 8px;
  white-space: nowrap;
  font-size: 14px;
  min-width: 80px;
  text-align: right;
}

.contract-details {
  flex: 1;
  padding: 8px 12px;
  background-color: #f9f9f9;
  border: 1px dashed #d9d9d9;
  border-radius: 4px;
  color: #666;
  font-size: 14px;
  line-height: 1.6;
}

.contract-details div {
  margin-bottom: 4px;
}

.contract-details div:last-child {
  margin-bottom: 0;
}

.contract-select-box {
  border: 2px dashed #d9d9d9;
  border-radius: 6px;
  padding: 40px 20px;
  text-align: center;
  cursor: pointer;
  transition: all 0.3s;
}

.contract-select-box:hover {
  border-color: #409EFF;
  background-color: #f0f9ff;
}

.contract-display-box {
  border: 1px solid #e6e6e6;
  border-radius: 6px;
  padding: 16px;
  background-color: #fafafa;
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
}

.contract-info {
  flex: 1;
}

.contract-item {
  margin-bottom: 8px;
  display: flex;
  align-items: center;
}

.contract-item:last-child {
  margin-bottom: 0;
}

.contract-item .label {
  font-weight: 500;
  color: #606266;
  min-width: 100px;
  margin-right: 8px;
}

.contract-item .value {
  color: #303133;
  flex: 1;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .info-row {
    flex-direction: column;
    gap: 8px;
  }
  
  .info-item {
    flex-direction: column;
    align-items: flex-start;
  }
  
  .info-item label {
    margin-bottom: 4px;
    text-align: left;
    min-width: auto;
  }
  
  .contract-item {
    flex-direction: column;
  }
  
  .contract-item label {
    margin-bottom: 8px;
    text-align: left;
    min-width: auto;
  }
}

@media (max-width: 1200px) {
  .info-row {
    flex-direction: column;
    gap: 8px;
  }
  
  .info-item {
    flex: none;
  }
  
  .info-item label {
    min-width: 100px;
  }
  
  .contract-item label {
    min-width: 100px;
  }
}
</style> 