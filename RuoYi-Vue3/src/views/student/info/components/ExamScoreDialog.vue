<template>
  <el-dialog :title="title" v-model="dialogVisible" width="800px" append-to-body @close="handleClose">
    <!-- 学生基本信息 -->
    <StudentBasicInfo :student-info="studentInfo" />

    <!-- 成绩信息 -->
    <div class="score-info">
      <h3 class="info-title">成绩信息</h3>
      
      <!-- 选项卡 -->
      <el-tabs v-model="activeTab">
        <!-- 校内学科成绩 -->
        <el-tab-pane label="校内学科成绩" name="school">
          <div class="tab-content">
            <div class="flex justify-between items-center mb-3">
              <h4>校内学科成绩</h4>
              <el-button type="primary" @click="handleAddScore('school')">新增</el-button>
            </div>
            
            <div class="table-container">
              <el-table :data="currentScoreList" border class="responsive-table">
                <el-table-column label="#" type="index" width="50" align="center" />
                <el-table-column label="课程名称" align="center" min-width="180">
                  <template #default="scope">
                    <el-select 
                      v-if="scope.row.isEditing" 
                      v-model="scope.row.courseName" 
                      placeholder="请选择课程"
                      style="width: 100%"
                    >
                      <el-option
                        v-for="course in currentCourseOptions"
                        :key="course.value"
                        :label="course.label"
                        :value="course.value"
                      />
                    </el-select>
                    <span v-else>{{ scope.row.courseName }}</span>
                  </template>
                </el-table-column>
                <el-table-column label="考试类型" align="center" min-width="130">
                  <template #default="scope">
                    <el-input 
                      v-if="scope.row.isEditing" 
                      v-model="scope.row.examType" 
                      placeholder="如：期末考试"
                      style="width: 100%"
                    />
                    <span v-else>{{ scope.row.examType }}</span>
                  </template>
                </el-table-column>
                <el-table-column label="成绩" align="center" min-width="100">
                  <template #default="scope">
                    <el-input 
                      v-if="scope.row.isEditing" 
                      v-model="scope.row.score" 
                      placeholder="请输入成绩"
                      style="width: 100%"
                    />
                    <span v-else>{{ scope.row.score }}</span>
                  </template>
                </el-table-column>
                <el-table-column label="操作" align="center" width="120" fixed="right">
                  <template #default="scope">
                    <template v-if="scope.row.isEditing">
                      <el-button link type="primary" @click="confirmScore(scope.row)">确认</el-button>
                      <el-button link type="info" @click="cancelScore(scope.row)">取消</el-button>
                    </template>
                    <template v-else>
                      <el-button link type="primary" @click="deleteScore(scope.row)">删除</el-button>
                    </template>
                  </template>
                </el-table-column>
              </el-table>
            </div>

            <!-- 分页 -->
            <pagination
              v-show="currentScoreTotal > 0"
              :total="currentScoreTotal"
              :page="currentQueryParams.pageNum"
              :limit="currentQueryParams.pageSize"
              @pagination="handlePagination"
            />
          </div>
        </el-tab-pane>
        
        <!-- 企业课程成绩 -->
        <el-tab-pane label="企业课程成绩" name="company">
          <div class="tab-content">
            <div class="flex justify-between items-center mb-3">
              <h4>企业课程成绩</h4>
              <el-button type="primary" @click="handleAddScore('company')">新增</el-button>
            </div>
            
            <div class="table-container">
              <el-table :data="currentScoreList" border class="responsive-table">
                <el-table-column label="#" type="index" width="50" align="center" />
                <el-table-column label="课程名称" align="center" min-width="180">
                  <template #default="scope">
                    <el-select 
                      v-if="scope.row.isEditing" 
                      v-model="scope.row.courseName" 
                      placeholder="请选择课程"
                      style="width: 100%"
                    >
                      <el-option
                        v-for="course in currentCourseOptions"
                        :key="course.value"
                        :label="course.label"
                        :value="course.value"
                      />
                    </el-select>
                    <span v-else>{{ scope.row.courseName }}</span>
                  </template>
                </el-table-column>
                <el-table-column label="考试类型" align="center" min-width="130">
                  <template #default="scope">
                    <el-input 
                      v-if="scope.row.isEditing" 
                      v-model="scope.row.examType" 
                      placeholder="如：期末考试"
                      style="width: 100%"
                    />
                    <span v-else>{{ scope.row.examType }}</span>
                  </template>
                </el-table-column>
                <el-table-column label="成绩" align="center" min-width="100">
                  <template #default="scope">
                    <el-input 
                      v-if="scope.row.isEditing" 
                      v-model="scope.row.score" 
                      placeholder="请输入成绩"
                      style="width: 100%"
                    />
                    <span v-else>{{ scope.row.score }}</span>
                  </template>
                </el-table-column>
                <el-table-column label="操作" align="center" width="120" fixed="right">
                  <template #default="scope">
                    <template v-if="scope.row.isEditing">
                      <el-button link type="primary" @click="confirmScore(scope.row)">确认</el-button>
                      <el-button link type="info" @click="cancelScore(scope.row)">取消</el-button>
                    </template>
                    <template v-else>
                      <el-button link type="primary" @click="deleteScore(scope.row)">删除</el-button>
                    </template>
                  </template>
                </el-table-column>
              </el-table>
            </div>

            <!-- 分页 -->
            <pagination
              v-show="currentScoreTotal > 0"
              :total="currentScoreTotal"
              :page="currentQueryParams.pageNum"
              :limit="currentQueryParams.pageSize"
              @pagination="handlePagination"
            />
          </div>
        </el-tab-pane>
      </el-tabs>
    </div>
  </el-dialog>
</template>

<script setup>
import { ref, computed, watch } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import { listExamScoreByStudentId, addExamScore, updateExamScore, delExamScore } from '@/api/student/examScore'
import StudentBasicInfo from './StudentBasicInfo.vue'

const props = defineProps({
  modelValue: {
    type: Boolean,
    default: false
  },
  studentInfo: {
    type: Object,
    default: () => ({})
  }
})

const emit = defineEmits(['update:modelValue', 'success'])

const dialogVisible = computed({
  get: () => props.modelValue,
  set: (val) => emit('update:modelValue', val)
})

const title = ref('考试成绩')
const activeTab = ref('school')

// 校内课程相关数据
const schoolScoreList = ref([])
const schoolScoreTotal = ref(0)
const schoolQueryParams = ref({
  pageNum: 1,
  pageSize: 10
})

// 企业课程相关数据
const companyScoreList = ref([])
const companyScoreTotal = ref(0)
const companyQueryParams = ref({
  pageNum: 1,
  pageSize: 10
})

// Mock数据 - 校内课程选项
const schoolCourseOptions = [
  { label: '数据结构', value: '数据结构' },
  { label: 'Java程序设计', value: 'Java程序设计' },
  { label: '数据库原理', value: '数据库原理' },
  { label: '计算机网络', value: '计算机网络' },
  { label: '软件工程', value: '软件工程' },
  { label: '操作系统', value: '操作系统' },
  { label: '算法设计与分析', value: '算法设计与分析' }
]

// Mock数据 - 企业课程选项
const companyCourseOptions = [
  { label: '企业项目实训', value: '企业项目实训' },
  { label: 'Spring Boot开发', value: 'Spring Boot开发' },
  { label: 'Vue.js前端开发', value: 'Vue.js前端开发' },
  { label: '微服务架构', value: '微服务架构' },
  { label: '云计算与部署', value: '云计算与部署' },
  { label: '团队协作项目', value: '团队协作项目' },
  { label: '企业级开发规范', value: '企业级开发规范' }
]

// 计算属性 - 当前选项卡的数据
const currentScoreList = computed(() => {
  return activeTab.value === 'school' ? schoolScoreList.value : companyScoreList.value
})

const currentScoreTotal = computed(() => {
  return activeTab.value === 'school' ? schoolScoreTotal.value : companyScoreTotal.value
})

const currentQueryParams = computed(() => {
  return activeTab.value === 'school' ? schoolQueryParams.value : companyQueryParams.value
})

const currentCourseOptions = computed(() => {
  return activeTab.value === 'school' ? schoolCourseOptions : companyCourseOptions
})

// 监听弹框显示
watch(() => props.modelValue, (val) => {
  if (val && props.studentInfo.studentId) {
    title.value = `${props.studentInfo.studentName}的考试成绩`
    loadScoreList('school')
    loadScoreList('company')
  }
})

// 监听选项卡切换
watch(activeTab, (newTab) => {
  if (newTab && props.studentInfo.studentId) {
    loadScoreList(newTab)
  }
})

// 通用加载成绩列表方法
const loadScoreList = async (type) => {
  try {
    const response = await listExamScoreByStudentId(props.studentInfo.studentId)
    const allScores = response.data || []
    const courseType = type === 'school' ? '0' : '1'
    const filteredScores = allScores.filter(item => item.courseType === courseType)
    
    const queryParams = type === 'school' ? schoolQueryParams.value : companyQueryParams.value
    
    // 分页处理
    const startIndex = (queryParams.pageNum - 1) * queryParams.pageSize
    const endIndex = startIndex + queryParams.pageSize
    const paginatedScores = filteredScores.slice(startIndex, endIndex)
    
    if (type === 'school') {
      schoolScoreList.value = paginatedScores
      schoolScoreTotal.value = filteredScores.length
    } else {
      companyScoreList.value = paginatedScores
      companyScoreTotal.value = filteredScores.length
    }
  } catch (error) {
    console.error(`加载${type === 'school' ? '校内' : '企业'}课程成绩失败:`, error)
    ElMessage.error(`加载${type === 'school' ? '校内' : '企业'}课程成绩失败`)
  }
}

// 通用新增成绩方法
const handleAddScore = (type) => {
  const courseType = type === 'school' ? '0' : '1'
  const newScore = {
    scoreId: null,
    studentId: props.studentInfo.studentId,
    courseName: '',
    courseType: courseType,
    examType: '',
    score: '',
    isEditing: true,
    isNew: true
  }
  
  if (type === 'school') {
    schoolScoreList.value.push(newScore)
  } else {
    companyScoreList.value.push(newScore)
  }
}

// 通用确认成绩方法
const confirmScore = async (row) => {
  if (!row.courseName) {
    ElMessage.warning('请选择课程名称')
    return
  }
  if (!row.examType) {
    ElMessage.warning('请输入考试类型')
    return
  }
  if (!row.score) {
    ElMessage.warning('请输入成绩')
    return
  }

  try {
    if (row.isNew) {
      // 新增
      await addExamScore({
        studentId: row.studentId,
        courseName: row.courseName,
        courseType: row.courseType,
        examType: row.examType,
        score: row.score
      })
      ElMessage.success('新增成功')
    } else {
      // 修改
      await updateExamScore(row)
      ElMessage.success('修改成功')
    }
    
    row.isEditing = false
    row.isNew = false
    
    // 根据课程类型重新加载对应列表
    const type = row.courseType === '0' ? 'school' : 'company'
    loadScoreList(type)
    emit('success')
  } catch (error) {
    console.error('操作失败:', error)
    ElMessage.error('操作失败')
  }
}

// 通用取消编辑方法
const cancelScore = (row) => {
  if (row.isNew) {
    const type = row.courseType === '0' ? 'school' : 'company'
    const scoreList = type === 'school' ? schoolScoreList.value : companyScoreList.value
    const index = scoreList.findIndex(item => item === row)
    scoreList.splice(index, 1)
  } else {
    row.isEditing = false
    const type = row.courseType === '0' ? 'school' : 'company'
    loadScoreList(type)
  }
}

// 通用删除成绩方法
const deleteScore = async (row) => {
  try {
    await ElMessageBox.confirm('是否确认删除该成绩记录?', '警告', {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'warning'
    })
    
    await delExamScore(row.scoreId)
    ElMessage.success('删除成功')
    
    // 根据课程类型重新加载对应列表
    const type = row.courseType === '0' ? 'school' : 'company'
    loadScoreList(type)
    emit('success')
  } catch (error) {
    if (error !== 'cancel') {
      console.error('删除失败:', error)
      ElMessage.error('删除失败')
    }
  }
}

// 通用分页处理方法
const handlePagination = (pagination) => {
  const type = activeTab.value
  const queryParams = type === 'school' ? schoolQueryParams.value : companyQueryParams.value
  
  queryParams.pageNum = pagination.page
  queryParams.pageSize = pagination.limit
  loadScoreList(type)
}

// 关闭弹框
const handleClose = () => {
  activeTab.value = 'school'
  schoolQueryParams.value.pageNum = 1
  companyQueryParams.value.pageNum = 1
}
</script>

<style scoped>

.info-title {
  margin: 0 0 16px 0;
  padding-left: 8px;
  border-left: 4px solid #409eff;
  font-size: 16px;
  font-weight: 600;
  color: #303133;
}
.score-info {
  margin-top: 24px;
}

.tab-content {
  padding: 16px 0;
}

.table-container {
  overflow-x: auto;
  min-height: 400px;
}

.responsive-table {
  min-width: 600px;
}

.flex {
  display: flex;
}

.justify-between {
  justify-content: space-between;
}

.items-center {
  align-items: center;
}

.mb-3 {
  margin-bottom: 16px;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .responsive-table {
    font-size: 12px;
  }
  
  .table-container {
    padding: 0 10px;
  }
}

:deep(.el-dialog__body) {
  padding: 20px;
}

:deep(.el-tabs__content) {
  padding: 0;
}

:deep(.el-table th) {
  background-color: #fafafa;
}

:deep(.el-dialog) {
  margin-top: 5vh !important;
}

@media (max-width: 768px) {
  :deep(.el-dialog) {
    width: 95% !important;
    margin: 5px auto !important;
  }
}
</style> 