<template>
   <div class="app-container">
      <el-form :model="queryParams" ref="queryRef" :inline="true" v-show="showSearch" label-width="68px">
         <el-form-item label="字典名称" prop="dictName">
            <el-input
               v-model="queryParams.dictName"
               placeholder="请输入字典名称"
               clearable
               style="width: 240px"
               @keyup.enter="handleQuery"
            />
         </el-form-item>
         <el-form-item>
            <el-button type="primary" icon="Search" @click="handleQuery">查询</el-button>
            <el-button icon="Refresh" @click="resetQuery">重置</el-button>
         </el-form-item>
      </el-form>

      <el-row :gutter="10" class="mb8">
         <el-col :span="1.5">
            <el-button
               type="primary"
               plain
               icon="Plus"
               @click="handleAdd"
            >新增</el-button>
         </el-col>
         <el-col :span="1.5">
            <el-button
               type="danger"
               plain
               icon="Delete"
               :disabled="multiple"
               @click="handleDelete"
            >批量删除</el-button>
         </el-col>
      </el-row>

      <el-table v-loading="loading" :data="typeList" @selection-change="handleSelectionChange">
         <el-table-column type="selection" width="55" align="center" />
         <el-table-column label="字典编号" align="center" prop="dictType" />
         <el-table-column label="字典名称" align="center" prop="dictName" :show-overflow-tooltip="true"/>
         <el-table-column label="状态" align="center" prop="status">
            <template #default="scope">
               <el-tag :type="scope.row.status === '0' ? 'success' : 'danger'">
                 {{ scope.row.status === '0' ? '正常' : '停用' }}
               </el-tag>
            </template>
         </el-table-column>
         <el-table-column label="备注" align="center" prop="remark" :show-overflow-tooltip="true" />
         <el-table-column label="创建时间" align="center" prop="createTime" width="180">
            <template #default="scope">
               <span>{{ parseTime(scope.row.createTime) }}</span>
            </template>
         </el-table-column>
         <el-table-column label="更新时间" align="center" prop="updateTime" width="180">
            <template #default="scope">
               <span>{{ parseTime(scope.row.updateTime) }}</span>
            </template>
         </el-table-column>
         <el-table-column label="操作人" align="center">
            <template #default="scope">
               <span>{{ scope.row.updateBy || scope.row.createBy || '-' }}</span>
            </template>
         </el-table-column>
         <el-table-column label="操作" align="center" width="280" class-name="small-padding fixed-width">
            <template #default="scope">
               <el-button link type="primary"  @click="handleView(scope.row)">查看</el-button>
               <el-button link type="primary"  @click="handleUpdate(scope.row)">编辑</el-button>
               <el-button link type="primary"  @click="handleManageItems(scope.row)">管理子项</el-button>
               <el-button link type="primary"  @click="handleToggleStatus(scope.row)">
                 {{ scope.row.status === '0' ? '封存' : '开启' }}
               </el-button>
            </template>
         </el-table-column>
      </el-table>

      <pagination
         v-show="total > 0"
         :total="total"
         v-model:page="queryParams.pageNum"
         v-model:limit="queryParams.pageSize"
         @pagination="getList"
      />

      <!-- 添加或修改参数配置对话框 -->
      <el-dialog :title="title" v-model="open" width="580px" append-to-body>
         <el-form ref="dictRef" :model="form" :rules="rules" label-width="140px">
            <el-row :gutter="20">
               <el-col :span="24">
                  <el-form-item label="字典编号" prop="dictType">
                     <el-input 
                        v-model="form.dictType" 
                        placeholder="请输入字典编号，如：sys_user_sex" 
                        clearable
                     />
                  </el-form-item>
               </el-col>
               <el-col :span="24">
                  <el-form-item label="字典名称" prop="dictName">
                     <el-input 
                        v-model="form.dictName" 
                        placeholder="请输入字典名称，如：用户性别" 
                        clearable
                     />
                  </el-form-item>
               </el-col>
               <el-col :span="24">
                  <el-form-item label="是否创建后立刻启用" prop="status">
                     <el-radio-group v-model="form.status">
                        <el-radio value="0">
                           <el-icon><Check /></el-icon>
                           启用
                        </el-radio>
                        <el-radio value="1">
                           <el-icon><Lock /></el-icon>
                           封存
                        </el-radio>
                     </el-radio-group>
                  </el-form-item>
               </el-col>
               <el-col :span="24">
                  <el-form-item label="备注" prop="remark">
                     <el-input 
                        v-model="form.remark" 
                        type="textarea" 
                        placeholder="请输入备注信息" 
                        :maxlength="300" 
                        show-word-limit
                        :rows="3"
                     />
                  </el-form-item>
               </el-col>
            </el-row>
         </el-form>
         <template #footer>
            <div class="dialog-footer">
               <el-button @click="cancel">取 消</el-button>
               <el-button type="primary" @click="submitForm">确 定</el-button>
            </div>
         </template>
      </el-dialog>

      <!-- 管理子项对话框 -->
      <el-dialog title="管理子项" v-model="itemDialogVisible" width="700px" append-to-body>
         <!-- 字典信息头部 -->
         <el-card shadow="never" class="mb-4">
            <div class="flex items-center">
               <div>
                  <div class="text-lg font-semibold">{{ currentDictName }}</div>
                  <div class="text-sm text-gray-500">编号：{{ currentDictType }}</div>
               </div>
            </div>
         </el-card>

         <!-- 操作按钮 -->
         <div class="mb-4 mt-4">
            <el-button type="primary" icon="Plus" @click="handleAddItem" class="mr-2">
               新增子项
            </el-button>
            <el-button 
               type="danger" 
               icon="Delete" 
               :disabled="itemMultiple" 
               @click="handleDeleteItems"
            >
               批量删除
            </el-button>
         </div>

         <!-- 子项列表 -->
         <el-table 
            v-loading="itemLoading" 
            :data="itemList" 
            @selection-change="handleItemSelectionChange"
            border
            stripe
         >
            <el-table-column type="selection" width="55" align="center" />
            <el-table-column label="序号" type="index" width="60" align="center" />
            <el-table-column label="字典值" align="center" prop="dictValue" min-width="200">
               <template #default="scope">
                  <el-tag type="info" effect="plain">{{ scope.row.dictValue }}</el-tag>
               </template>
            </el-table-column>
            <el-table-column label="操作" align="center" width="120">
               <template #default="scope">
                  <el-button 
                     link 
                     type="danger" 
                     icon="Delete" 
                     @click="handleDeleteItem(scope.row)"
                  >
                     删除
                  </el-button>
               </template>
            </el-table-column>
         </el-table>

         <!-- 子项分页 -->
         <pagination
            v-show="itemTotal > 0"
            :total="itemTotal"
            :page="itemQueryParams.pageNum"
            :limit="itemQueryParams.pageSize"
            @pagination="handleItemPagination"
         />

         <!-- 新增子项对话框 -->
         <el-dialog title="新增子项" v-model="addItemDialogVisible" width="450px" append-to-body>
            <el-form ref="itemFormRef" :model="itemForm" :rules="itemRules" label-width="80px">
               <el-form-item label="字典值" prop="dictValue">
                  <el-input 
                     v-model="itemForm.dictValue" 
                     placeholder="请输入字典值，如：男、女" 
                     clearable
                  />
               </el-form-item>
            </el-form>
            <template #footer>
               <div class="dialog-footer">
                  <el-button @click="cancelItem">取 消</el-button>
                  <el-button type="primary" @click="submitItemForm">确 定</el-button>
               </div>
            </template>
         </el-dialog>
      </el-dialog>

   </div>
</template>

<script setup name="Dict">
import useDictStore from '@/store/modules/dict'
import { listType, getType, delType, addType, updateType, refreshCache } from "@/api/system/dict/type"
import { listData, getData, delData, addData, updateData } from "@/api/system/dict/data"

const { proxy } = getCurrentInstance()
const { sys_normal_disable } = proxy.useDict("sys_normal_disable")

const typeList = ref([])
const open = ref(false)
const loading = ref(true)
const showSearch = ref(true)
const ids = ref([])
const single = ref(true)
const multiple = ref(true)
const total = ref(0)
const title = ref("")
const dateRange = ref([])

// 管理子项相关
const itemDialogVisible = ref(false)
const itemLoading = ref(false)
const itemList = ref([])
const itemIds = ref([])
const itemMultiple = ref(true)
const currentDictType = ref("")
const currentDictName = ref("")
const addItemDialogVisible = ref(false)
const itemForm = ref({})
const itemRules = ref({
  dictValue: [{ required: true, message: "值不能为空", trigger: "blur" }]
})
const itemTotal = ref(0)
const itemQueryParams = ref({
  pageNum: 1,
  pageSize: 10,
  dictType: ""
})



const data = reactive({
  form: {},
  queryParams: {
    pageNum: 1,
    pageSize: 10,
    dictName: undefined
  },
  rules: {
    dictName: [{ required: true, message: "字典名称不能为空", trigger: "blur" }],
    dictType: [{ required: true, message: "字典编号不能为空", trigger: "blur" }]
  },
})

const { queryParams, form, rules } = toRefs(data)

/** 查询字典类型列表 */
function getList() {
  loading.value = true
  listType(queryParams.value).then(response => {
    typeList.value = response.rows
    total.value = response.total
    loading.value = false
  })
}

/** 取消按钮 */
function cancel() {
  open.value = false
  reset()
}

/** 表单重置 */
function reset() {
  form.value = {
    dictId: undefined,
    dictName: undefined,
    dictType: undefined,
    status: "0",
    remark: undefined
  }
  proxy.resetForm("dictRef")
}

/** 搜索按钮操作 */
function handleQuery() {
  queryParams.value.pageNum = 1
  getList()
}

/** 重置按钮操作 */
function resetQuery() {
  proxy.resetForm("queryRef")
  handleQuery()
}

/** 新增按钮操作 */
function handleAdd() {
  reset()
  open.value = true
  title.value = "添加字典类型"
}

/** 多选框选中数据 */
function handleSelectionChange(selection) {
  ids.value = selection.map(item => item.dictId)
  single.value = selection.length != 1
  multiple.value = !selection.length
}

/** 修改按钮操作 */
function handleUpdate(row) {
  reset()
  const dictId = row.dictId || ids.value
  getType(dictId).then(response => {
    form.value = response.data
    open.value = true
    title.value = "修改字典类型"
  })
}

/** 提交按钮 */
function submitForm() {
  proxy.$refs["dictRef"].validate(valid => {
    if (valid) {
      if (form.value.dictId != undefined) {
        updateType(form.value).then(response => {
          proxy.$modal.msgSuccess("修改成功")
          open.value = false
          getList()
        })
      } else {
        addType(form.value).then(response => {
          proxy.$modal.msgSuccess("新增成功")
          open.value = false
          getList()
        })
      }
    }
  })
}

/** 删除按钮操作 */
function handleDelete(row) {
  const dictIds = row.dictId || ids.value
  proxy.$modal.confirm('是否确认删除字典编号为"' + dictIds + '"的数据项？').then(function() {
    return delType(dictIds)
  }).then(() => {
    getList()
    proxy.$modal.msgSuccess("删除成功")
  }).catch(() => {})
}

/** 导出按钮操作 */
function handleExport() {
  proxy.download("system/dict/type/export", {
    ...queryParams.value
  }, `dict_${new Date().getTime()}.xlsx`)
}

/** 刷新缓存按钮操作 */
function handleRefreshCache() {
  refreshCache().then(() => {
    proxy.$modal.msgSuccess("刷新成功")
    useDictStore().cleanDict()
  })
}

/** 查看按钮操作 */
function handleView(row) {
  reset()
  const dictId = row.dictId
  getType(dictId).then(response => {
    form.value = response.data
    open.value = true
    title.value = "查看字典类型"
  })
}

/** 管理子项按钮操作 */
function handleManageItems(row) {
  currentDictType.value = row.dictType
  currentDictName.value = row.dictName
  itemDialogVisible.value = true
  // 重置分页参数
  itemQueryParams.value.pageNum = 1
  itemQueryParams.value.pageSize = 10
  getItemList(row.dictType)
}

/** 获取子项列表 */
function getItemList(dictType) {
  itemLoading.value = true
  itemQueryParams.value.dictType = dictType
  listData(itemQueryParams.value).then(response => {
    itemList.value = response.rows
    itemTotal.value = response.total
    itemLoading.value = false
  })
}

/** 子项分页处理 */
function handleItemPagination(pagination) {
  itemQueryParams.value.pageNum = pagination.page
  itemQueryParams.value.pageSize = pagination.limit
  getItemList(currentDictType.value)
}

/** 子项多选框选中数据 */
function handleItemSelectionChange(selection) {
  itemIds.value = selection.map(item => item.dictCode)
  itemMultiple.value = !selection.length
}

/** 新增子项按钮操作 */
function handleAddItem() {
  itemForm.value = {
    dictType: currentDictType.value
  }
  addItemDialogVisible.value = true
}

/** 删除子项按钮操作 */
function handleDeleteItem(row) {
  const dictCodes = [row.dictCode]
  proxy.$modal.confirm('是否确认删除该子项？').then(function() {
    return delData(dictCodes)
  }).then(() => {
    // 如果当前页只有一条数据且不是第一页，则跳转到上一页
    if (itemList.value.length === 1 && itemQueryParams.value.pageNum > 1) {
      itemQueryParams.value.pageNum--
    }
    getItemList(currentDictType.value)
    proxy.$modal.msgSuccess("删除成功")
  }).catch(() => {})
}

/** 批量删除子项按钮操作 */
function handleDeleteItems() {
  const dictCodes = itemIds.value
  proxy.$modal.confirm('是否确认删除选中的子项？').then(function() {
    return delData(dictCodes)
  }).then(() => {
    // 如果删除后当前页没有数据且不是第一页，则跳转到上一页
    if (itemList.value.length <= dictCodes.length && itemQueryParams.value.pageNum > 1) {
      itemQueryParams.value.pageNum--
    }
    getItemList(currentDictType.value)
    proxy.$modal.msgSuccess("删除成功")
  }).catch(() => {})
}

/** 提交子项表单 */
function submitItemForm() {
  proxy.$refs["itemFormRef"].validate(valid => {
    if (valid) {
      addData(itemForm.value).then(response => {
        proxy.$modal.msgSuccess("新增成功")
        addItemDialogVisible.value = false
        // 新增后跳转到第一页
        itemQueryParams.value.pageNum = 1
        getItemList(currentDictType.value)
      })
    }
  })
}

/** 取消子项按钮 */
function cancelItem() {
  addItemDialogVisible.value = false
  proxy.resetForm("itemFormRef")
}



/** 切换状态按钮操作 */
function handleToggleStatus(row) {
  const newStatus = row.status === '0' ? '1' : '0'
  const statusText = newStatus === '0' ? '启用' : '封存'
  proxy.$modal.confirm('是否确认' + statusText + '字典编号为"' + row.dictType + '"的数据项？').then(function() {
    const updateData = { ...row, status: newStatus }
    return updateType(updateData)
  }).then(() => {
    getList()
    proxy.$modal.msgSuccess(statusText + "成功")
  }).catch(() => {})
}

getList()
</script>
