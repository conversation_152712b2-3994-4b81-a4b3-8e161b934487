<template>
      <el-header class="header">
        <div class="header-content">
          <div class="header-left">
            <h1>现场工程师学院人才培养管理系统</h1>
          </div>
          <div class="header-right">
            <el-dropdown @command="handleCommand" trigger="hover">
              <span class="user-info">
                <el-icon class="user-icon">
                  <UserFilled />
                </el-icon>
                <span class="username">{{ userStore.nickName || 'admin' }}</span>
                <el-icon class="el-icon--right">
                  <ArrowDown />
                </el-icon>
              </span>
              <template #dropdown>
                <el-dropdown-menu>
                  <router-link to="/user/profile">
                    <el-dropdown-item>个人中心</el-dropdown-item>
                  </router-link>
                  <el-dropdown-item divided command="logout">
                    <span>退出登录</span>
                  </el-dropdown-item>
                </el-dropdown-menu>
              </template>
            </el-dropdown>
          </div>
        </div>
      </el-header>
</template>

<script setup>
import { ElMessageBox } from 'element-plus'
import { UserFilled, ArrowDown } from '@element-plus/icons-vue'
import useUserStore from '@/store/modules/user'

const userStore = useUserStore()

function handleCommand(command) {
  switch (command) {
    case "logout":
      logout()
      break
    default:
      break
  }
}

function logout() {
  ElMessageBox.confirm('确定注销并退出系统吗？', '提示', {
    confirmButtonText: '确定',
    cancelButtonText: '取消',
    type: 'warning'
  }).then(() => {
    userStore.logOut().then(() => {
      location.href = '/index'
    })
  }).catch(() => { })
}

const emits = defineEmits(['setLayout'])
</script>

<style lang='scss' scoped>
.app {
  width: 100%;
  height: 100vh;
  overflow: hidden;
  margin: 0;
  padding: 0;
}

.header {
  background: #2156d7;
  color: white;
  padding: 0;
  height: 60px !important;
  line-height: 60px;
  width: 100%;
}

.header-content {
  display: flex;
  justify-content: space-between;
  align-items: center;
  height: 100%;
  padding: 0 20px;
}

.header-left {
  display: flex;
  align-items: center;
  gap: 15px;
  
  h1 {
    margin: 0;
    font-size: 18px;
    font-weight: 600;
    color: white;
  }
}

.header-right {
  display: flex;
  align-items: center;
  
  .user-info {
    display: flex;
    align-items: center;
    gap: 8px;
    padding: 8px 12px;
    border-radius: 4px;
    cursor: pointer;
    transition: background-color 0.3s ease;
    color: white;
    
    &:hover {
      background-color: rgba(255, 255, 255, 0.1);
    }
    
    .user-icon {
      font-size: 16px;
      color: white;
    }
    
    .username {
      font-size: 14px;
      font-weight: 500;
      color: white;
    }
    
    .el-icon--right {
      font-size: 12px;
      color: rgba(255, 255, 255, 0.8);
      transition: transform 0.3s ease;
    }
  }
}

// 下拉菜单样式优化
:deep(.el-dropdown-menu) {
  border-radius: 6px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
  
  .el-dropdown-menu__item {
    padding: 10px 16px;
    font-size: 14px;
    color: #333;
    
    &:hover {
      background-color: #f5f7fa;
      color: #409eff;
    }
    
    &.is-disabled {
      color: #c0c4cc;
    }
  }
}

// 路由链接样式
:deep(a) {
  text-decoration: none;
  color: inherit;
  
  &:hover {
    text-decoration: none;
  }
}
</style>
