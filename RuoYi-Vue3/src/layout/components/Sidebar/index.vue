<template>
  <div :class="{ 'has-logo': showLogo }" class="sidebar-container">
    <logo v-if="showLogo" :collapse="isCollapse" />
    <el-scrollbar wrap-class="scrollbar-wrapper">
      <el-menu
        :default-active="activeMenu"
        :collapse="isCollapse"
        :background-color="menuBackground"
        :text-color="menuTextColor"
        :unique-opened="false"
        :active-text-color="activeTextColor"
        :collapse-transition="false"
        mode="vertical"
      >
        <sidebar-item
          v-for="(route, index) in sidebarRouters"
          :key="route.path + index"
          :item="route"
          :base-path="route.path"
        />
      </el-menu>
    </el-scrollbar>
  </div>
</template>

<script setup>
import Logo from './Logo'
import SidebarItem from './SidebarItem'
import useAppStore from '@/store/modules/app'
import useSettingsStore from '@/store/modules/settings'
import usePermissionStore from '@/store/modules/permission'

const route = useRoute()
const appStore = useAppStore()
const settingsStore = useSettingsStore()
const permissionStore = usePermissionStore()

const sidebarRouters = computed(() => permissionStore.sidebarRouters)
const showLogo = computed(() => settingsStore.sidebarLogo)
const isCollapse = computed(() => !appStore.sidebar.opened)

// 现代化菜单颜色配置
const menuBackground = computed(() => 'transparent')
const menuTextColor = computed(() => '#64748b')
const activeTextColor = computed(() => '#409eff')

const activeMenu = computed(() => {
  const { meta, path } = route
  if (meta.activeMenu) {
    return meta.activeMenu
  }
  return path
})
</script>

<style lang="scss" scoped>
.sidebar-container {
  height: calc(100vh - 60px) !important;
  background: linear-gradient(180deg, #ffffff 0%, #f8fafc 100%);
  border-right: 1px solid #e2e8f0;
  box-shadow: 0 1px 3px 0 rgba(0, 0, 0, 0.1), 0 1px 2px 0 rgba(0, 0, 0, 0.06);
  
  .scrollbar-wrapper {
    background: transparent;
  }

  .el-menu {
    border: none;
    height: 100%;
    width: 100% !important;
    background: transparent !important;
    padding: 8px 0;
    
    .el-menu-item, .el-sub-menu__title {
      background: transparent;
      border-radius: 8px;
      margin: 4px 12px;
      transition: none;
      font-weight: 500;
      color: #64748b;
      position: relative;

      &:hover {
        color: #409eff !important;
      }
    }

    .el-menu-item {
      &.is-active {
        background: #e8f0fe !important;
        color: #409eff !important;
        box-shadow: 0 2px 8px rgba(64, 158, 255, 0.15);

        &::before {
          content: '';
          position: absolute;
          left: 0;
          top: 50%;
          transform: translateY(-50%);
          width: 4px;
          height: 20px;
          background: #409eff;
          border-radius: 0 2px 2px 0;
        }
      }
    }
  }
}
</style>
