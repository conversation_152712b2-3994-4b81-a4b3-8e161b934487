import request from '@/utils/request'

// 查询职业能力列表
export function listAbility(query) {
  return request({
    url: '/training/ability/list',
    method: 'get',
    params: query
  })
}

// 查询职业能力详细
export function getAbility(abilityId) {
  return request({
    url: '/training/ability/' + abilityId,
    method: 'get'
  })
}

// 新增职业能力
export function addAbility(data) {
  return request({
    url: '/training/ability',
    method: 'post',
    data: data
  })
}

// 修改职业能力
export function updateAbility(data) {
  return request({
    url: '/training/ability',
    method: 'put',
    data: data
  })
}

// 删除职业能力
export function delAbility(abilityIds) {
  return request({
    url: '/training/ability/remove',
    method: 'post',
    data: abilityIds
  })
}

// 导出职业能力
export function exportAbility(query) {
  return request({
    url: '/training/ability/export',
    method: 'post',
    data: query
  })
}

// 导入职业能力
export function importAbility(data) {
  return request({
    url: '/training/ability/importData',
    method: 'post',
    data: data
  })
}

// 下载职业能力导入模板
export function importTemplate() {
  return request({
    url: '/training/ability/importTemplate',
    method: 'post'
  })
}