import request from '@/utils/request'

// 查询评价模型题目列表
export function listQuestion(query) {
  return request({
    url: '/training/question/list',
    method: 'get',
    params: query
  })
}



// 新增评价模型题目
export function addQuestion(data) {
  return request({
    url: '/training/question',
    method: 'post',
    data: data
  })
}

// 修改评价模型题目
export function updateQuestion(data) {
  return request({
    url: '/training/question',
    method: 'put',
    data: data
  })
}

// 删除评价模型题目
export function delQuestion(questionId) {
  return request({
    url: '/training/question/' + questionId,
    method: 'delete'
  })
}

// 更新评价模型题目状态
export function updateQuestionStatus(data) {
  return request({
    url: '/training/question/status',
    method: 'put',
    data: data
  })
} 