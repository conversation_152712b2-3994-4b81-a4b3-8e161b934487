import request from '@/utils/request'

// 查询评价模型题库列表
export function listBank(query) {
  return request({
    url: '/training/bank/list',
    method: 'get',
    params: query
  })
}

// 查询评价模型题库详细
export function getBank(bankId) {
  return request({
    url: '/training/bank/' + bankId,
    method: 'get'
  })
}

// 新增评价模型题库
export function addBank(data) {
  return request({
    url: '/training/bank',
    method: 'post',
    data: data
  })
}

// 修改评价模型题库
export function updateBank(data) {
  return request({
    url: '/training/bank',
    method: 'put',
    data: data
  })
}

// 删除评价模型题库
export function delBank(bankId) {
  return request({
    url: '/training/bank/' + bankId,
    method: 'delete'
  })
} 