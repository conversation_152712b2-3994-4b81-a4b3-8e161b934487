import request from '@/utils/request'

// 查询人才培养方案列表
export function listProgram(query) {
  return request({
    url: '/training/program/list',
    method: 'get',
    params: query
  })
}

// 查询人才培养方案详细
export function getProgram(programId) {
  return request({
    url: '/training/program/' + programId,
    method: 'get'
  })
}

// 新增人才培养方案
export function addProgram(data) {
  return request({
    url: '/training/program',
    method: 'post',
    data: data
  })
}

// 修改人才培养方案
export function updateProgram(data) {
  return request({
    url: '/training/program',
    method: 'put',
    data: data
  })
}

// 删除人才培养方案
export function delProgram(programIds) {
  return request({
    url: '/training/program/remove',
    method: 'post',
    data: programIds
  })
}

// 导出人才培养方案
export function exportProgram(query) {
  return request({
    url: '/training/program/export',
    method: 'post',
    data: query
  })
}

// 导入人才培养方案
export function importProgram(data) {
  return request({
    url: '/training/program/importData',
    method: 'post',
    data: data
  })
}

// 下载人才培养方案导入模板
export function importTemplate() {
  return request({
    url: '/training/program/importTemplate',
    method: 'post'
  })
} 