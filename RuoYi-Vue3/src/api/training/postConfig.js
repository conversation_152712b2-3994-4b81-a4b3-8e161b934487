import request from '@/utils/request'

// 查询岗位模型配置
export function getPostModelConfig(postId) {
  return request({
    url: '/training/post-config/' + postId,
    method: 'get'
  })
}

// 保存岗位模型配置
export function savePostModelConfig(data) {
  return request({
    url: '/training/post-config/save',
    method: 'post',
    data: data
  })
}

// 更新岗位模型配置
export function updatePostModelConfig(data) {
  return request({
    url: '/training/post-config',
    method: 'put',
    data: data
  })
}

// 删除岗位模型配置
export function delPostModelConfig(postIds) {
  return request({
    url: '/training/post-config/remove',
    method: 'post',
    params: { postIds: postIds }
  })
}

// 获取岗位模型配置列表
export function listPostModelConfig(query) {
  return request({
    url: '/training/post-config/list',
    method: 'get',
    params: query
  })
}

 