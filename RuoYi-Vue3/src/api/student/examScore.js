import request from '@/utils/request'

// 查询学生考试成绩列表
export function listExamScore(query) {
  return request({
    url: '/student/score/list',
    method: 'get',
    params: query
  })
}

// 根据学生ID查询考试成绩列表
export function listExamScoreByStudentId(studentId) {
  return request({
    url: '/student/score/list/' + studentId,
    method: 'get'
  })
}

// 查询学生考试成绩详细
export function getExamScore(scoreId) {
  return request({
    url: '/student/score/' + scoreId,
    method: 'get'
  })
}

// 新增学生考试成绩
export function addExamScore(data) {
  return request({
    url: '/student/score',
    method: 'post',
    data: data
  })
}

// 修改学生考试成绩
export function updateExamScore(data) {
  return request({
    url: '/student/score',
    method: 'put',
    data: data
  })
}

// 删除学生考试成绩
export function delExamScore(scoreId) {
  return request({
    url: '/student/score/' + scoreId,
    method: 'delete'
  })
} 