import request from '@/utils/request'

// 查询学生合同信息列表
export function listContract(query) {
  return request({
    url: '/student/contract/list',
    method: 'get',
    params: query
  })
}

// 查询学生合同信息详细
export function getContract(contractId) {
  return request({
    url: '/student/contract/' + contractId,
    method: 'get'
  })
}

// 新增学生合同信息
export function addContract(data) {
  return request({
    url: '/student/contract',
    method: 'post',
    data: data
  })
}

// 修改学生合同信息
export function updateContract(data) {
  return request({
    url: '/student/contract',
    method: 'put',
    data: data
  })
}

// 删除学生合同信息
export function delContract(contractId) {
  return request({
    url: '/student/contract/' + contractId,
    method: 'delete'
  })
}

 