import request from '@/utils/request'

// 查询学生成长路径列表
export function listGrowthPath(query) {
  return request({
    url: '/student/growthPath/list',
    method: 'get',
    params: query
  })
}

// 根据学生ID查询成长路径列表
export function listGrowthPathByStudentId(studentId, query) {
  return request({
    url: '/student/growthPath/listByStudentId/' + studentId,
    method: 'get',
    params: query
  })
}

// 查询学生成长路径详细
export function getGrowthPath(pathId) {
  return request({
    url: '/student/growthPath/' + pathId,
    method: 'get'
  })
}

// 新增学生成长路径
export function addGrowthPath(data) {
  return request({
    url: '/student/growthPath',
    method: 'post',
    data: data
  })
}

// 修改学生成长路径
export function updateGrowthPath(data) {
  return request({
    url: '/student/growthPath',
    method: 'put',
    data: data
  })
}

// 删除学生成长路径
export function delGrowthPath(pathId) {
  return request({
    url: '/student/growthPath/' + pathId,
    method: 'delete'
  })
} 