/**
 * 导入相关工具函数
 */

/**
 * 验证Excel文件
 * @param {File} file - 要验证的文件
 * @param {number} maxSize - 最大文件大小(MB)
 * @returns {Object} 验证结果 {valid: boolean, message: string}
 */
export function validateExcelFile(file, maxSize = 10) {
  // 检查文件类型
  const allowedTypes = [
    'application/vnd.ms-excel',
    'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
    'application/octet-stream' // 某些情况下Excel文件可能是这个类型
  ]
  
  const allowedExtensions = ['.xls', '.xlsx']
  const fileName = file.name.toLowerCase()
  
  // 检查文件扩展名
  const hasValidExtension = allowedExtensions.some(ext => fileName.endsWith(ext))
  
  // 检查MIME类型
  const hasValidType = allowedTypes.includes(file.type) || file.type === ''
  
  if (!hasValidExtension && !hasValidType) {
    return {
      valid: false,
      message: '只能上传Excel文件（.xls、.xlsx格式）'
    }
  }
  
  // 检查文件大小
  const maxSizeInBytes = maxSize * 1024 * 1024 // 转换为字节
  if (file.size > maxSizeInBytes) {
    return {
      valid: false,
      message: `文件大小不能超过 ${maxSize}MB`
    }
  }
  
  return {
    valid: true,
    message: '文件验证通过'
  }
}

/**
 * 验证CSV文件
 * @param {File} file - 要验证的文件
 * @param {number} maxSize - 最大文件大小(MB)
 * @returns {Object} 验证结果 {valid: boolean, message: string}
 */
export function validateCSVFile(file, maxSize = 10) {
  // 检查文件类型
  const allowedTypes = [
    'text/csv',
    'text/plain',
    'application/csv'
  ]
  
  const allowedExtensions = ['.csv']
  const fileName = file.name.toLowerCase()
  
  // 检查文件扩展名
  const hasValidExtension = allowedExtensions.some(ext => fileName.endsWith(ext))
  
  // 检查MIME类型
  const hasValidType = allowedTypes.includes(file.type) || file.type === ''
  
  if (!hasValidExtension && !hasValidType) {
    return {
      valid: false,
      message: '只能上传CSV文件（.csv格式）'
    }
  }
  
  // 检查文件大小
  const maxSizeInBytes = maxSize * 1024 * 1024 // 转换为字节
  if (file.size > maxSizeInBytes) {
    return {
      valid: false,
      message: `文件大小不能超过 ${maxSize}MB`
    }
  }
  
  return {
    valid: true,
    message: '文件验证通过'
  }
}

/**
 * 通用文件验证函数
 * @param {File} file - 要验证的文件
 * @param {Object} options - 验证选项
 * @param {Array} options.allowedTypes - 允许的MIME类型
 * @param {Array} options.allowedExtensions - 允许的文件扩展名
 * @param {number} options.maxSize - 最大文件大小(MB)
 * @returns {Object} 验证结果 {valid: boolean, message: string}
 */
export function validateFile(file, options = {}) {
  const {
    allowedTypes = [],
    allowedExtensions = [],
    maxSize = 10
  } = options
  
  // 检查文件类型
  const fileName = file.name.toLowerCase()
  const hasValidExtension = allowedExtensions.some(ext => fileName.endsWith(ext))
  const hasValidType = allowedTypes.includes(file.type) || file.type === ''
  
  if (!hasValidExtension && !hasValidType) {
    return {
      valid: false,
      message: `只能上传 ${allowedExtensions.join('、')} 格式的文件`
    }
  }
  
  // 检查文件大小
  const maxSizeInBytes = maxSize * 1024 * 1024 // 转换为字节
  if (file.size > maxSizeInBytes) {
    return {
      valid: false,
      message: `文件大小不能超过 ${maxSize}MB`
    }
  }
  
  return {
    valid: true,
    message: '文件验证通过'
  }
}

/**
 * 格式化文件大小
 * @param {number} bytes - 文件大小（字节）
 * @returns {string} 格式化后的文件大小
 */
export function formatFileSize(bytes) {
  if (bytes === 0) return '0 B'
  
  const k = 1024
  const sizes = ['B', 'KB', 'MB', 'GB', 'TB']
  const i = Math.floor(Math.log(bytes) / Math.log(k))
  
  return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i]
}

/**
 * 获取文件扩展名
 * @param {string} fileName - 文件名
 * @returns {string} 文件扩展名（包含点号）
 */
export function getFileExtension(fileName) {
  const lastDotIndex = fileName.lastIndexOf('.')
  return lastDotIndex > 0 ? fileName.substring(lastDotIndex) : ''
}

/**
 * 检查是否为Excel文件
 * @param {File} file - 文件对象
 * @returns {boolean} 是否为Excel文件
 */
export function isExcelFile(file) {
  const fileName = file.name.toLowerCase()
  return fileName.endsWith('.xls') || fileName.endsWith('.xlsx')
}

/**
 * 检查是否为CSV文件
 * @param {File} file - 文件对象
 * @returns {boolean} 是否为CSV文件
 */
export function isCSVFile(file) {
  const fileName = file.name.toLowerCase()
  return fileName.endsWith('.csv')
} 