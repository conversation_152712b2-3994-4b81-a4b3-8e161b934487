// 全局表单提交阻止工具
export function preventFormSubmit() {
  // 监听所有表单的提交事件
  document.addEventListener('submit', function(e) {
    // 检查是否是Element Plus的表单
    const form = e.target
    if (form && form.classList.contains('el-form')) {
      e.preventDefault()
      e.stopPropagation()
      return false
    }
  }, true)

  // 监听所有输入框的回车事件
  document.addEventListener('keydown', function(e) {
    if (e.key === 'Enter') {
      const target = e.target
      // 检查是否在Element Plus的输入框中
      if (target && (
        target.classList.contains('el-input__inner') ||
        target.classList.contains('el-textarea__inner') ||
        target.tagName === 'INPUT' ||
        target.tagName === 'TEXTAREA'
      )) {
        // 检查是否在表单中
        const form = target.closest('.el-form')
        if (form) {
          e.preventDefault()
          e.stopPropagation()
          return false
        }
      }
    }
  }, true)
}

// 自动初始化
preventFormSubmit() 