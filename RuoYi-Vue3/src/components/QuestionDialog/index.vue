<template>
  <el-dialog :title="title" v-model="open" width="1000px" append-to-body>
    <div class="question-dialog-container">
      <!-- 左侧题目类型选择和列表 -->
      <div class="left-panel">
        <div class="question-type-buttons">
          <div 
            class="type-btn"
            @click="selectQuestionType('0')"
          >
            <el-icon><Plus /></el-icon>
            <span>单选题</span>
          </div>
          <div class="divider-vertical"></div>
          <div 
            class="type-btn"
            @click="selectQuestionType('1')"
          >
            <el-icon><Plus /></el-icon>
            <span>多选题</span>
          </div>
        </div>
        
        <div class="question-list">
          <div 
            v-for="(question, index) in questionList" 
            :key="index"
            class="question-item"
            :class="{ active: currentQuestionIndex === index }"
            @click="selectQuestion(index)"
          >
            <span class="question-number">#{{ index + 1 }}</span>
            <span class="question-type-text">{{ question.questionType === '0' ? '单选题' : '多选题' }}</span>
            <el-button 
              type="text" 
              size="small" 
              @click.stop="deleteQuestion(index)"
              :disabled="questionList.length <= 1"
              class="delete-btn"
            >
              <el-icon><Delete /></el-icon>
            </el-button>
          </div>
        </div>
      </div>

      <!-- 右侧题目配置 -->
      <div class="right-panel">
        <div class="config-header">
          <h3>题目配置</h3>
          <div class="divider"></div>
        </div>
        
        <div v-if="currentQuestion" class="config-content">
          <!-- 题干 -->
          <div class="form-item">
            <label class="form-label">题干:</label>
            <el-input
              v-model="currentQuestion.questionStem"
              type="textarea"
              :rows="3"
              placeholder="请输入"
              maxlength="200"
              show-word-limit
              class="form-input"
            />
          </div>

          <!-- 选项 -->
          <div class="form-item">
            <label class="form-label">选项:</label>
            <div class="options-container">
              <el-button type="primary" size="small" @click="addOption" class="add-option-btn">
                + 添加
              </el-button>
              <div 
                v-for="(option, index) in currentQuestion.options" 
                :key="index"
                class="option-item"
              >
                <el-input
                  v-model="option.value"
                  :placeholder="`选项 ${String.fromCharCode(65 + index)}`"
                  class="option-input"
                />
                <el-button
                  type="danger"
                  size="small"
                  @click="removeOption(index)"
                  :disabled="currentQuestion.options.length <= 2"
                  class="delete-option-btn"
                >
                  <el-icon><Delete /></el-icon>
                </el-button>
              </div>
            </div>
          </div>

          <!-- 职业能力 -->
          <div class="form-item">
            <label class="form-label">职业能力:</label>
            <el-cascader
              v-model="currentQuestion.abilityCascader"
              :options="abilityOptions"
              :props="cascaderProps"
              placeholder="请选择职业能力"
              clearable
              class="form-input"
              @change="handleAbilityChange"
            />
          </div>
        </div>
        
        <div v-else class="empty-config">
          <!-- <div class="empty-illustration">
            <div class="glow-box"></div>
          </div> -->
          <p class="empty-text">请添加题型</p>
          <div class="empty-buttons">
            <div class="type-btn" @click="selectQuestionType('0')">
              <el-icon><Plus /></el-icon>
              <span>单选题</span>
            </div>
            <div class="divider-vertical"></div>
            <div class="type-btn" @click="selectQuestionType('1')">
              <el-icon><Plus /></el-icon>
              <span>多选题</span>
            </div>
          </div>
        </div>
      </div>
    </div>

    <template #footer>
      <div class="dialog-footer">
        <el-button type="primary" @click="submitForm">保存</el-button>
        <el-button @click="cancel">取消</el-button>
      </div>
    </template>
  </el-dialog>
</template>

<script setup name="QuestionDialog">
import { addQuestion, updateQuestion } from "@/api/training/question";
import { listPost } from "@/api/training/post";
import { getPostModelConfig } from "@/api/training/postConfig";
import { Plus, Delete } from '@element-plus/icons-vue';

const { proxy } = getCurrentInstance();

const props = defineProps({
  bankId: {
    type: [String, Number],
    default: null
  }
});

const emit = defineEmits(['refresh']);

// 响应式数据
const open = ref(false);
const title = ref("");
const selectedType = ref("0");
const currentQuestionIndex = ref(0);
const questionList = ref([]);
const postList = ref([]);
const abilityOptions = ref([]);

// 级联选择器配置
const cascaderProps = {
  value: 'value',
  label: 'label',
  children: 'children',
  disabled: 'disabled'
};

// 计算属性
const currentQuestion = computed(() => {
  if (questionList.value.length > 0 && currentQuestionIndex.value >= 0) {
    return questionList.value[currentQuestionIndex.value];
  }
  return null;
});

// 方法
const selectQuestionType = (type) => {
  selectedType.value = type;
  const newQuestion = {
    questionType: type,
    questionStem: '',
    options: [
      { value: '' },
      { value: '' }
    ],
    abilityCascader: [],
    postId: null,
    abilityId: null
  };
  questionList.value.push(newQuestion);
  currentQuestionIndex.value = questionList.value.length - 1;
};

const selectQuestion = (index) => {
  currentQuestionIndex.value = index;
  selectedType.value = questionList.value[index].questionType;
};

const deleteQuestion = (index) => {
  if (questionList.value.length > 1) {
    questionList.value.splice(index, 1);
    if (currentQuestionIndex.value >= questionList.value.length) {
      currentQuestionIndex.value = questionList.value.length - 1;
    }
  }
};

const addOption = () => {
  if (currentQuestion.value) {
    currentQuestion.value.options.push({ value: '' });
  }
};

const removeOption = (index) => {
  if (currentQuestion.value && currentQuestion.value.options.length > 2) {
    currentQuestion.value.options.splice(index, 1);
  }
};

const getPostList = async () => {
  try {
    const response = await listPost({});
    postList.value = response.rows || [];
    buildAbilityOptions();
  } catch (error) {
    console.error('获取岗位列表失败:', error);
  }
};

const buildAbilityOptions = async () => {
  abilityOptions.value = [];
  
  for (const post of postList.value) {
    try {
      const response = await getPostModelConfig(post.postId);
      const postConfig = response.data;
      
      if (postConfig && postConfig.abilityConfigs && postConfig.abilityConfigs.length > 0) {
        abilityOptions.value.push({
          value: post.postId,
          label: post.postName,
          children: postConfig.abilityConfigs.map(abilityConfig => ({
            value: abilityConfig.abilityId,
            label: abilityConfig.abilityName
          }))
        });
      } else {
        // 即使没有配置职业能力，也显示岗位（但不可选）
        abilityOptions.value.push({
          value: post.postId,
          label: post.postName,
          children: [],
          disabled: true
        });
      }
    } catch (error) {
      console.error(`获取岗位 ${post.postName} 的职业能力失败:`, error);
      // 出错时也显示岗位但不可选
      abilityOptions.value.push({
        value: post.postId,
        label: post.postName,
        children: [],
        disabled: true
      });
    }
  }
};

const handleAbilityChange = (value) => {
  if (currentQuestion.value && value && value.length === 2) {
    currentQuestion.value.postId = value[0];
    currentQuestion.value.abilityId = value[1];
  } else {
    currentQuestion.value.postId = null;
    currentQuestion.value.abilityId = null;
  }
};

const reset = () => {
  questionList.value = [];
  currentQuestionIndex.value = 0;
  selectedType.value = "0";
};

const handleAdd = () => {
  reset();
  open.value = true;
  title.value = "新建题目";
  // 不自动添加题目，保持空状态
};

const handleUpdate = (row) => {
  reset();
  open.value = true;
  title.value = "编辑题目";
  
  // 解析选项
  let options = [];
  try {
    const optionsArray = JSON.parse(row.options);
    options = optionsArray.map(option => ({ value: option }));
  } catch (e) {
    options = [
      { value: '' },
      { value: '' }
    ];
  }
  
  // 构建级联选择器值
  const abilityCascader = [];
  if (row.postId) {
    abilityCascader.push(row.postId);
    if (row.abilityId) {
      abilityCascader.push(row.abilityId);
    }
  }
  
  const question = {
    questionId: row.questionId,
    questionType: row.questionType,
    questionStem: row.questionStem,
    options: options,
    postId: row.postId,
    abilityId: row.abilityId,
    abilityCascader: abilityCascader
  };
  
  questionList.value = [question];
  currentQuestionIndex.value = 0;
  selectedType.value = row.questionType;
};

const submitForm = async () => {
  // 验证所有题目
  for (let i = 0; i < questionList.value.length; i++) {
    const question = questionList.value[i];
    
    if (!question.questionStem.trim()) {
      proxy.$modal.msgError(`第${i + 1}题的题干不能为空`);
      return;
    }
    
    const validOptions = question.options.filter(option => option.value.trim() !== '');
    if (validOptions.length < 2) {
      proxy.$modal.msgError(`第${i + 1}题至少需要2个选项`);
      return;
    }
  }
  
  // 保存所有题目
  try {
    for (const question of questionList.value) {
      const data = {
        bankId: props.bankId,
        questionStem: question.questionStem,
        questionType: question.questionType,
        options: JSON.stringify(question.options.map(option => option.value)),
        postId: question.postId,
        abilityId: question.abilityId,
        status: "0"
      };
      
      if (question.questionId) {
        data.questionId = question.questionId;
        await updateQuestion(data);
      } else {
        await addQuestion(data);
      }
    }
    
    proxy.$modal.msgSuccess("保存成功");
    open.value = false;
    emit('refresh');
  } catch (error) {
    console.error('保存题目失败:', error);
    proxy.$modal.msgError("保存失败");
  }
};

const cancel = () => {
  open.value = false;
  reset();
};

// 初始化
onMounted(() => {
  getPostList();
});

// 暴露方法给父组件
defineExpose({
  handleAdd,
  handleUpdate
});
</script>

<style scoped>
.question-dialog-container {
  display: flex;
  gap: 20px;
  height: 500px;
}

.left-panel {
  width: 250px;
  background: #F5F5F5;
  display: flex;
  flex-direction: column;
  padding: 20px;
}

.question-type-buttons {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 12px;
  margin-bottom: 20px;
}

.divider-vertical {
  height: 20px;
  width: 1px;
  background-color: #D3D3D3;
  margin: 0 10px;
}

.type-btn {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 8px;
  cursor: pointer;
  transition: all 0.3s ease;
  font-size: 14px;
  color: #606266;
}


.type-btn .el-icon {
  font-size: 16px;
}

.type-btn span {
  font-weight: 500;
}

.question-list {
  flex: 1;
  overflow-y: auto;
}

.question-item {
  display: flex;
  align-items: center;
  padding: 8px 12px;
  margin-bottom: 8px;
  border: 1px solid #e4e7ed;
  border-radius: 4px;
  cursor: pointer;
  transition: all 0.3s;
}

.question-item:hover {
  border-color: #409eff;
}

.question-item.active {
  background-color: #409eff;
  color: white;
  border-color: #409eff;
}

.question-item.active .delete-btn {
  color: white;
}

.question-number {
  font-weight: bold;
  margin-right: 8px;
}

.question-type-text {
  flex: 1;
  font-size: 12px;
}

.delete-btn {
  opacity: 0;
  transition: opacity 0.3s;
}

.question-item:hover .delete-btn,
.question-item.active .delete-btn {
  opacity: 1;
}

.right-panel {
  flex: 1;
  display: flex;
  flex-direction: column;
}

.config-header {
  margin-bottom: 20px;
}

.config-header h3 {
  margin: 0 0 8px 0;
  font-size: 16px;
  font-weight: 600;
}

.divider {
  height: 1px;
  width: 100%;
  background: repeating-linear-gradient(to right, #e4e7ed 0, #e4e7ed 4px, transparent 4px, transparent 8px);
}

.config-content {
  flex: 1;
  display: flex;
  flex-direction: column;
  gap: 20px;
}

.form-item {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.form-label {
  font-weight: 500;
  color: #606266;
}

.form-input {
  width: 100%;
}

.options-container {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.add-option-btn {
  align-self: flex-start;
}

.option-item {
  display: flex;
  align-items: center;
  gap: 8px;
}

.option-input {
  flex: 1;
}

.delete-option-btn {
  flex-shrink: 0;
}

.empty-config {
  flex: 1;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  gap: 20px;
}

.empty-illustration {
  position: relative;
  width: 120px;
  height: 120px;
}



.empty-text {
  color: #909399;
  font-size: 14px;
}

.empty-buttons {
  display: flex;
  gap: 12px;
}

.empty-buttons .type-btn {
  border-color: #409eff;
}

.empty-buttons .type-btn:hover {
  background-color: #66b1ff;
  border-color: #66b1ff;
  color: white;
}

.dialog-footer {
  text-align: right;
}
</style> 