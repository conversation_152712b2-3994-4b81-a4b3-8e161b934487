<template>
  <el-dialog :title="title" v-model="open" width="500px" append-to-body>
    <el-form ref="bankRef" :model="form" :rules="rules" label-width="120px">
      <el-form-item label="题库名称" prop="bankName">
        <el-input v-model="form.bankName" placeholder="请输入题库名称" maxlength="12" show-word-limit />
      </el-form-item>
    </el-form>
    <template #footer>
      <div class="dialog-footer">
        <el-button type="primary" @click="submitForm">确 定</el-button>
        <el-button @click="cancel">取 消</el-button>
      </div>
    </template>
  </el-dialog>
</template>

<script setup name="BankDialog">
import { addBank, updateBank, getBank } from "@/api/training/bank";

const { proxy } = getCurrentInstance();

const data = reactive({
  form: {},
  rules: {
    bankName: [
      { required: true, message: "题库名称不能为空", trigger: "blur" },
      { min: 1, max: 12, message: "题库名称长度必须介于 1 和 12 之间", trigger: "blur" }
    ]
  }
});

const { form, rules } = toRefs(data);

/** 弹出层标题 */
const title = ref("");
/** 是否显示弹出层 */
const open = ref(false);

const emit = defineEmits(['refresh']);

// 取消按钮
const cancel = () => {
  open.value = false;
  reset();
};

// 表单重置
const reset = () => {
  form.value = {
    bankId: null,
    bankName: null,
    status: "0"
  };
  proxy.resetForm("bankRef");
};

/** 新增按钮操作 */
const handleAdd = () => {
  reset();
  open.value = true;
  title.value = "新增评价模型题库";
};

/** 修改按钮操作 */
const handleUpdate = (row) => {
  reset();
  const bankId = row.bankId;
  getBank(bankId).then(response => {
    form.value = response.data;
    open.value = true;
    title.value = "编辑评价模型题库";
  });
};

/** 提交按钮 */
const submitForm = () => {
  proxy.$refs["bankRef"].validate(valid => {
    if (valid) {
      if (form.value.bankId != null) {
        updateBank(form.value).then(response => {
          proxy.$modal.msgSuccess("修改成功");
          open.value = false;
          emit('refresh');
        });
      } else {
        addBank(form.value).then(response => {
          proxy.$modal.msgSuccess("新增成功");
          open.value = false;
          emit('refresh');
        });
      }
    }
  });
};

// 暴露方法给父组件
defineExpose({
  handleAdd,
  handleUpdate
});
</script> 