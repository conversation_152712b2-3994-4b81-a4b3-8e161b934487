<template>
  <el-dialog
    :title="title"
    v-model="visible"
    :width="width"
    append-to-body
    @close="handleClose"
  >
    <!-- 覆盖数据选项 -->
    <div class="overwrite-section" style="margin-bottom: 20px;">
      <span style="color: #f56c6c; margin-right: 8px;">*</span>
      <span style="font-weight: 500;">覆盖数据：</span>
      <el-radio-group v-model="overwriteData" style="margin-left: 10px;">
        <el-radio :label="false">否</el-radio>
        <el-radio :label="true">是</el-radio>
      </el-radio-group>
    </div>

    <!-- 文件上传区域 -->
    <div class="file-upload-section">
      <span style="color: #f56c6c; margin-right: 8px;">*</span>
      <span style="font-weight: 500;">文件上传：</span>
      <el-link
        type="primary"
        :underline="false"
        style="margin-left: 10px; font-size: 14px;"
        @click="handleDownloadTemplate"
        v-if="templateUrl"
      >
        📥 下载模板
      </el-link>
    </div>

    <el-upload
      ref="uploadRef"
      :limit="1"
      :accept="accept"
      :headers="headers"
      :action="uploadUrlWithParams"
      :disabled="isUploading"
      :on-progress="handleFileUploadProgress"
      :on-success="handleFileSuccess"
      :on-error="handleFileError"
      :before-upload="handleBeforeUpload"
      :auto-upload="false"
      drag
      style="margin-top: 15px;"
    >
      <el-icon class="el-icon--upload"><upload-filled /></el-icon>
      <div class="el-upload__text">
        📁 上传文件
      </div>
      <div class="el-upload__text">
        将文件拖到此处，或<em>点击上传</em>
      </div>
      <template #tip>
        <div class="el-upload__tip text-center">
          <span>{{ fileTypeText }}</span>
        </div>
      </template>
    </el-upload>

    <template #footer>
      <div class="dialog-footer">
        <el-button @click="handleCancel">取消</el-button>
        <el-button type="primary" @click="handleConfirm" :loading="isUploading">
          {{ isUploading ? '导入中...' : '确认' }}
        </el-button>
      </div>
    </template>
  </el-dialog>
</template>

<script setup name="ImportDialog">
import { ref, computed, watch } from 'vue'
import { ElMessage } from 'element-plus'
import { UploadFilled } from '@element-plus/icons-vue'
import { getToken } from '@/utils/auth'
import { validateExcelFile } from '@/utils/import'

const props = defineProps({
  // 是否显示弹窗
  modelValue: {
    type: Boolean,
    default: false
  },
  // 弹窗标题
  title: {
    type: String,
    default: '导入'
  },
  // 弹窗宽度
  width: {
    type: String,
    default: '500px'
  },
  // 导入接口URL
  importUrl: {
    type: String,
    required: true
  },
  // 模板下载URL
  templateUrl: {
    type: String,
    default: ''
  },
  // 接受的文件类型
  accept: {
    type: String,
    default: '.xlsx,.xls'
  },
  // 文件类型提示文本
  fileTypeText: {
    type: String,
    default: '仅允许导入xls、xlsx格式文件。'
  },
  // 最大文件大小(MB)
  maxFileSize: {
    type: Number,
    default: 10
  },
  // 是否显示导入说明
  showInstructions: {
    type: Boolean,
    default: true
  },
  // 额外的上传参数
  uploadData: {
    type: Object,
    default: () => ({})
  }
})

const emit = defineEmits(['update:modelValue', 'success', 'error', 'download-template'])

// 响应式数据
const visible = ref(false)
const overwriteData = ref(false)
const isUploading = ref(false)
const uploadRef = ref(null)

// 计算属性
const headers = computed(() => ({
  Authorization: "Bearer " + getToken()
}))

const uploadUrl = computed(() => {
  const baseUrl = import.meta.env.VITE_APP_BASE_API
  const url = props.importUrl.startsWith('/') ? props.importUrl : `/${props.importUrl}`
  return `${baseUrl}${url}`
})

const uploadUrlWithParams = computed(() => {
  const baseUrl = uploadUrl.value
  const params = new URLSearchParams()

  // 添加覆盖数据参数
  params.append('overwrite', overwriteData.value)
  params.append('updateSupport', overwriteData.value)

  // 添加额外参数
  Object.keys(props.uploadData).forEach(key => {
    params.append(key, props.uploadData[key])
  })

  return `${baseUrl}?${params.toString()}`
})

// 监听 modelValue 变化
watch(() => props.modelValue, (newVal) => {
  visible.value = newVal
  if (newVal) {
    // 重置状态
    overwriteData.value = false
    isUploading.value = false
  }
})

watch(visible, (newVal) => {
  emit('update:modelValue', newVal)
})

// 方法
const handleClose = () => {
  visible.value = false
}

const handleCancel = () => {
  visible.value = false
}

const handleConfirm = () => {
  if (uploadRef.value) {
    uploadRef.value.submit()
  }
}

const handleDownloadTemplate = () => {
  emit('download-template', props.templateUrl)
}

const handleBeforeUpload = (file) => {
  const validation = validateExcelFile(file, props.maxFileSize)
  if (!validation.valid) {
    ElMessage.error(validation.message)
    return false
  }
  return true
}

const handleFileUploadProgress = () => {
  isUploading.value = true
}

const handleFileSuccess = (response, file) => {
  isUploading.value = false
  visible.value = false

  // 清除文件
  if (uploadRef.value) {
    uploadRef.value.handleRemove(file)
  }

  emit('success', response)
}

const handleFileError = (error, file) => {
  isUploading.value = false

  // 清除文件
  if (uploadRef.value) {
    uploadRef.value.handleRemove(file)
  }

  emit('error', error)
}

// 暴露方法给父组件
defineExpose({
  overwriteData
})
</script>

<style scoped>
.import-instructions {
  margin-bottom: 20px;
  padding: 15px;
  background-color: #f8f9fa;
  border-radius: 6px;
  border-left: 4px solid #409eff;
}

.import-instructions h4 {
  margin: 0 0 10px 0;
  color: #303133;
  font-size: 14px;
  font-weight: 600;
}

.import-instructions ol {
  margin: 0;
  padding-left: 20px;
  color: #606266;
  font-size: 13px;
  line-height: 1.6;
}

.import-instructions li {
  margin-bottom: 5px;
}

.overwrite-section {
  display: flex;
  align-items: center;
  padding: 12px 0;
  border-bottom: 1px solid #ebeef5;
}

.file-upload-section {
  display: flex;
  align-items: center;
  padding: 12px 0;
  margin-bottom: 10px;
}

.el-upload__text {
  color: #606266;
  font-size: 14px;
  text-align: center;
}

.el-upload__text em {
  color: #409eff;
  font-style: normal;
}
</style>