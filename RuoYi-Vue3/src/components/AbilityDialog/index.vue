<template>
  <el-dialog
    v-model="dialogVisible"
    :title="title"
    width="800px"
    :before-close="handleClose"
  >
    <el-form
      ref="formRef"
      :model="form"
      :rules="rules"
      label-width="150px"
    >
      <el-form-item label="职业能力名称" prop="abilityName">
        <el-input 
          v-model="form.abilityName" 
          placeholder="请输入职业能力名称"
          maxlength="12"
          show-word-limit
        />
      </el-form-item>
      <el-form-item label="能力描述" prop="abilityDesc">
        <el-input 
          v-model="form.abilityDesc" 
          type="textarea" 
          placeholder="请输入能力描述"
          :rows="4"
          maxlength="300"
          show-word-limit
        />
      </el-form-item>
      <el-form-item label="达标阈值" prop="thresholdValue">
        <el-input-number 
          v-model="form.thresholdValue" 
          :min="0" 
          :max="100" 
          :step="5"
          style="width: 200px"
        />
        <span style="margin-left: 10px;">分</span>
      </el-form-item>
    </el-form>
    <template #footer>
      <div class="dialog-footer">
        <el-button @click="handleClose">取消</el-button>
        <el-button type="primary" @click="handleSubmit">确定</el-button>
      </div>
    </template>
  </el-dialog>
</template>

<script setup>
import { ref, reactive, watch } from 'vue'
import { ElMessage } from 'element-plus'
import { addAbility, updateAbility } from '@/api/training/ability'

const props = defineProps({
  visible: {
    type: Boolean,
    default: false
  },
  title: {
    type: String,
    default: '新增职业能力'
  },
  abilityData: {
    type: Object,
    default: () => ({})
  }
})

const emit = defineEmits(['update:visible', 'success', 'close'])

const dialogVisible = ref(false)
const formRef = ref()

const form = reactive({
  abilityId: undefined,
  abilityName: undefined,
  abilityDesc: undefined,
  thresholdValue: 60
})

const rules = {
  abilityName: [
    { required: true, message: "职业能力名称不能为空", trigger: "blur" },
    { min: 1, max: 12, message: "职业能力名称长度必须介于 1 和 12 之间", trigger: "blur" }
  ],
  thresholdValue: [
    { required: true, message: "达标阈值不能为空", trigger: "blur" },
    { type: 'number', min: 0, max: 100, message: "达标阈值必须在0-100之间", trigger: "blur" }
  ]
}

// 监听visible变化
watch(() => props.visible, (newVal) => {
  dialogVisible.value = newVal
  if (newVal) {
    resetForm()
    if (props.abilityData && props.abilityData.abilityId) {
      // 编辑模式，填充表单数据
      Object.assign(form, props.abilityData)
    }
  }
})

// 监听dialogVisible变化
watch(dialogVisible, (newVal) => {
  emit('update:visible', newVal)
  if (!newVal) {
    emit('close')
  }
})

// 重置表单
const resetForm = () => {
  Object.assign(form, {
    abilityId: undefined,
    abilityName: undefined,
    abilityDesc: undefined,
    thresholdValue: 60
  })
  if (formRef.value) {
    formRef.value.resetFields()
  }
}

// 关闭弹框
const handleClose = () => {
  dialogVisible.value = false
}

// 提交表单
const handleSubmit = async () => {
  try {
    await formRef.value.validate()
    
    let response
    if (form.abilityId) {
      // 编辑模式
      response = await updateAbility(form)
    } else {
      // 新增模式
      response = await addAbility(form)
    }
    
    if (response.code === 200) {
      const message = form.abilityId ? '修改成功' : '新增成功'
      ElMessage.success(message)
      handleClose()
      emit('success', response.data)
    }
  } catch (error) {
    console.error('操作失败:', error)
    if (error.message) {
      ElMessage.error(error.message)
    } else {
      ElMessage.error('操作失败')
    }
  }
}
</script>

<style scoped>
.dialog-footer {
  text-align: right;
}
</style> 