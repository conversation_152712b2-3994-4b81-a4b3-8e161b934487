import { ref, computed } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'

/**
 * 导入功能组合式函数
 * @param {Object} options 配置选项
 * @param {string} options.importUrl 导入接口URL
 * @param {string} options.templateUrl 模板下载URL
 * @param {string} options.moduleName 模块名称，用于显示
 * @param {Function} options.onSuccess 导入成功回调
 * @param {Function} options.onError 导入失败回调
 * @param {Function} options.downloadTemplate 下载模板函数
 * @returns {Object} 导入相关的响应式数据和方法
 */
export function useImport(options = {}) {
  const {
    importUrl,
    templateUrl,
    moduleName = '数据',
    onSuccess,
    onError,
    downloadTemplate
  } = options

  // 响应式数据
  const importDialogVisible = ref(false)
  const importTitle = computed(() => `${moduleName}导入`)

  // 打开导入对话框
  const openImportDialog = () => {
    importDialogVisible.value = true
  }

  // 关闭导入对话框
  const closeImportDialog = () => {
    importDialogVisible.value = false
  }

  // 处理导入成功
  const handleImportSuccess = (response) => {
    ElMessageBox.alert(
      `<div style='overflow: auto;overflow-x: hidden;max-height: 70vh;padding: 10px 20px 0;'>${response.msg}</div>`,
      "导入结果",
      { 
        dangerouslyUseHTMLString: true,
        confirmButtonText: '确定'
      }
    )
    
    if (onSuccess) {
      onSuccess(response)
    }
  }

  // 处理导入失败
  const handleImportError = (error) => {
    console.error('导入失败:', error)
    
    let errorMessage = '导入失败，请稍后重试'
    if (error.response && error.response.data) {
      const data = error.response.data
      if (data.msg) {
        errorMessage = data.msg
      } else if (data.message) {
        errorMessage = data.message
      }
    } else if (error.message) {
      errorMessage = error.message
    }
    
    ElMessage.error(errorMessage)
    
    if (onError) {
      onError(error)
    }
  }

  // 处理模板下载
  const handleDownloadTemplate = (templateUrl) => {
    if (downloadTemplate) {
      downloadTemplate(templateUrl)
    } else {
      // 默认下载逻辑
      const link = document.createElement('a')
      link.href = templateUrl
      link.download = `${moduleName}_template_${new Date().getTime()}.xlsx`
      document.body.appendChild(link)
      link.click()
      document.body.removeChild(link)
    }
  }

  return {
    // 响应式数据
    importDialogVisible,
    importTitle,
    
    // 方法
    openImportDialog,
    closeImportDialog,
    handleImportSuccess,
    handleImportError,
    handleDownloadTemplate,
    
    // 配置
    importUrl,
    templateUrl
  }
}

/**
 * 创建标准导入配置
 * @param {string} baseUrl 基础URL，如 '/training/ability'
 * @param {string} moduleName 模块名称
 * @param {Function} refreshList 刷新列表函数
 * @param {Function} downloadFn 下载函数
 * @returns {Object} 导入配置对象
 */
export function createImportConfig(baseUrl, moduleName, refreshList, downloadFn) {
  return {
    importUrl: `${baseUrl}/importData`,
    templateUrl: `${baseUrl}/importTemplate`,
    moduleName,
    onSuccess: (response) => {
      if (refreshList) {
        refreshList()
      }
    },
    onError: (error) => {
      console.error(`${moduleName}导入失败:`, error)
    },
    downloadTemplate: (templateUrl) => {
      if (downloadFn) {
        downloadFn(templateUrl, {}, `${moduleName}_template_${new Date().getTime()}.xlsx`)
      }
    }
  }
}

/**
 * 验证导入文件
 * @param {File} file 要验证的文件
 * @param {Object} options 验证选项
 * @param {number} options.maxSize 最大文件大小(MB)，默认10MB
 * @param {Array} options.allowedTypes 允许的文件类型，默认['xls', 'xlsx']
 * @returns {Object} 验证结果 { valid: boolean, message: string }
 */
export function validateImportFile(file, options = {}) {
  const { maxSize = 10, allowedTypes = ['xls', 'xlsx'] } = options
  
  // 检查文件是否存在
  if (!file) {
    return {
      valid: false,
      message: '请选择要导入的文件!'
    }
  }
  
  // 检查文件类型
  const fileName = file.name.toLowerCase()
  const isValidType = allowedTypes.some(type => fileName.endsWith(`.${type}`))
  
  if (!isValidType) {
    return {
      valid: false,
      message: `只能上传 ${allowedTypes.map(type => type.toUpperCase()).join('/')} 文件!`
    }
  }

  // 检查文件大小
  const isLtMaxSize = file.size / 1024 / 1024 < maxSize
  if (!isLtMaxSize) {
    return {
      valid: false,
      message: `上传文件大小不能超过 ${maxSize}MB!`
    }
  }

  // 检查文件名是否包含特殊字符
  if (fileName.includes(',')) {
    return {
      valid: false,
      message: '文件名不能包含英文逗号!'
    }
  }

  return {
    valid: true,
    message: '文件验证通过'
  }
}

/**
 * 格式化导入错误信息
 * @param {Object} error 错误对象
 * @returns {string} 格式化后的错误信息
 */
export function formatImportError(error) {
  if (error.response && error.response.data) {
    const data = error.response.data
    if (data.msg) {
      return data.msg
    }
    if (data.message) {
      return data.message
    }
  }
  
  if (error.message) {
    return error.message
  }
  
  return '导入失败，请稍后重试'
}
