-- ----------------------------
-- 职业能力表
-- ----------------------------
DROP TABLE IF EXISTS `professional_ability`;
CREATE TABLE `professional_ability` (
  `ability_id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '职业能力ID',
  `ability_name` varchar(50) NOT NULL COMMENT '职业能力名称',
  `ability_desc` varchar(500) DEFAULT NULL COMMENT '职业能力描述',
  `threshold_value` int(3) DEFAULT 60 COMMENT '达标阈值',
  `create_by` varchar(64) DEFAULT '' COMMENT '创建者',
  `create_time` datetime DEFAULT NULL COMMENT '创建时间',
  `update_by` varchar(64) DEFAULT '' COMMENT '更新者',
  `update_time` datetime DEFAULT NULL COMMENT '更新时间',
  `remark` varchar(500) DEFAULT NULL COMMENT '备注',
  PRIMARY KEY (`ability_id`)
) ENGINE=InnoDB AUTO_INCREMENT=1 DEFAULT CHARSET=utf8mb4 COMMENT='职业能力表';

-- ----------------------------
-- 初始化职业能力数据
-- ----------------------------
INSERT INTO `professional_ability` VALUES (1, '机械设计师', '具备机械产品设计和开发的专业能力', 60, 'admin', sysdate(), '', null, null);
INSERT INTO `professional_ability` VALUES (2, '电气技术员', '具备电气设备维护和技术支持的专业能力', 60, 'admin', sysdate(), '', null, null);
INSERT INTO `professional_ability` VALUES (3, '软件工程师', '具备软件系统开发和维护的专业能力', 60, 'admin', sysdate(), '', null, null);
INSERT INTO `professional_ability` VALUES (4, '数据分析师', '具备数据分析和业务洞察的专业能力', 60, 'admin', sysdate(), '', null, null);
INSERT INTO `professional_ability` VALUES (5, '质量检测员', '具备产品质量检测和控制的专业能力', 60, 'admin', sysdate(), '', null, null); 