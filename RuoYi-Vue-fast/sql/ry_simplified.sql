/*
 Navicat Premium Data Transfer

 Source Server         : localhost_3306
 Source Server Type    : MySQL
 Source Server Version : 80039
 Source Host           : localhost:3306
 Source Schema         : ry-vue

 Target Server Type    : MySQL
 Target Server Version : 80039
 File Encoding         : 65001

 Date: 29/07/2025 17:00:00
 
 精简版数据库初始化脚本 - 仅保留核心系统管理功能
*/

SET NAMES utf8mb4;
SET FOREIGN_KEY_CHECKS = 0;

-- ----------------------------
-- 1、部门表
-- ----------------------------
drop table if exists sys_dept;
create table sys_dept (
  dept_id           bigint(20)      not null auto_increment    comment '部门id',
  parent_id         bigint(20)      default 0                  comment '父部门id',
  ancestors         varchar(50)     default ''                 comment '祖级列表',
  dept_name         varchar(30)     default ''                 comment '部门名称',
  order_num         int(4)          default 0                  comment '显示顺序',
  leader            varchar(20)     default null               comment '负责人',
  phone             varchar(11)     default null               comment '联系电话',
  email             varchar(50)     default null               comment '邮箱',
  status            char(1)         default '0'                comment '部门状态（0正常 1停用）',
  del_flag          char(1)         default '0'                comment '删除标志（0代表存在 2代表删除）',
  create_by         varchar(64)     default ''                 comment '创建者',
  create_time       datetime                                   comment '创建时间',
  update_by         varchar(64)     default ''                 comment '更新者',
  update_time       datetime                                   comment '更新时间',
  primary key (dept_id)
) engine=innodb auto_increment=200 comment = '部门表';

-- ----------------------------
-- 2、用户信息表
-- ----------------------------
drop table if exists sys_user;
create table sys_user (
  user_id           bigint(20)      not null auto_increment    comment '用户ID',
  dept_id           bigint(20)      default null               comment '部门ID',
  user_name         varchar(30)     not null                   comment '用户账号',
  nick_name         varchar(30)     not null                   comment '用户昵称',
  user_type         varchar(2)      default '00'               comment '用户类型（00系统用户）',
  email             varchar(50)     default ''                 comment '用户邮箱',
  phonenumber       varchar(11)     default ''                 comment '手机号码',
  sex               char(1)         default '0'                comment '用户性别（0男 1女 2未知）',
  avatar            varchar(100)    default ''                 comment '头像地址',
  password          varchar(100)    default ''                 comment '密码',
  status            char(1)         default '0'                comment '帐号状态（0正常 1停用）',
  del_flag          char(1)         default '0'                comment '删除标志（0代表存在 2代表删除）',
  login_ip          varchar(128)    default ''                 comment '最后登录IP',
  login_date        datetime                                   comment '最后登录时间',
  pwd_update_date   datetime                                   comment '密码最后更新时间',
  create_by         varchar(64)     default ''                 comment '创建者',
  create_time       datetime                                   comment '创建时间',
  update_by         varchar(64)     default ''                 comment '更新者',
  update_time       datetime                                   comment '更新时间',
  remark            varchar(500)    default null               comment '备注',
  primary key (user_id)
) engine=innodb auto_increment=100 comment = '用户信息表';

-- ----------------------------
-- 3、角色信息表
-- ----------------------------
drop table if exists sys_role;
create table sys_role (
  role_id              bigint(20)      not null auto_increment    comment '角色ID',
  role_name            varchar(30)     not null                   comment '角色名称',
  role_key             varchar(100)    not null                   comment '角色权限字符串',
  role_sort            int(4)          not null                   comment '显示顺序',
  data_scope           char(1)         default '1'                comment '数据范围（1：全部数据权限 2：自定数据权限 3：本部门数据权限 4：本部门及以下数据权限）',
  menu_check_strictly  tinyint(1)      default 1                  comment '菜单树选择项是否关联显示',
  dept_check_strictly  tinyint(1)      default 1                  comment '部门树选择项是否关联显示',
  status               char(1)         not null                   comment '角色状态（0正常 1停用）',
  del_flag             char(1)         default '0'                comment '删除标志（0代表存在 2代表删除）',
  create_by            varchar(64)     default ''                 comment '创建者',
  create_time          datetime                                   comment '创建时间',
  update_by            varchar(64)     default ''                 comment '更新者',
  update_time          datetime                                   comment '更新时间',
  remark               varchar(500)    default null               comment '备注',
  primary key (role_id)
) engine=innodb auto_increment=100 comment = '角色信息表';

-- ----------------------------
-- 4、菜单权限表
-- ----------------------------
drop table if exists sys_menu;
create table sys_menu (
  menu_id           bigint(20)      not null auto_increment    comment '菜单ID',
  menu_name         varchar(50)     not null                   comment '菜单名称',
  parent_id         bigint(20)      default 0                  comment '父菜单ID',
  order_num         int(4)          default 0                  comment '显示顺序',
  path              varchar(200)    default ''                 comment '路由地址',
  component         varchar(255)    default null               comment '组件路径',
  query             varchar(255)    default null               comment '路由参数',
  route_name        varchar(50)     default ''                 comment '路由名称',
  is_frame          int(1)          default 1                  comment '是否为外链（0是 1否）',
  is_cache          int(1)          default 0                  comment '是否缓存（0缓存 1不缓存）',
  menu_type         char(1)         default ''                 comment '菜单类型（M目录 C菜单 F按钮）',
  visible           char(1)         default 0                  comment '菜单状态（0显示 1隐藏）',
  status            char(1)         default 0                  comment '菜单状态（0正常 1停用）',
  perms             varchar(100)    default null               comment '权限标识',
  icon              varchar(100)    default '#'                comment '菜单图标',
  create_by         varchar(64)     default ''                 comment '创建者',
  create_time       datetime                                   comment '创建时间',
  update_by         varchar(64)     default ''                 comment '更新者',
  update_time       datetime                                   comment '更新时间',
  remark            varchar(500)    default ''                 comment '备注',
  primary key (menu_id)
) engine=innodb auto_increment=2000 comment = '菜单权限表';

-- ----------------------------
-- 5、用户和角色关联表  用户N-1角色
-- ----------------------------
drop table if exists sys_user_role;
create table sys_user_role (
  user_id   bigint(20) not null comment '用户ID',
  role_id   bigint(20) not null comment '角色ID',
  primary key(user_id, role_id)
) engine=innodb comment = '用户和角色关联表';

-- ----------------------------
-- 6、角色和菜单关联表  角色1-N菜单
-- ----------------------------
drop table if exists sys_role_menu;
create table sys_role_menu (
  role_id   bigint(20) not null comment '角色ID',
  menu_id   bigint(20) not null comment '菜单ID',
  primary key(role_id, menu_id)
) engine=innodb comment = '角色和菜单关联表';

-- ----------------------------
-- 7、角色和部门关联表  角色1-N部门
-- ----------------------------
drop table if exists sys_role_dept;
create table sys_role_dept (
  role_id   bigint(20) not null comment '角色ID',
  dept_id   bigint(20) not null comment '部门ID',
  primary key(role_id, dept_id)
) engine=innodb comment = '角色和部门关联表';

-- ----------------------------
-- 8、字典类型表
-- ----------------------------
drop table if exists sys_dict_type;
create table sys_dict_type (
  dict_id          bigint(20)      not null auto_increment    comment '字典主键',
  dict_type        varchar(100)    default ''                 comment '字典编号',
  dict_name        varchar(100)    default ''                 comment '字典名称',
  status           char(1)         default '0'                comment '状态（0正常 1停用）',
  remark           varchar(500)    default null               comment '备注',
  create_by        varchar(64)     default ''                 comment '创建者',
  create_time      datetime                                   comment '创建时间',
  update_by        varchar(64)     default ''                 comment '更新者',
  update_time      datetime                                   comment '更新时间',
  primary key (dict_id),
  unique (dict_type)
) engine=innodb auto_increment=100 comment = '字典类型表';

-- ----------------------------
-- 9、字典数据表
-- ----------------------------
drop table if exists sys_dict_data;
create table sys_dict_data (
  dict_code        bigint(20)      not null auto_increment    comment '字典编码',
  dict_value       varchar(100)    default ''                 comment '字典键值',
  dict_type        varchar(100)    default ''                 comment '字典类型',
  primary key (dict_code)
) engine=innodb auto_increment=100 comment = '字典数据表';

-- ----------------------------
-- 初始化-字典类型表数据
-- ----------------------------
insert into sys_dict_type values(100, 'organization_type', '机构类型', '0', '机构类型字典', 'admin', sysdate(), 'admin', sysdate());
insert into sys_dict_type values(101, 'resource_category', '资源类别', '0', '资源类别字典', 'admin', sysdate(), 'admin', sysdate());
insert into sys_dict_type values(102, 'message_type', '消息类型', '0', '消息类型字典', 'admin', sysdate(), 'admin', sysdate());

-- ----------------------------
-- 初始化-字典数据表数据
-- ----------------------------
-- 机构类型
insert into sys_dict_data values(100, '学校', 'organization_type');
insert into sys_dict_data values(101, '理事会', 'organization_type');
insert into sys_dict_data values(102, '中心', 'organization_type');
insert into sys_dict_data values(103, '教务处', 'organization_type');
insert into sys_dict_data values(104, '学院', 'organization_type');

-- 资源类别
insert into sys_dict_data values(200, '教材', 'resource_category');
insert into sys_dict_data values(201, '设备', 'resource_category');
insert into sys_dict_data values(202, '软件', 'resource_category');
insert into sys_dict_data values(203, '课件', 'resource_category');
insert into sys_dict_data values(204, '资料', 'resource_category');

-- 消息类型
insert into sys_dict_data values(300, '通知公告', 'message_type');
insert into sys_dict_data values(301, '规章制度', 'message_type');

-- ----------------------------
-- 初始化-部门表数据
-- ----------------------------
insert into sys_dept values(100,  0,   '0',          '若依科技',   0, '若依', '15888888888', '<EMAIL>', '0', '0', 'admin', sysdate(), '', null);
insert into sys_dept values(101,  100, '0,100',      '深圳总公司', 1, '若依', '15888888888', '<EMAIL>', '0', '0', 'admin', sysdate(), '', null);
insert into sys_dept values(102,  100, '0,100',      '长沙分公司', 2, '若依', '15888888888', '<EMAIL>', '0', '0', 'admin', sysdate(), '', null);
insert into sys_dept values(103,  101, '0,100,101',  '研发部门',   1, '若依', '15888888888', '<EMAIL>', '0', '0', 'admin', sysdate(), '', null);
insert into sys_dept values(104,  101, '0,100,101',  '市场部门',   2, '若依', '15888888888', '<EMAIL>', '0', '0', 'admin', sysdate(), '', null);
insert into sys_dept values(105,  101, '0,100,101',  '测试部门',   3, '若依', '15888888888', '<EMAIL>', '0', '0', 'admin', sysdate(), '', null);
insert into sys_dept values(106,  101, '0,100,101',  '财务部门',   4, '若依', '15888888888', '<EMAIL>', '0', '0', 'admin', sysdate(), '', null);
insert into sys_dept values(107,  101, '0,100,101',  '运维部门',   5, '若依', '15888888888', '<EMAIL>', '0', '0', 'admin', sysdate(), '', null);
insert into sys_dept values(108,  102, '0,100,102',  '市场部门',   1, '若依', '15888888888', '<EMAIL>', '0', '0', 'admin', sysdate(), '', null);
insert into sys_dept values(109,  102, '0,100,102',  '财务部门',   2, '若依', '15888888888', '<EMAIL>', '0', '0', 'admin', sysdate(), '', null);

-- ----------------------------
-- 初始化-用户信息表数据
-- ----------------------------
insert into sys_user values(1,  103, 'admin', '若依', '00', '<EMAIL>', '15888888888', '1', '', '$2a$10$7JB720yubVSZvUI0rEqK/.VqGOZTH.ulu33dHOiBE8ByOhJIrdAu2', '0', '0', '127.0.0.1', sysdate(), null, 'admin', sysdate(), '', null, '管理员');
insert into sys_user values(2,  105, 'ry',    '若依', '00', '<EMAIL>',  '15666666666', '1', '', '$2a$10$7JB720yubVSZvUI0rEqK/.VqGOZTH.ulu33dHOiBE8ByOhJIrdAu2', '0', '0', '127.0.0.1', sysdate(), null, 'admin', sysdate(), '', null, '测试员');

-- ----------------------------
-- 初始化-角色信息表数据
-- ----------------------------
insert into sys_role values('1', '超级管理员',  'admin',  1, 1, 1, 1, '0', '0', 'admin', sysdate(), '', null, '超级管理员');
insert into sys_role values('2', '普通角色',    'common', 2, 2, 1, 1, '0', '0', 'admin', sysdate(), '', null, '普通角色');

-- ----------------------------
-- 初始化-菜单信息表数据
-- ----------------------------
-- 一级菜单
insert into sys_menu values('1', '系统管理', '0', '1', 'system', null, '', '', 1, 0, 'M', '0', '0', '', 'system', 'admin', sysdate(), '', null, '系统管理目录');
insert into sys_menu values('2', '学生(学徒)画像', '0', '2', 'profile', null, '', '', 1, 0, 'M', '0', '0', '', 'chart', 'admin', sysdate(), '', null, '学生(学徒)画像目录');
insert into sys_menu values('3', '学生管理', '0', '3', 'student', null, '', '', 1, 0, 'M', '0', '0', '', 'peoples', 'admin', sysdate(), '', null, '学生管理目录');
insert into sys_menu values('4', '人才培养过程管理', '0', '4', 'training', null, '', '', 1, 0, 'M', '0', '0', '', 'education', 'admin', sysdate(), '', null, '人才培养过程管理目录');
insert into sys_menu values('5', '课程管理', '0', '5', 'course', null, '', '', 1, 0, 'M', '0', '0', '', 'education', 'admin', sysdate(), '', null, '课程管理目录');
insert into sys_menu values('6', '标准制度管理', '0', '6', 'standard', null, '', '', 1, 0, 'M', '0', '0', '', 'monitor', 'admin', sysdate(), '', null, '标准制度管理目录');
insert into sys_menu values('7', '培训项目管理', '0', '7', 'project', null, '', '', 1, 0, 'M', '0', '0', '', 'build', 'admin', sysdate(), '', null, '培训项目管理目录');
insert into sys_menu values('8', '机构管理', '0', '8', 'organization', null, '', '', 1, 0, 'M', '0', '0', '', 'tree', 'admin', sysdate(), '', null, '机构管理目录');
insert into sys_menu values('9', '信息管理', '0', '9', 'information', null, '', '', 1, 0, 'M', '0', '0', '', 'message', 'admin', sysdate(), '', null, '信息管理目录');

-- 系统管理二级菜单
insert into sys_menu values('100',  '用户管理', '1',   '1', 'user',       'system/user/index',        '', '', 1, 0, 'C', '0', '0', 'system:user:list',        '',          'admin', sysdate(), '', null, '用户管理菜单');
insert into sys_menu values('101',  '角色管理', '1',   '2', 'role',       'system/role/index',        '', '', 1, 0, 'C', '0', '0', 'system:role:list',        '',       'admin', sysdate(), '', null, '角色管理菜单');
insert into sys_menu values('102',  '菜单管理', '1',   '3', 'menu',       'system/menu/index',        '', '', 1, 0, 'C', '0', '0', 'system:menu:list',        '',    'admin', sysdate(), '', null, '菜单管理菜单');
insert into sys_menu values('103',  '部门管理', '1',   '4', 'dept',       'system/dept/index',        '', '', 1, 0, 'C', '0', '0', 'system:dept:list',        '',          'admin', sysdate(), '', null, '部门管理菜单');
insert into sys_menu values('105',  '字典管理', '1',   '5', 'dict',       'system/dict/index',        '', '', 1, 0, 'C', '0', '0', 'system:dict:list',        '',          'admin', sysdate(), '', null, '字典管理菜单');

-- 学生(学徒)画像二级菜单
insert into sys_menu values('200',  '群体画像管理', '2',   '1', 'group-profile',       'profile/group/index',        '', '', 1, 0, 'C', '0', '0', 'profile:group:list',        '',          'admin', sysdate(), '', null, '群体画像管理菜单');
insert into sys_menu values('201',  '个人画像管理', '2',   '2', 'personal-profile',       'profile/personal/index',        '', '', 1, 0, 'C', '0', '0', 'profile:personal:list',        '',          'admin', sysdate(), '', null, '个人画像管理菜单');
insert into sys_menu values('202',  '评价模型构建', '2',   '3', 'evaluation-model',       'profile/evaluation/index',        '', '', 1, 0, 'C', '0', '0', 'profile:evaluation:list',        '',          'admin', sysdate(), '', null, '评价模型构建菜单');

-- 学生管理二级菜单
insert into sys_menu values('300',  '学生信息管理', '3',   '1', 'student-info',       'student/info/index',        '', '', 1, 0, 'C', '0', '0', 'student:info:list',        '',          'admin', sysdate(), '', null, '学生信息管理菜单');
insert into sys_menu values('301',  '合同信息管理', '3',   '2', 'contract-info',       'student/contract/index',        '', '', 1, 0, 'C', '0', '0', 'student:contract:list',        '',          'admin', sysdate(), '', null, '合同信息管理菜单');

-- 人才培养过程管理二级菜单
insert into sys_menu values('400',  '岗位模型管理', '4',   '1', 'post-model',       'training/post/index',        '', '', 1, 0, 'C', '0', '0', 'training:post:list',        '',          'admin', sysdate(), '', null, '岗位模型管理菜单');
insert into sys_menu values('401',  '职业能力管理', '4',   '2', 'ability',       'training/ability/index',        '', '', 1, 0, 'C', '0', '0', 'training:ability:list',        '',          'admin', sysdate(), '', null, '职业能力管理菜单');
insert into sys_menu values('402',  '人才培养方案管理', '4',   '3', 'training-program',       'training/program/index',        '', '', 1, 0, 'C', '0', '0', 'training:program:list',        '',          'admin', sysdate(), '', null, '人才培养方案管理菜单');

-- 课程管理二级菜单
insert into sys_menu values('500',  '学校课程管理', '5',   '1', 'school-course',       'course/school/index',        '', '', 1, 0, 'C', '0', '0', 'course:school:list',        '',          'admin', sysdate(), '', null, '学校课程管理菜单');
insert into sys_menu values('501',  '企业课程管理', '5',   '2', 'company-course',       'course/company/index',        '', '', 1, 0, 'C', '0', '0', 'course:company:list',        '',          'admin', sysdate(), '', null, '企业课程管理菜单');

-- 标准制度管理二级菜单
insert into sys_menu values('600',  '课程标准管理', '6',   '1', 'course-standard',       'standard/course/index',        '', '', 1, 0, 'C', '0', '0', 'standard:course:list',        '',          'admin', sysdate(), '', null, '课程标准管理菜单');
insert into sys_menu values('601',  '制度管理', '6',   '2', 'regulation',       'standard/regulation/index',        '', '', 1, 0, 'C', '0', '0', 'standard:regulation:list',        '',          'admin', sysdate(), '', null, '制度管理菜单');

-- 培训项目管理二级菜单
insert into sys_menu values('700',  '培训项目管理', '7',   '1', 'training-project',       'project/training/index',        '', '', 1, 0, 'C', '0', '0', 'project:training:list',        '',          'admin', sysdate(), '', null, '培训项目管理菜单');
insert into sys_menu values('701',  '培训资源管理', '7',   '2', 'training-resource',       'project/resource/index',        '', '', 1, 0, 'C', '0', '0', 'project:resource:list',        '',          'admin', sysdate(), '', null, '培训资源管理菜单');
insert into sys_menu values('702',  '培训模式管理', '7',   '3', 'training-mode',       'project/mode/index',        '', '', 1, 0, 'C', '0', '0', 'project:mode:list',        '',          'admin', sysdate(), '', null, '培训模式管理菜单');

-- 机构管理二级菜单
insert into sys_menu values('800',  '专业管理', '8',   '1', 'major',       'organization/major/index',        '', '', 1, 0, 'C', '0', '0', 'organization:major:list',        '',          'admin', sysdate(), '', null, '专业管理菜单');
insert into sys_menu values('801',  '教师管理', '8',   '2', 'teacher',       'organization/teacher/index',        '', '', 1, 0, 'C', '0', '0', 'organization:teacher:list',        '',          'admin', sysdate(), '', null, '教师管理菜单');
insert into sys_menu values('802',  '企业管理', '8',   '3', 'company',       'organization/company/index',        '', '', 1, 0, 'C', '0', '0', 'organization:company:list',        '',          'admin', sysdate(), '', null, '企业管理菜单');
insert into sys_menu values('803',  '组织人员管理', '8',   '4', 'org-user',       'organization/user/index',        '', '', 1, 0, 'C', '0', '0', 'organization:user:list',        '',          'admin', sysdate(), '', null, '组织人员管理菜单');
insert into sys_menu values('804',  '组织架构管理', '8',   '5', 'org-structure',       'organization/structure/index',        '', '', 1, 0, 'C', '0', '0', 'organization:structure:list',        '',          'admin', sysdate(), '', null, '组织架构管理菜单');

-- 信息管理二级菜单
insert into sys_menu values('900',  '通知公告管理', '9',   '1', 'notice',       'information/notice/index',        '', '', 1, 0, 'C', '0', '0', 'information:notice:list',        '',          'admin', sysdate(), '', null, '通知公告管理菜单');
insert into sys_menu values('901',  '消息中心', '9',   '2', 'message-center',       'information/message/index',        '', '', 1, 0, 'C', '0', '0', 'information:message:list',        '',          'admin', sysdate(), '', null, '消息中心菜单');

-- 注意：已移除按钮级别权限，只保留菜单级别权限控制

-- ----------------------------
-- 初始化-用户和角色关联表数据
-- ----------------------------
insert into sys_user_role values ('1', '1');
insert into sys_user_role values ('2', '2');

-- ----------------------------
-- 初始化-角色和菜单关联表数据（简化版：只关联菜单，不关联按钮）
-- ----------------------------
-- 系统管理菜单
insert into sys_role_menu values ('2', '1');
insert into sys_role_menu values ('2', '100');
insert into sys_role_menu values ('2', '101');
insert into sys_role_menu values ('2', '102');
insert into sys_role_menu values ('2', '103');
insert into sys_role_menu values ('2', '105');

-- 学生(学徒)画像菜单
insert into sys_role_menu values ('2', '2');
insert into sys_role_menu values ('2', '200');
insert into sys_role_menu values ('2', '201');
insert into sys_role_menu values ('2', '202');

-- 学生管理菜单
insert into sys_role_menu values ('2', '3');
insert into sys_role_menu values ('2', '300');
insert into sys_role_menu values ('2', '301');

-- 人才培养过程管理菜单
insert into sys_role_menu values ('2', '4');
insert into sys_role_menu values ('2', '400');
insert into sys_role_menu values ('2', '401');
insert into sys_role_menu values ('2', '402');

-- 课程管理菜单
insert into sys_role_menu values ('2', '5');
insert into sys_role_menu values ('2', '500');
insert into sys_role_menu values ('2', '501');

-- 标准制度管理菜单
insert into sys_role_menu values ('2', '6');
insert into sys_role_menu values ('2', '600');
insert into sys_role_menu values ('2', '601');

-- 培训项目管理菜单
insert into sys_role_menu values ('2', '7');
insert into sys_role_menu values ('2', '700');
insert into sys_role_menu values ('2', '701');
insert into sys_role_menu values ('2', '702');

-- 机构管理菜单
insert into sys_role_menu values ('2', '8');
insert into sys_role_menu values ('2', '800');
insert into sys_role_menu values ('2', '801');
insert into sys_role_menu values ('2', '802');
insert into sys_role_menu values ('2', '803');
insert into sys_role_menu values ('2', '804');

-- 信息管理菜单
insert into sys_role_menu values ('2', '9');
insert into sys_role_menu values ('2', '900');
insert into sys_role_menu values ('2', '901');

SET FOREIGN_KEY_CHECKS = 1;
