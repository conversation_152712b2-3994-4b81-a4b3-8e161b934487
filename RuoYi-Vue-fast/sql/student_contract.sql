-- ----------------------------
-- 学生合同信息表
-- ----------------------------
drop table if exists student_contract;
create table student_contract (
  contract_id          bigint(20)      not null auto_increment    comment '合同ID',
  contract_number      varchar(50)     not null                   comment '合同编号',
  contract_name        varchar(100)    not null                   comment '合同名称',
  sign_date            date            not null                   comment '签约日期',
  student_id           bigint(20)      not null                   comment '学生ID',
  company_name         varchar(200)    not null                   comment '企业名称',
  post_name            varchar(200)    default ''                 comment '岗位名称',
  salary               decimal(10,2)   default null               comment '薪酬',
  create_by            varchar(64)     default ''                 comment '创建者',
  create_time          datetime                                   comment '创建时间',
  update_by            varchar(64)     default ''                 comment '更新者',
  update_time          datetime                                   comment '更新时间',
  remark               varchar(500)    default null               comment '备注',
  primary key (contract_id),
  unique key uk_contract_number (contract_number),
  key idx_student_id (student_id)
) engine=innodb auto_increment=1 comment = '学生合同信息表';

-- ----------------------------
-- 初始化学生合同信息表数据
-- ----------------------------
insert into student_contract values
(1, '20240301001', '培训项目合同', '2024-03-01', 1, '北京科技有限公司', '软件开发工程师', 5000.00, 'admin', sysdate(), '', null, ''),
(2, '20240301002', '实习合同', '2024-03-02', 2, '上海软件有限公司', 'Java开发工程师', 4500.00, 'admin', sysdate(), '', null, ''),
(3, '20240301003', '就业合同', '2024-03-03', 3, '企业1', '前端开发工程师', 5500.00, 'admin', sysdate(), '', null, ''),
(4, '20240301004', '培训合同', '2024-03-04', 4, '企业2', '测试工程师', 4800.00, 'admin', sysdate(), '', null, ''),
(5, '20240301005', '项目合同', '2024-03-05', 5, '企业3', '产品经理', 5200.00, 'admin', sysdate(), '', null, ''); 