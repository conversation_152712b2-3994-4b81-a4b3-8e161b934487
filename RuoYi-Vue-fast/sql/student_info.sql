-- ----------------------------
-- 学生信息表
-- ----------------------------
drop table if exists student_info;
create table student_info (
  student_id           bigint(20)      not null auto_increment    comment '学生ID',
  student_name         varchar(50)     not null                   comment '学生姓名',
  student_number       varchar(50)     not null                   comment '学号',
  class_name           varchar(100)    default ''                 comment '班级',
  gender               char(1)         default '0'                comment '性别（0男 1女）',
  student_status       varchar(20)     default '0'                comment '学生状态（0未面试 1面试失败 2成功签约）',
  school_teacher       varchar(100)    default ''                 comment '校内教师',
  company_teacher      varchar(100)    default ''                 comment '企业导师',
  major                varchar(100)    default ''                 comment '专业',
  target_position      varchar(200)    default ''                 comment '目标岗位',
  resident_company     varchar(200)    default ''                 comment '入驻企业',
  salary               decimal(10,2)   default null               comment '薪酬',
  contract_id          bigint(20)      default null               comment '关联合同ID',
  create_by            varchar(64)     default ''                 comment '创建者',
  create_time          datetime                                   comment '创建时间',
  update_by            varchar(64)     default ''                 comment '更新者',
  update_time          datetime                                   comment '更新时间',
  remark               varchar(500)    default null               comment '备注',
  primary key (student_id),
  unique key uk_student_number (student_number)
) engine=innodb auto_increment=1 comment = '学生信息表';

-- ----------------------------
-- 初始化学生信息表数据
-- ----------------------------
insert into student_info values(1, '王尔康', '2450301537', '24计应五', '0', '2', '葛延宇', '庄国强', '网位名称', '', '', null, 1, 'admin', sysdate(), '', null, '');
insert into student_info values(2, '丁豪谷', '2450301538', '24计应五', '0', '1', '沈咏东', '荀磊', '网位名称', '', '', null, null, 'admin', sysdate(), '', null, '');
insert into student_info values(3, '胡允', '2450301539', '24计应五', '0', '0', '孙旭', '庄国强', '网位名称', '', '', null, null, 'admin', sysdate(), '', null, '');
insert into student_info values(4, '李书君', '2450301540', '24计应五', '0', '0', '康佳炜', '荀磊', '网位名称', '', '', null, null, 'admin', sysdate(), '', null, '');
insert into student_info values(5, '段向懿', '2450301541', '24计应五', '0', '0', '申海元', '庄国强', '网位名称', '', '', null, null, 'admin', sysdate(), '', null, '');
insert into student_info values(6, '赵雨', '2450401234', '24大数据二', '0', '0', '葛延宇', '荀磊', '网位名称', '', '', null, null, 'admin', sysdate(), '', null, '');
insert into student_info values(7, '李怡成', '2450401235', '24大数据二', '0', '0', '沈咏东', '庄国强', '网位名称', '', '', null, null, 'admin', sysdate(), '', null, '');
insert into student_info values(8, '傅彭得', '2450401236', '24大数据二', '0', '0', '孙旭', '荀磊', '网位名称', '', '', null, null, 'admin', sysdate(), '', null, '');
insert into student_info values(9, '刘大大', '2450401238', '24大数据二', '0', '0', '康佳炜', '庄国强', '网位名称', '', '', null, null, 'admin', sysdate(), '', null, '');
insert into student_info values(10, '杨静云', '2450401326', '24大数据二', '0', '0', '申海元', '荀磊', '网位名称', '', '', null, null, 'admin', sysdate(), '', null, ''); 