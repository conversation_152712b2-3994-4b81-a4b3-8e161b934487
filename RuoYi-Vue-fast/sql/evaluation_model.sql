-- ----------------------------
-- 评价模型题库表
-- ----------------------------
DROP TABLE IF EXISTS `evaluation_question_bank`;
CREATE TABLE `evaluation_question_bank` (
  `bank_id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '题库ID',
  `bank_name` varchar(60) NOT NULL COMMENT '题库名称',
  `status` char(1) DEFAULT '0' COMMENT '状态（0正常 1停用）',
  `create_by` varchar(64) DEFAULT '' COMMENT '创建者',
  `create_time` datetime DEFAULT NULL COMMENT '创建时间',
  `update_by` varchar(64) DEFAULT '' COMMENT '更新者',
  `update_time` datetime DEFAULT NULL COMMENT '更新时间',
  `remark` varchar(500) DEFAULT NULL COMMENT '备注',
  PRIMARY KEY (`bank_id`)
) ENGINE=InnoDB AUTO_INCREMENT=1 DEFAULT CHARSET=utf8mb4 COMMENT='评价模型题库表';

-- ----------------------------
-- 初始化评价模型题库数据
-- ----------------------------
INSERT INTO `evaluation_question_bank` VALUES (1, '电子信息', '0', 'admin', sysdate(), '', null, '电子信息相关题目');
INSERT INTO `evaluation_question_bank` VALUES (2, '土木工程', '0', 'admin', sysdate(), '', null, '土木工程相关题目');
INSERT INTO `evaluation_question_bank` VALUES (3, '化工工艺', '0', 'admin', sysdate(), '', null, '化工工艺相关题目');
INSERT INTO `evaluation_question_bank` VALUES (4, '能源动力', '0', 'admin', sysdate(), '', null, '能源动力相关题目');
INSERT INTO `evaluation_question_bank` VALUES (5, '材料科学', '0', 'admin', sysdate(), '', null, '材料科学相关题目');
INSERT INTO `evaluation_question_bank` VALUES (6, '生物工程', '0', 'admin', sysdate(), '', null, '生物工程相关题目');
INSERT INTO `evaluation_question_bank` VALUES (7, '环境科学', '0', 'admin', sysdate(), '', null, '环境科学相关题目');
INSERT INTO `evaluation_question_bank` VALUES (8, '测绘工程', '0', 'admin', sysdate(), '', null, '测绘工程相关题目');

-- ----------------------------
-- 评价模型题目表
-- ----------------------------
DROP TABLE IF EXISTS `evaluation_question`;
CREATE TABLE `evaluation_question` (
  `question_id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '题目ID',
  `bank_id` bigint(20) NOT NULL COMMENT '题库ID',
  `question_stem` varchar(500) NOT NULL COMMENT '题干',
  `question_type` char(1) NOT NULL COMMENT '题目类型（0单选题 1多选题）',
  `options` text NOT NULL COMMENT '选项（JSON格式）',
  `post_id` bigint(20) DEFAULT NULL COMMENT '岗位ID',
  `ability_id` bigint(20) DEFAULT NULL COMMENT '职业能力ID',
  `status` char(1) DEFAULT '0' COMMENT '状态（0正常 1停用）',
  `create_by` varchar(64) DEFAULT '' COMMENT '创建者',
  `create_time` datetime DEFAULT NULL COMMENT '创建时间',
  `update_by` varchar(64) DEFAULT '' COMMENT '更新者',
  `update_time` datetime DEFAULT NULL COMMENT '更新时间',
  `remark` varchar(500) DEFAULT NULL COMMENT '备注',
  PRIMARY KEY (`question_id`),
  KEY `idx_bank_id` (`bank_id`),
  KEY `idx_post_id` (`post_id`),
  KEY `idx_ability_id` (`ability_id`)
) ENGINE=InnoDB AUTO_INCREMENT=1 DEFAULT CHARSET=utf8mb4 COMMENT='评价模型题目表';

-- ----------------------------
-- 初始化评价模型题目数据
-- ----------------------------
INSERT INTO `evaluation_question` VALUES (1, 1, '题干文案超出列表最大长度时，需要显示省略号，并且鼠标悬停时显示完整内容', '0', '["A. 选项A", "B. 选项B", "C. 选项C", "D. 选项D"]', NULL, NULL, '0', 'admin', sysdate(), '', NULL, '');
INSERT INTO `evaluation_question` VALUES (2, 1, '题干文案', '0', '["A. 选项A", "B. 选项B", "C. 选项C", "D. 选项D"]', NULL, NULL, '0', 'admin', sysdate(), '', NULL, '');
INSERT INTO `evaluation_question` VALUES (3, 1, '题干文案', '1', '["A. 选项A", "B. 选项B", "C. 选项C", "D. 选项D"]', NULL, NULL, '0', 'admin', sysdate(), '', NULL, '');
INSERT INTO `evaluation_question` VALUES (4, 1, '题干文案', '0', '["A. 选项A", "B. 选项B", "C. 选项C", "D. 选项D"]', NULL, NULL, '0', 'admin', sysdate(), '', NULL, '');
INSERT INTO `evaluation_question` VALUES (5, 1, '题干文案', '1', '["A. 选项A", "B. 选项B", "C. 选项C", "D. 选项D"]', NULL, NULL, '0', 'admin', sysdate(), '', NULL, '');
INSERT INTO `evaluation_question` VALUES (6, 1, '题干文案', '0', '["A. 选项A", "B. 选项B", "C. 选项C", "D. 选项D"]', NULL, NULL, '0', 'admin', sysdate(), '', NULL, '');
INSERT INTO `evaluation_question` VALUES (7, 1, '题干文案', '1', '["A. 选项A", "B. 选项B", "C. 选项C", "D. 选项D"]', NULL, NULL, '0', 'admin', sysdate(), '', NULL, '');
INSERT INTO `evaluation_question` VALUES (8, 1, '题干文案', '0', '["A. 选项A", "B. 选项B", "C. 选项C", "D. 选项D"]', NULL, NULL, '0', 'admin', sysdate(), '', NULL, '');
INSERT INTO `evaluation_question` VALUES (9, 1, '题干文案', '1', '["A. 选项A", "B. 选项B", "C. 选项C", "D. 选项D"]', NULL, NULL, '0', 'admin', sysdate(), '', NULL, '');
INSERT INTO `evaluation_question` VALUES (10, 1, '题干文案', '0', '["A. 选项A", "B. 选项B", "C. 选项C", "D. 选项D"]', NULL, NULL, '0', 'admin', sysdate(), '', NULL, ''); 