-- ----------------------------
-- 学生考试成绩表
-- ----------------------------
drop table if exists student_exam_score;
create table student_exam_score (
  score_id             bigint(20)      not null auto_increment    comment '成绩ID',
  student_id           bigint(20)      not null                   comment '学生ID',
  course_name          varchar(100)    not null                   comment '课程名称',
  course_type          char(1)         not null default '0'       comment '课程类型（0校内课程 1企业课程）',
  exam_type            varchar(50)     default ''                 comment '考试类型',
  score                decimal(5,2)    not null                   comment '成绩',
  create_by            varchar(64)     default ''                 comment '创建者',
  create_time          datetime                                   comment '创建时间',
  update_by            varchar(64)     default ''                 comment '更新者',
  update_time          datetime                                   comment '更新时间',
  remark               varchar(500)    default null               comment '备注',
  primary key (score_id),
  key idx_student_id (student_id)
) engine=innodb auto_increment=1 comment = '学生考试成绩表';

-- ----------------------------
-- 初始化学生考试成绩表数据
-- ----------------------------
insert into student_exam_score values(1, 1, '数据结构', '0', '期末考试', 90.0, 'admin', sysdate(), '', null, '');
insert into student_exam_score values(2, 1, 'Java程序设计', '0', '期中考试', 85.5, 'admin', sysdate(), '', null, '');
insert into student_exam_score values(3, 1, '企业项目实训', '1', '项目答辩', 92.0, 'admin', sysdate(), '', null, ''); 