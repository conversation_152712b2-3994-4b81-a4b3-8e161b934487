-- ----------------------------
-- 学期表
-- ----------------------------
drop table if exists semester;
create table semester (
  semester_id          bigint(20)      not null auto_increment    comment '学期ID',
  semester_code        varchar(50)     not null                   comment '学期编码（如：2024-2025-1）',
  semester_name        varchar(50)     not null                   comment '学期名称（如：2024-2025-1）',
  primary key (semester_id),
  unique key uk_semester_code (semester_code)
) engine=innodb auto_increment=1 comment = '学期表';

-- ----------------------------
-- 生成学期数据（2021-2022 到 2100-2101）
-- ----------------------------

-- 生成学期数据的存储过程
DELIMITER $$

DROP PROCEDURE IF EXISTS generate_semesters$$

CREATE PROCEDURE generate_semesters()
BEGIN
    DECLARE start_year INT DEFAULT 2021;
    DECLARE end_year INT DEFAULT 2100;
    DECLARE current_year INT;
    DECLARE next_year INT;
    DECLARE semester_code_1 VARCHAR(50);
    DECLARE semester_code_2 VARCHAR(50);
    DECLARE academic_year_str VARCHAR(20);
    
    SET current_year = start_year;
    
    WHILE current_year <= end_year DO
        SET next_year = current_year + 1;
        SET academic_year_str = CONCAT(current_year, '-', next_year);
        SET semester_code_1 = CONCAT(academic_year_str, '-1');
        SET semester_code_2 = CONCAT(academic_year_str, '-2');
        
        -- 插入第一学期
        INSERT INTO semester (semester_code, semester_name) 
        VALUES (semester_code_1, semester_code_1);
        
        -- 插入第二学期
        INSERT INTO semester (semester_code, semester_name) 
        VALUES (semester_code_2, semester_code_2);
        
        SET current_year = current_year + 1;
    END WHILE;
    
END$$

DELIMITER ;

-- 执行存储过程生成数据
CALL generate_semesters();

-- 删除存储过程
DROP PROCEDURE generate_semesters;

-- 查看生成的数据示例
SELECT * FROM semester WHERE semester_code IN ('2021-2022-1', '2021-2022-2', '2024-2025-1', '2024-2025-2', '2099-2100-1', '2099-2100-2') ORDER BY semester_id; 