-- ----------------------------
-- 岗位模型表
-- ----------------------------
DROP TABLE IF EXISTS `post_model`;
CREATE TABLE `post_model` (
  `post_id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '岗位ID',
  `post_name` varchar(50) NOT NULL COMMENT '岗位名称',
  `post_desc` varchar(500) DEFAULT NULL COMMENT '岗位描述',
  `create_by` varchar(64) DEFAULT '' COMMENT '创建者',
  `create_time` datetime DEFAULT NULL COMMENT '创建时间',
  `update_by` varchar(64) DEFAULT '' COMMENT '更新者',
  `update_time` datetime DEFAULT NULL COMMENT '更新时间',
  `remark` varchar(500) DEFAULT NULL COMMENT '备注',
  PRIMARY KEY (`post_id`)
) ENGINE=InnoDB AUTO_INCREMENT=1 DEFAULT CHARSET=utf8mb4 COMMENT='岗位模型表';

-- ----------------------------
-- 岗位模型与企业关联表
-- ----------------------------
DROP TABLE IF EXISTS `post_model_company`;
CREATE TABLE `post_model_company` (
  `post_id` bigint(20) NOT NULL COMMENT '岗位ID',
  `company_id` bigint(20) NOT NULL COMMENT '企业ID',
  PRIMARY KEY (`post_id`, `company_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='岗位模型与企业关联表';

-- ----------------------------
-- 初始化岗位模型数据
-- ----------------------------
INSERT INTO `post_model` VALUES (1, '机械设计师', '负责机械产品的设计和开发工作', 'admin', sysdate(), '', null, null);
INSERT INTO `post_model` VALUES (2, '电气技术员', '负责电气设备的维护和技术支持', 'admin', sysdate(), '', null, null);
INSERT INTO `post_model` VALUES (3, '软件工程师', '负责软件系统的开发和维护', 'admin', sysdate(), '', null, null);
INSERT INTO `post_model` VALUES (4, '数据分析师', '负责数据分析和业务洞察', 'admin', sysdate(), '', null, null);
INSERT INTO `post_model` VALUES (5, '质量检测员', '负责产品质量检测和控制', 'admin', sysdate(), '', null, null);

-- ----------------------------
-- 初始化岗位模型与企业关联数据
-- ----------------------------
INSERT INTO `post_model_company` VALUES (1, 1);
INSERT INTO `post_model_company` VALUES (1, 2);
INSERT INTO `post_model_company` VALUES (2, 2);
INSERT INTO `post_model_company` VALUES (2, 3);
INSERT INTO `post_model_company` VALUES (3, 3);
INSERT INTO `post_model_company` VALUES (3, 4);
INSERT INTO `post_model_company` VALUES (4, 1);
INSERT INTO `post_model_company` VALUES (4, 4);
INSERT INTO `post_model_company` VALUES (4, 5);
INSERT INTO `post_model_company` VALUES (5, 1);
INSERT INTO `post_model_company` VALUES (5, 3);
INSERT INTO `post_model_company` VALUES (5, 5);
