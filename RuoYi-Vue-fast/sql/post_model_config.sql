-- ----------------------------
-- 岗位模型配置表
-- ----------------------------
DROP TABLE IF EXISTS `post_model_config`;
CREATE TABLE `post_model_config` (
  `config_id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '配置ID',
  `post_id` bigint(20) NOT NULL COMMENT '岗位ID',
  `post_name` varchar(50) DEFAULT NULL COMMENT '岗位名称',
  `create_by` varchar(64) DEFAULT '' COMMENT '创建者',
  `create_time` datetime DEFAULT NULL COMMENT '创建时间',
  `update_by` varchar(64) DEFAULT '' COMMENT '更新者',
  `update_time` datetime DEFAULT NULL COMMENT '更新时间',
  `remark` varchar(500) DEFAULT NULL COMMENT '备注',
  PRIMARY KEY (`config_id`),
  UNIQUE KEY `uk_post_id` (`post_id`)
) ENGINE=InnoDB AUTO_INCREMENT=1 DEFAULT CHARSET=utf8mb4 COMMENT='岗位模型配置表';

-- ----------------------------
-- 岗位职业能力配置表
-- ----------------------------
DROP TABLE IF EXISTS `post_ability_config`;
CREATE TABLE `post_ability_config` (
  `config_id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '配置ID',
  `post_id` bigint(20) NOT NULL COMMENT '岗位ID',
  `ability_id` bigint(20) NOT NULL COMMENT '职业能力ID',
  `ability_name` varchar(100) DEFAULT NULL COMMENT '职业能力名称',
  `threshold` int(11) DEFAULT 60 COMMENT '达标阈值',
  `weight` int(11) DEFAULT 100 COMMENT '计算权重',
  `create_by` varchar(64) DEFAULT '' COMMENT '创建者',
  `create_time` datetime DEFAULT NULL COMMENT '创建时间',
  `update_by` varchar(64) DEFAULT '' COMMENT '更新者',
  `update_time` datetime DEFAULT NULL COMMENT '更新时间',
  `remark` varchar(500) DEFAULT NULL COMMENT '备注',
  PRIMARY KEY (`config_id`),
  KEY `idx_post_id` (`post_id`),
  KEY `idx_ability_id` (`ability_id`)
) ENGINE=InnoDB AUTO_INCREMENT=1 DEFAULT CHARSET=utf8mb4 COMMENT='岗位职业能力配置表';

-- ----------------------------
-- 岗位课程配置表
-- ----------------------------
DROP TABLE IF EXISTS `post_course_config`;
CREATE TABLE `post_course_config` (
  `config_id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '配置ID',
  `post_id` bigint(20) NOT NULL COMMENT '岗位ID',
  `ability_config_id` bigint(20) NOT NULL COMMENT '职业能力配置ID',
  `course_id` bigint(20) NOT NULL COMMENT '课程ID',
  `course_name` varchar(100) DEFAULT NULL COMMENT '课程名称',
  `course_objective` varchar(500) DEFAULT NULL COMMENT '课程目标',
  `course_type` int(11) DEFAULT 1 COMMENT '课程类型（1-学校课程，2-企业课程）',
  `weight` int(11) DEFAULT 100 COMMENT '权重（%）',
  `create_by` varchar(64) DEFAULT '' COMMENT '创建者',
  `create_time` datetime DEFAULT NULL COMMENT '创建时间',
  `update_by` varchar(64) DEFAULT '' COMMENT '更新者',
  `update_time` datetime DEFAULT NULL COMMENT '更新时间',
  `remark` varchar(500) DEFAULT NULL COMMENT '备注',
  PRIMARY KEY (`config_id`),
  KEY `idx_post_id` (`post_id`),
  KEY `idx_ability_config_id` (`ability_config_id`),
  KEY `idx_course_id` (`course_id`)
) ENGINE=InnoDB AUTO_INCREMENT=1 DEFAULT CHARSET=utf8mb4 COMMENT='岗位课程配置表';

 