-- ----------------------------
-- 人才培养方案表
-- ----------------------------
DROP TABLE IF EXISTS `training_program`;
CREATE TABLE `training_program` (
  `program_id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '方案ID',
  `program_name` varchar(60) NOT NULL COMMENT '方案名称',
  `program_intro` varchar(600) DEFAULT NULL COMMENT '方案简介',
  `training_time` int(3) NOT NULL COMMENT '培训时间(年)',
  `trainee_count` int(5) NOT NULL COMMENT '预计培养人数(人)',
  `training_goal` varchar(600) DEFAULT NULL COMMENT '培养目标',
  `training_spec` varchar(600) DEFAULT NULL COMMENT '培养规格',
  `revision_count` int(3) DEFAULT 0 COMMENT '修订次数',
  `create_by` varchar(64) DEFAULT '' COMMENT '创建者',
  `create_time` datetime DEFAULT NULL COMMENT '创建时间',
  `update_by` varchar(64) DEFAULT '' COMMENT '更新者',
  `update_time` datetime DEFAULT NULL COMMENT '更新时间',
  `remark` varchar(500) DEFAULT NULL COMMENT '备注',
  PRIMARY KEY (`program_id`)
) ENGINE=InnoDB AUTO_INCREMENT=1 DEFAULT CHARSET=utf8mb4 COMMENT='人才培养方案表';

-- ----------------------------
-- 初始化人才培养方案数据
-- ----------------------------
INSERT INTO `training_program` VALUES (1, '机械设计师', '培养具备机械产品设计和开发能力的专业人才', 3, 30, '培养具备机械产品设计和开发能力的专业人才，能够独立完成机械产品的设计、分析和优化工作。', '掌握机械设计基础理论，熟练使用CAD/CAM软件，具备产品设计和开发能力。', 0, 'admin', sysdate(), '', null, null);
INSERT INTO `training_program` VALUES (2, '电气技术员', '培养具备电气设备维护和技术支持能力的专业人才', 2, 25, '培养具备电气设备维护和技术支持能力的专业人才，能够独立完成电气设备的安装、调试和维护工作。', '掌握电气技术基础理论，熟练使用电气设计软件，具备设备维护和技术支持能力。', 0, 'admin', sysdate(), '', null, null);
INSERT INTO `training_program` VALUES (3, '软件工程师', '培养具备软件系统开发和维护能力的专业人才', 3, 40, '培养具备软件系统开发和维护能力的专业人才，能够独立完成软件系统的设计、开发和维护工作。', '掌握软件工程基础理论，熟练使用多种编程语言和开发工具，具备系统开发和维护能力。', 0, 'admin', sysdate(), '', null, null);
INSERT INTO `training_program` VALUES (4, '数据分析师', '培养具备数据分析和业务洞察能力的专业人才', 2, 20, '培养具备数据分析和业务洞察能力的专业人才，能够独立完成数据分析和业务洞察工作。', '掌握数据分析基础理论，熟练使用数据分析工具，具备数据分析和业务洞察能力。', 0, 'admin', sysdate(), '', null, null);
INSERT INTO `training_program` VALUES (5, '质量检测员', '培养具备产品质量检测和控制能力的专业人才', 1, 15, '培养具备产品质量检测和控制能力的专业人才，能够独立完成产品质量检测和控制工作。', '掌握质量检测基础理论，熟练使用检测设备和工具，具备质量检测和控制能力。', 0, 'admin', sysdate(), '', null, null); 