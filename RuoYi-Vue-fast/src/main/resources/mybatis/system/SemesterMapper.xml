<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.project.system.mapper.SemesterMapper">
    
    <resultMap type="Semester" id="SemesterResult">
        <result property="semesterId"     column="semester_id"     />
        <result property="semesterCode"   column="semester_code"   />
        <result property="semesterName"   column="semester_name"   />
    </resultMap>

    <sql id="selectSemesterVo">
        select semester_id, semester_code, semester_name from semester
    </sql>

    <select id="selectSemesterList" parameterType="Semester" resultMap="SemesterResult">
        <include refid="selectSemesterVo"/>
        <where>  
            <if test="semesterCode != null and semesterCode != ''"> and semester_code like concat('%', #{semesterCode}, '%')</if>
            <if test="semesterName != null and semesterName != ''"> and semester_name like concat('%', #{semesterName}, '%')</if>
        </where>
        order by semester_id desc
    </select>
    
    <select id="selectSemesterBySemesterId" parameterType="Long" resultMap="SemesterResult">
        <include refid="selectSemesterVo"/>
        where semester_id = #{semesterId}
    </select>

</mapper> 