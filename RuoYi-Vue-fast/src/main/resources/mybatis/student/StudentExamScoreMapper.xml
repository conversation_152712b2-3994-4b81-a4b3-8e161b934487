<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.project.student.mapper.StudentExamScoreMapper">
    
    <resultMap type="StudentExamScore" id="StudentExamScoreResult">
        <result property="scoreId"       column="score_id"      />
        <result property="studentId"     column="student_id"    />
        <result property="courseName"    column="course_name"   />
        <result property="courseType"    column="course_type"   />
        <result property="examType"      column="exam_type"     />
        <result property="score"         column="score"         />
        <result property="createBy"      column="create_by"     />
        <result property="createTime"    column="create_time"   />
        <result property="updateBy"      column="update_by"     />
        <result property="updateTime"    column="update_time"   />
        <result property="remark"        column="remark"        />
    </resultMap>

    <sql id="selectStudentExamScoreVo">
        select score_id, student_id, course_name, course_type, exam_type, score, create_by, create_time, update_by, update_time, remark from student_exam_score
    </sql>

    <select id="selectStudentExamScoreList" parameterType="StudentExamScore" resultMap="StudentExamScoreResult">
        <include refid="selectStudentExamScoreVo"/>
        <where>  
            <if test="studentId != null "> and student_id = #{studentId}</if>
            <if test="courseName != null  and courseName != ''"> and course_name like concat('%', #{courseName}, '%')</if>
            <if test="courseType != null  and courseType != ''"> and course_type = #{courseType}</if>
            <if test="examType != null  and examType != ''"> and exam_type = #{examType}</if>
            <if test="score != null "> and score = #{score}</if>
        </where>
    </select>
    
    <select id="selectStudentExamScoreByScoreId" parameterType="Long" resultMap="StudentExamScoreResult">
        <include refid="selectStudentExamScoreVo"/>
        where score_id = #{scoreId}
    </select>

    <select id="selectStudentExamScoreListByStudentId" parameterType="Long" resultMap="StudentExamScoreResult">
        <include refid="selectStudentExamScoreVo"/>
        where student_id = #{studentId}
        order by create_time desc
    </select>
        
    <insert id="insertStudentExamScore" parameterType="StudentExamScore" useGeneratedKeys="true" keyProperty="scoreId">
        insert into student_exam_score
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="studentId != null">student_id,</if>
            <if test="courseName != null">course_name,</if>
            <if test="courseType != null">course_type,</if>
            <if test="examType != null">exam_type,</if>
            <if test="score != null">score,</if>
            <if test="createBy != null">create_by,</if>
            <if test="createTime != null">create_time,</if>
            <if test="updateBy != null">update_by,</if>
            <if test="updateTime != null">update_time,</if>
            <if test="remark != null">remark,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="studentId != null">#{studentId},</if>
            <if test="courseName != null">#{courseName},</if>
            <if test="courseType != null">#{courseType},</if>
            <if test="examType != null">#{examType},</if>
            <if test="score != null">#{score},</if>
            <if test="createBy != null">#{createBy},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="updateBy != null">#{updateBy},</if>
            <if test="updateTime != null">#{updateTime},</if>
            <if test="remark != null">#{remark},</if>
         </trim>
    </insert>

    <update id="updateStudentExamScore" parameterType="StudentExamScore">
        update student_exam_score
        <trim prefix="SET" suffixOverrides=",">
            <if test="studentId != null">student_id = #{studentId},</if>
            <if test="courseName != null">course_name = #{courseName},</if>
            <if test="courseType != null">course_type = #{courseType},</if>
            <if test="examType != null">exam_type = #{examType},</if>
            <if test="score != null">score = #{score},</if>
            <if test="createBy != null">create_by = #{createBy},</if>
            <if test="createTime != null">create_time = #{createTime},</if>
            <if test="updateBy != null">update_by = #{updateBy},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
            <if test="remark != null">remark = #{remark},</if>
        </trim>
        where score_id = #{scoreId}
    </update>

    <delete id="deleteStudentExamScoreByScoreId" parameterType="Long">
        delete from student_exam_score where score_id = #{scoreId}
    </delete>

    <delete id="deleteStudentExamScoreByScoreIds" parameterType="String">
        delete from student_exam_score where score_id in 
        <foreach item="scoreId" collection="array" open="(" separator="," close=")">
            #{scoreId}
        </foreach>
    </delete>

    <delete id="deleteStudentExamScoreByStudentId" parameterType="Long">
        delete from student_exam_score where student_id = #{studentId}
    </delete>

    <delete id="deleteStudentExamScoreByStudentIds" parameterType="String">
        delete from student_exam_score where student_id in 
        <foreach item="studentId" collection="array" open="(" separator="," close=")">
            #{studentId}
        </foreach>
    </delete>
</mapper> 