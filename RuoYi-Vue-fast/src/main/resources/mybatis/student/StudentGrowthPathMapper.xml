<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.project.student.mapper.StudentGrowthPathMapper">
    
    <resultMap type="StudentGrowthPath" id="StudentGrowthPathResult">
        <result property="pathId"            column="path_id"           />
        <result property="studentId"         column="student_id"        />
        <result property="semesterCode"      column="semester_code"     />
        <result property="learningSituation" column="learning_situation" />
        <result property="createBy"          column="create_by"         />
        <result property="createTime"        column="create_time"       />
        <result property="updateBy"          column="update_by"         />
        <result property="updateTime"        column="update_time"       />
    </resultMap>

    <sql id="selectStudentGrowthPathVo">
        select path_id, student_id, semester_code, learning_situation, create_by, create_time, update_by, update_time from student_growth_path
    </sql>

    <select id="selectStudentGrowthPathList" parameterType="StudentGrowthPath" resultMap="StudentGrowthPathResult">
        <include refid="selectStudentGrowthPathVo"/>
        <where>  
            <if test="studentId != null"> and student_id = #{studentId}</if>
            <if test="semesterCode != null and semesterCode != ''"> and semester_code like concat('%', #{semesterCode}, '%')</if>
            <if test="learningSituation != null and learningSituation != ''"> and learning_situation like concat('%', #{learningSituation}, '%')</if>
        </where>
        order by create_time desc
    </select>
    
    <select id="selectStudentGrowthPathByPathId" parameterType="Long" resultMap="StudentGrowthPathResult">
        <include refid="selectStudentGrowthPathVo"/>
        where path_id = #{pathId}
    </select>

    <select id="selectStudentGrowthPathByStudentId" parameterType="Long" resultMap="StudentGrowthPathResult">
        <include refid="selectStudentGrowthPathVo"/>
        where student_id = #{studentId}
        order by create_time desc
    </select>
        
    <insert id="insertStudentGrowthPath" parameterType="StudentGrowthPath" useGeneratedKeys="true" keyProperty="pathId">
        insert into student_growth_path
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="studentId != null">student_id,</if>
            <if test="semesterCode != null and semesterCode != ''">semester_code,</if>
            <if test="learningSituation != null">learning_situation,</if>
            <if test="createBy != null">create_by,</if>
            <if test="createTime != null">create_time,</if>
            <if test="updateBy != null">update_by,</if>
            <if test="updateTime != null">update_time,</if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="studentId != null">#{studentId},</if>
            <if test="semesterCode != null and semesterCode != ''">#{semesterCode},</if>
            <if test="learningSituation != null">#{learningSituation},</if>
            <if test="createBy != null">#{createBy},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="updateBy != null">#{updateBy},</if>
            <if test="updateTime != null">#{updateTime},</if>
        </trim>
    </insert>

    <update id="updateStudentGrowthPath" parameterType="StudentGrowthPath">
        update student_growth_path
        <trim prefix="SET" suffixOverrides=",">
            <if test="studentId != null">student_id = #{studentId},</if>
            <if test="semesterCode != null and semesterCode != ''">semester_code = #{semesterCode},</if>
            <if test="learningSituation != null">learning_situation = #{learningSituation},</if>
            <if test="updateBy != null">update_by = #{updateBy},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
        </trim>
        where path_id = #{pathId}
    </update>

    <delete id="deleteStudentGrowthPathByPathId" parameterType="Long">
        delete from student_growth_path where path_id = #{pathId}
    </delete>

    <delete id="deleteStudentGrowthPathByPathIds" parameterType="String">
        delete from student_growth_path where path_id in 
        <foreach item="pathId" collection="array" open="(" separator="," close=")">
            #{pathId}
        </foreach>
    </delete>

    <delete id="deleteStudentGrowthPathByStudentId" parameterType="Long">
        delete from student_growth_path where student_id = #{studentId}
    </delete>

    <delete id="deleteStudentGrowthPathByStudentIds" parameterType="String">
        delete from student_growth_path where student_id in 
        <foreach item="studentId" collection="array" open="(" separator="," close=")">
            #{studentId}
        </foreach>
    </delete>
</mapper> 