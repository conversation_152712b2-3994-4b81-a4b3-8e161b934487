<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.project.student.mapper.StudentContractMapper">
    
    <resultMap type="StudentContract" id="StudentContractResult">
        <result property="contractId"       column="contract_id"      />
        <result property="contractNumber"   column="contract_number"  />
        <result property="contractName"     column="contract_name"    />
        <result property="signDate"         column="sign_date"        />
        <result property="studentId"        column="student_id"       />
        <result property="companyName"      column="company_name"     />
        <result property="postName"         column="post_name"        />
        <result property="salary"           column="salary"           />
        <result property="createBy"         column="create_by"        />
        <result property="createTime"       column="create_time"      />
        <result property="updateBy"         column="update_by"        />
        <result property="updateTime"       column="update_time"      />
        <result property="remark"           column="remark"           />
        <!-- 关联的学生信息 -->
        <result property="studentName"      column="student_name"     />
        <result property="studentNumber"    column="student_number"   />
        <result property="major"            column="major"            />
        <result property="schoolTeacher"    column="school_teacher"   />
        <result property="companyTeacher"   column="company_teacher"  />
    </resultMap>

    <sql id="selectStudentContractVo">
        select sc.contract_id, sc.contract_number, sc.contract_name, sc.sign_date, sc.student_id, 
               sc.company_name, sc.post_name, sc.salary, 
               sc.create_by, sc.create_time, sc.update_by, sc.update_time, sc.remark
        from student_contract sc
    </sql>

    <sql id="selectStudentContractWithStudentVo">
        select sc.contract_id, sc.contract_number, sc.contract_name, sc.sign_date, sc.student_id, 
               sc.company_name, sc.post_name, sc.salary, 
               sc.create_by, sc.create_time, sc.update_by, sc.update_time, sc.remark,
               si.student_name, si.student_number, si.major, si.school_teacher, si.company_teacher
        from student_contract sc
        left join student_info si on sc.student_id = si.student_id
    </sql>

    <select id="selectStudentContractList" parameterType="StudentContract" resultMap="StudentContractResult">
        <include refid="selectStudentContractWithStudentVo"/>
        <where>  
            <if test="contractNumber != null and contractNumber != ''"> and sc.contract_number like concat('%', #{contractNumber}, '%')</if>
            <if test="contractName != null and contractName != ''"> and sc.contract_name like concat('%', #{contractName}, '%')</if>
            <if test="companyName != null and companyName != ''"> and sc.company_name like concat('%', #{companyName}, '%')</if>
            <if test="postName != null and postName != ''"> and sc.post_name like concat('%', #{postName}, '%')</if>
            <if test="studentName != null and studentName != ''"> and si.student_name like concat('%', #{studentName}, '%')</if>
            <if test="studentNumber != null and studentNumber != ''"> and si.student_number like concat('%', #{studentNumber}, '%')</if>
            <if test="params.beginSignDate != null and params.beginSignDate != ''"><!-- 开始签约日期 -->
                and date_format(sc.sign_date,'%y%m%d') &gt;= date_format(#{params.beginSignDate},'%y%m%d')
            </if>
            <if test="params.endSignDate != null and params.endSignDate != ''"><!-- 结束签约日期 -->
                and date_format(sc.sign_date,'%y%m%d') &lt;= date_format(#{params.endSignDate},'%y%m%d')
            </if>
        </where>
        order by sc.contract_id asc
    </select>
    
    <select id="selectStudentContractByContractId" parameterType="Long" resultMap="StudentContractResult">
        <include refid="selectStudentContractWithStudentVo"/>
        where sc.contract_id = #{contractId}
    </select>

    <select id="checkContractNumberUnique" parameterType="String" resultMap="StudentContractResult">
        <include refid="selectStudentContractVo"/>
        where contract_number = #{contractNumber} limit 1
    </select>
        
    <insert id="insertStudentContract" parameterType="StudentContract" useGeneratedKeys="true" keyProperty="contractId">
        insert into student_contract
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="contractNumber != null and contractNumber != ''">contract_number,</if>
            <if test="contractName != null and contractName != ''">contract_name,</if>
            <if test="signDate != null">sign_date,</if>
            <if test="studentId != null">student_id,</if>
            <if test="companyName != null and companyName != ''">company_name,</if>
            <if test="postName != null">post_name,</if>
            <if test="salary != null">salary,</if>
            <if test="createBy != null">create_by,</if>
            <if test="createTime != null">create_time,</if>
            <if test="updateBy != null">update_by,</if>
            <if test="updateTime != null">update_time,</if>
            <if test="remark != null">remark,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="contractNumber != null and contractNumber != ''">#{contractNumber},</if>
            <if test="contractName != null and contractName != ''">#{contractName},</if>
            <if test="signDate != null">#{signDate},</if>
            <if test="studentId != null">#{studentId},</if>
            <if test="companyName != null and companyName != ''">#{companyName},</if>
            <if test="postName != null">#{postName},</if>
            <if test="salary != null">#{salary},</if>
            <if test="createBy != null">#{createBy},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="updateBy != null">#{updateBy},</if>
            <if test="updateTime != null">#{updateTime},</if>
            <if test="remark != null">#{remark},</if>
         </trim>
    </insert>

    <update id="updateStudentContract" parameterType="StudentContract">
        update student_contract
        <trim prefix="SET" suffixOverrides=",">
            <if test="contractNumber != null and contractNumber != ''">contract_number = #{contractNumber},</if>
            <if test="contractName != null and contractName != ''">contract_name = #{contractName},</if>
            <if test="signDate != null">sign_date = #{signDate},</if>
            <if test="studentId != null">student_id = #{studentId},</if>
            <if test="companyName != null and companyName != ''">company_name = #{companyName},</if>
            <if test="postName != null">post_name = #{postName},</if>
            <if test="salary != null">salary = #{salary},</if>
            <if test="updateBy != null">update_by = #{updateBy},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
            <if test="remark != null">remark = #{remark},</if>
        </trim>
        where contract_id = #{contractId}
    </update>

    <delete id="deleteStudentContractByContractId" parameterType="Long">
        delete from student_contract where contract_id = #{contractId}
    </delete>

    <delete id="deleteStudentContractByContractIds" parameterType="String">
        delete from student_contract where contract_id in 
        <foreach item="contractId" collection="array" open="(" separator="," close=")">
            #{contractId}
        </foreach>
    </delete>
</mapper> 