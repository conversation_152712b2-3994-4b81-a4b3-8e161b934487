<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.project.training.mapper.TrainingProgramMapper">
    
    <resultMap type="TrainingProgram" id="TrainingProgramResult">
        <result property="programId"    column="program_id"    />
        <result property="programName"    column="program_name"    />
        <result property="programIntro"    column="program_intro"    />
        <result property="trainingTime"    column="training_time"    />
        <result property="traineeCount"    column="trainee_count"    />
        <result property="trainingGoal"    column="training_goal"    />
        <result property="trainingSpec"    column="training_spec"    />
        <result property="revisionCount"    column="revision_count"    />
        <result property="createBy"    column="create_by"    />
        <result property="createTime"    column="create_time"    />
        <result property="updateBy"    column="update_by"    />
        <result property="updateTime"    column="update_time"    />
        <result property="remark"    column="remark"    />
    </resultMap>

    <sql id="selectTrainingProgramVo">
        select program_id, program_name, program_intro, training_time, trainee_count, training_goal, training_spec, revision_count, create_by, create_time, update_by, update_time, remark from training_program
    </sql>

    <select id="selectTrainingProgramList" parameterType="TrainingProgram" resultMap="TrainingProgramResult">
        <include refid="selectTrainingProgramVo"/>
        <where>  
            <if test="programName != null  and programName != ''"> and program_name like concat('%', #{programName}, '%')</if>
            <if test="programIntro != null  and programIntro != ''"> and program_intro like concat('%', #{programIntro}, '%')</if>
            <if test="trainingTime != null "> and training_time = #{trainingTime}</if>
            <if test="traineeCount != null "> and trainee_count = #{traineeCount}</if>
            <if test="trainingGoal != null  and trainingGoal != ''"> and training_goal like concat('%', #{trainingGoal}, '%')</if>
            <if test="trainingSpec != null  and trainingSpec != ''"> and training_spec like concat('%', #{trainingSpec}, '%')</if>
            <if test="revisionCount != null "> and revision_count = #{revisionCount}</if>
        </where>
    </select>
    
    <select id="selectTrainingProgramByProgramId" parameterType="Long" resultMap="TrainingProgramResult">
        <include refid="selectTrainingProgramVo"/>
        where program_id = #{programId}
    </select>
        
    <insert id="insertTrainingProgram" parameterType="TrainingProgram" useGeneratedKeys="true" keyProperty="programId">
        insert into training_program
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="programName != null and programName != ''">program_name,</if>
            <if test="programIntro != null">program_intro,</if>
            <if test="trainingTime != null">training_time,</if>
            <if test="traineeCount != null">trainee_count,</if>
            <if test="trainingGoal != null">training_goal,</if>
            <if test="trainingSpec != null">training_spec,</if>
            <if test="revisionCount != null">revision_count,</if>
            <if test="createBy != null">create_by,</if>
            <if test="createTime != null">create_time,</if>
            <if test="updateBy != null">update_by,</if>
            <if test="updateTime != null">update_time,</if>
            <if test="remark != null">remark,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="programName != null and programName != ''">#{programName},</if>
            <if test="programIntro != null">#{programIntro},</if>
            <if test="trainingTime != null">#{trainingTime},</if>
            <if test="traineeCount != null">#{traineeCount},</if>
            <if test="trainingGoal != null">#{trainingGoal},</if>
            <if test="trainingSpec != null">#{trainingSpec},</if>
            <if test="revisionCount != null">#{revisionCount},</if>
            <if test="createBy != null">#{createBy},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="updateBy != null">#{updateBy},</if>
            <if test="updateTime != null">#{updateTime},</if>
            <if test="remark != null">#{remark},</if>
         </trim>
    </insert>

    <update id="updateTrainingProgram" parameterType="TrainingProgram">
        update training_program
        <trim prefix="SET" suffixOverrides=",">
            <if test="programName != null and programName != ''">program_name = #{programName},</if>
            <if test="programIntro != null">program_intro = #{programIntro},</if>
            <if test="trainingTime != null">training_time = #{trainingTime},</if>
            <if test="traineeCount != null">trainee_count = #{traineeCount},</if>
            <if test="trainingGoal != null">training_goal = #{trainingGoal},</if>
            <if test="trainingSpec != null">training_spec = #{trainingSpec},</if>
            <if test="revisionCount != null">revision_count = #{revisionCount},</if>
            <if test="createBy != null">create_by = #{createBy},</if>
            <if test="createTime != null">create_time = #{createTime},</if>
            <if test="updateBy != null">update_by = #{updateBy},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
            <if test="remark != null">remark = #{remark},</if>
        </trim>
        where program_id = #{programId}
    </update>

    <delete id="deleteTrainingProgramByProgramId" parameterType="Long">
        delete from training_program where program_id = #{programId}
    </delete>

    <delete id="deleteTrainingProgramByProgramIds" parameterType="Long">
        delete from training_program where program_id in 
        <foreach item="programId" collection="array" open="(" separator="," close=")">
            #{programId}
        </foreach>
    </delete>

    <delete id="deleteAllTrainingPrograms">
        delete from training_program
    </delete>
</mapper> 