<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.project.training.mapper.PostModelMapper">
    
    <resultMap type="PostModel" id="PostModelResult">
        <result property="postId"    column="post_id"    />
        <result property="postName"    column="post_name"    />
        <result property="postDesc"    column="post_desc"    />
        <result property="createBy"    column="create_by"    />
        <result property="createTime"    column="create_time"    />
        <result property="updateBy"    column="update_by"    />
        <result property="updateTime"    column="update_time"    />
        <result property="remark"    column="remark"    />
    </resultMap>

    <sql id="selectPostModelVo">
        select post_id, post_name, post_desc, create_by, create_time, update_by, update_time, remark from post_model
    </sql>

    <select id="selectPostModelList" parameterType="PostModel" resultMap="PostModelResult">
        <include refid="selectPostModelVo"/>
        <where>  
            <if test="postName != null  and postName != ''"> and post_name like concat('%', #{postName}, '%')</if>
            <if test="postDesc != null  and postDesc != ''"> and post_desc like concat('%', #{postDesc}, '%')</if>
        </where>
        order by post_id desc
    </select>
    
    <select id="selectPostModelByPostId" parameterType="Long" resultMap="PostModelResult">
        <include refid="selectPostModelVo"/>
        where post_id = #{postId}
    </select>

    <select id="selectCompanyIdsByPostId" parameterType="Long" resultType="Long">
        select company_id from post_model_company where post_id = #{postId}
    </select>
        
    <insert id="insertPostModel" parameterType="PostModel" useGeneratedKeys="true" keyProperty="postId">
        insert into post_model
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="postName != null and postName != ''">post_name,</if>
            <if test="postDesc != null">post_desc,</if>
            <if test="createBy != null">create_by,</if>
            <if test="createTime != null">create_time,</if>
            <if test="updateBy != null">update_by,</if>
            <if test="updateTime != null">update_time,</if>
            <if test="remark != null">remark,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="postName != null and postName != ''">#{postName},</if>
            <if test="postDesc != null">#{postDesc},</if>
            <if test="createBy != null">#{createBy},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="updateBy != null">#{updateBy},</if>
            <if test="updateTime != null">#{updateTime},</if>
            <if test="remark != null">#{remark},</if>
         </trim>
    </insert>

    <update id="updatePostModel" parameterType="PostModel">
        update post_model
        <trim prefix="SET" suffixOverrides=",">
            <if test="postName != null and postName != ''">post_name = #{postName},</if>
            <if test="postDesc != null">post_desc = #{postDesc},</if>
            <if test="createBy != null">create_by = #{createBy},</if>
            <if test="createTime != null">create_time = #{createTime},</if>
            <if test="updateBy != null">update_by = #{updateBy},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
            <if test="remark != null">remark = #{remark},</if>
        </trim>
        where post_id = #{postId}
    </update>

    <delete id="deletePostModelByPostId" parameterType="Long">
        delete from post_model where post_id = #{postId}
    </delete>

    <delete id="deletePostModelByPostIds" parameterType="String">
        delete from post_model where post_id in 
        <foreach item="postId" collection="postIds" open="(" separator="," close=")">
            #{postId}
        </foreach>
    </delete>

    <!-- 删除岗位企业关联 -->
    <delete id="deletePostModelCompanyByPostId" parameterType="Long">
        delete from post_model_company where post_id = #{postId}
    </delete>

    <delete id="deletePostModelCompanyByPostIds" parameterType="String">
        delete from post_model_company where post_id in 
        <foreach item="postId" collection="postIds" open="(" separator="," close=")">
            #{postId}
        </foreach>
    </delete>

    <!-- 插入岗位企业关联 -->
    <insert id="insertPostModelCompany">
        insert into post_model_company(post_id, company_id) values
        <foreach item="companyId" collection="companyIds" separator=",">
            (#{postId}, #{companyId})
        </foreach>
    </insert>

</mapper>
