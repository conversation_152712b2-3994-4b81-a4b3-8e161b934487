<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.project.training.mapper.EvaluationQuestionBankMapper">
    
    <resultMap type="EvaluationQuestionBank" id="EvaluationQuestionBankResult">
        <result property="bankId"    column="bank_id"    />
        <result property="bankName"    column="bank_name"    />
        <result property="status"    column="status"    />
        <result property="createBy"    column="create_by"    />
        <result property="createTime"    column="create_time"    />
        <result property="updateBy"    column="update_by"    />
        <result property="updateTime"    column="update_time"    />
        <result property="remark"    column="remark"    />
    </resultMap>

    <sql id="selectEvaluationQuestionBankVo">
        select bank_id, bank_name, status, create_by, create_time, update_by, update_time, remark from evaluation_question_bank
    </sql>

    <select id="selectEvaluationQuestionBankList" parameterType="EvaluationQuestionBank" resultMap="EvaluationQuestionBankResult">
        <include refid="selectEvaluationQuestionBankVo"/>
        <where>  
            <if test="bankName != null  and bankName != ''"> and bank_name like concat('%', #{bankName}, '%')</if>
            <if test="status != null  and status != ''"> and status = #{status}</if>
        </where>
        order by create_time desc
    </select>
    
    <select id="selectEvaluationQuestionBankByBankId" parameterType="Long" resultMap="EvaluationQuestionBankResult">
        <include refid="selectEvaluationQuestionBankVo"/>
        where bank_id = #{bankId}
    </select>
        
    <insert id="insertEvaluationQuestionBank" parameterType="EvaluationQuestionBank" useGeneratedKeys="true" keyProperty="bankId">
        insert into evaluation_question_bank
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="bankName != null and bankName != ''">bank_name,</if>
            <if test="status != null">status,</if>
            <if test="createBy != null">create_by,</if>
            <if test="createTime != null">create_time,</if>
            <if test="updateBy != null">update_by,</if>
            <if test="updateTime != null">update_time,</if>
            <if test="remark != null">remark,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="bankName != null and bankName != ''">#{bankName},</if>
            <if test="status != null">#{status},</if>
            <if test="createBy != null">#{createBy},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="updateBy != null">#{updateBy},</if>
            <if test="updateTime != null">#{updateTime},</if>
            <if test="remark != null">#{remark},</if>
         </trim>
    </insert>

    <update id="updateEvaluationQuestionBank" parameterType="EvaluationQuestionBank">
        update evaluation_question_bank
        <trim prefix="SET" suffixOverrides=",">
            <if test="bankName != null and bankName != ''">bank_name = #{bankName},</if>
            <if test="status != null">status = #{status},</if>
            <if test="createBy != null">create_by = #{createBy},</if>
            <if test="createTime != null">create_time = #{createTime},</if>
            <if test="updateBy != null">update_by = #{updateBy},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
            <if test="remark != null">remark = #{remark},</if>
        </trim>
        where bank_id = #{bankId}
    </update>

    <delete id="deleteEvaluationQuestionBankByBankId" parameterType="Long">
        delete from evaluation_question_bank where bank_id = #{bankId}
    </delete>

    <delete id="deleteEvaluationQuestionBankByBankIds" parameterType="String">
        delete from evaluation_question_bank where bank_id in 
        <foreach item="bankId" collection="array" open="(" separator="," close=")">
            #{bankId}
        </foreach>
    </delete>
</mapper> 