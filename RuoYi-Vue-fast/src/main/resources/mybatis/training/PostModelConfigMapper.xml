<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.project.training.mapper.PostModelConfigMapper">
    
    <resultMap type="PostModelConfig" id="PostModelConfigResult">
        <result property="configId"    column="config_id"    />
        <result property="postId"    column="post_id"    />
        <result property="postName"    column="post_name"    />
        <result property="createBy"    column="create_by"    />
        <result property="createTime"    column="create_time"    />
        <result property="updateBy"    column="update_by"    />
        <result property="updateTime"    column="update_time"    />
        <result property="remark"    column="remark"    />
    </resultMap>

    <resultMap type="PostAbilityConfig" id="PostAbilityConfigResult">
        <result property="configId"    column="config_id"    />
        <result property="postId"    column="post_id"    />
        <result property="abilityId"    column="ability_id"    />
        <result property="abilityName"    column="ability_name"    />
        <result property="threshold"    column="threshold"    />
        <result property="weight"    column="weight"    />
        <result property="createBy"    column="create_by"    />
        <result property="createTime"    column="create_time"    />
        <result property="updateBy"    column="update_by"    />
        <result property="updateTime"    column="update_time"    />
        <result property="remark"    column="remark"    />
    </resultMap>

    <resultMap type="PostCourseConfig" id="PostCourseConfigResult">
        <result property="configId"    column="config_id"    />
        <result property="postId"    column="post_id"    />
        <result property="abilityConfigId"    column="ability_config_id"    />
        <result property="courseId"    column="course_id"    />
        <result property="courseName"    column="course_name"    />
        <result property="courseObjective"    column="course_objective"    />
        <result property="courseType"    column="course_type"    />
        <result property="weight"    column="weight"    />
        <result property="createBy"    column="create_by"    />
        <result property="createTime"    column="create_time"    />
        <result property="updateBy"    column="update_by"    />
        <result property="updateTime"    column="update_time"    />
        <result property="remark"    column="remark"    />
    </resultMap>

    <sql id="selectPostModelConfigVo">
        select config_id, post_id, post_name, create_by, create_time, update_by, update_time, remark from post_model_config
    </sql>

    <sql id="selectPostAbilityConfigVo">
        select config_id, post_id, ability_id, ability_name, threshold, weight, create_by, create_time, update_by, update_time, remark from post_ability_config
    </sql>

    <sql id="selectPostCourseConfigVo">
        select config_id, post_id, ability_config_id, course_id, course_name, course_objective, course_type, weight, create_by, create_time, update_by, update_time, remark from post_course_config
    </sql>

    <select id="selectPostModelConfigList" parameterType="PostModelConfig" resultMap="PostModelConfigResult">
        <include refid="selectPostModelConfigVo"/>
        <where>  
            <if test="postId != null "> and post_id = #{postId}</if>
            <if test="postName != null  and postName != ''"> and post_name like concat('%', #{postName}, '%')</if>
        </where>
        order by config_id desc
    </select>
    
    <select id="selectPostModelConfigByPostId" parameterType="Long" resultMap="PostModelConfigResult">
        <include refid="selectPostModelConfigVo"/>
        where post_id = #{postId}
    </select>

    <select id="selectPostAbilityConfigList" parameterType="Long" resultMap="PostAbilityConfigResult">
        <include refid="selectPostAbilityConfigVo"/>
        where post_id = #{postId}
        order by config_id asc
    </select>

    <select id="selectPostCourseConfigList" parameterType="Long" resultMap="PostCourseConfigResult">
        <include refid="selectPostCourseConfigVo"/>
        where ability_config_id = #{abilityConfigId}
        order by config_id asc
    </select>
        
    <insert id="insertPostModelConfig" parameterType="PostModelConfig" useGeneratedKeys="true" keyProperty="configId">
        insert into post_model_config
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="postId != null">post_id,</if>
            <if test="postName != null">post_name,</if>
            <if test="createBy != null">create_by,</if>
            <if test="createTime != null">create_time,</if>
            <if test="updateBy != null">update_by,</if>
            <if test="updateTime != null">update_time,</if>
            <if test="remark != null">remark,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="postId != null">#{postId},</if>
            <if test="postName != null">#{postName},</if>
            <if test="createBy != null">#{createBy},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="updateBy != null">#{updateBy},</if>
            <if test="updateTime != null">#{updateTime},</if>
            <if test="remark != null">#{remark},</if>
         </trim>
    </insert>

    <insert id="insertPostAbilityConfig" parameterType="PostAbilityConfig" useGeneratedKeys="true" keyProperty="configId">
        insert into post_ability_config
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="postId != null">post_id,</if>
            <if test="abilityId != null">ability_id,</if>
            <if test="abilityName != null">ability_name,</if>
            <if test="threshold != null">threshold,</if>
            <if test="weight != null">weight,</if>
            <if test="createBy != null">create_by,</if>
            <if test="createTime != null">create_time,</if>
            <if test="updateBy != null">update_by,</if>
            <if test="updateTime != null">update_time,</if>
            <if test="remark != null">remark,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="postId != null">#{postId},</if>
            <if test="abilityId != null">#{abilityId},</if>
            <if test="abilityName != null">#{abilityName},</if>
            <if test="threshold != null">#{threshold},</if>
            <if test="weight != null">#{weight},</if>
            <if test="createBy != null">#{createBy},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="updateBy != null">#{updateBy},</if>
            <if test="updateTime != null">#{updateTime},</if>
            <if test="remark != null">#{remark},</if>
         </trim>
    </insert>

    <insert id="insertPostCourseConfig" parameterType="PostCourseConfig" useGeneratedKeys="true" keyProperty="configId">
        insert into post_course_config
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="postId != null">post_id,</if>
            <if test="abilityConfigId != null">ability_config_id,</if>
            <if test="courseId != null">course_id,</if>
            <if test="courseName != null">course_name,</if>
            <if test="courseObjective != null">course_objective,</if>
            <if test="courseType != null">course_type,</if>
            <if test="weight != null">weight,</if>
            <if test="createBy != null">create_by,</if>
            <if test="createTime != null">create_time,</if>
            <if test="updateBy != null">update_by,</if>
            <if test="updateTime != null">update_time,</if>
            <if test="remark != null">remark,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="postId != null">#{postId},</if>
            <if test="abilityConfigId != null">#{abilityConfigId},</if>
            <if test="courseId != null">#{courseId},</if>
            <if test="courseName != null">#{courseName},</if>
            <if test="courseObjective != null">#{courseObjective},</if>
            <if test="courseType != null">#{courseType},</if>
            <if test="weight != null">#{weight},</if>
            <if test="createBy != null">#{createBy},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="updateBy != null">#{updateBy},</if>
            <if test="updateTime != null">#{updateTime},</if>
            <if test="remark != null">#{remark},</if>
         </trim>
    </insert>

    <update id="updatePostModelConfig" parameterType="PostModelConfig">
        update post_model_config
        <trim prefix="SET" suffixOverrides=",">
            <if test="postId != null">post_id = #{postId},</if>
            <if test="postName != null">post_name = #{postName},</if>
            <if test="createBy != null">create_by = #{createBy},</if>
            <if test="createTime != null">create_time = #{createTime},</if>
            <if test="updateBy != null">update_by = #{updateBy},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
            <if test="remark != null">remark = #{remark},</if>
        </trim>
        where config_id = #{configId}
    </update>

    <update id="updatePostAbilityConfig" parameterType="PostAbilityConfig">
        update post_ability_config
        <trim prefix="SET" suffixOverrides=",">
            <if test="postId != null">post_id = #{postId},</if>
            <if test="abilityId != null">ability_id = #{abilityId},</if>
            <if test="abilityName != null">ability_name = #{abilityName},</if>
            <if test="threshold != null">threshold = #{threshold},</if>
            <if test="weight != null">weight = #{weight},</if>
            <if test="createBy != null">create_by = #{createBy},</if>
            <if test="createTime != null">create_time = #{createTime},</if>
            <if test="updateBy != null">update_by = #{updateBy},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
            <if test="remark != null">remark = #{remark},</if>
        </trim>
        where config_id = #{configId}
    </update>

    <update id="updatePostCourseConfig" parameterType="PostCourseConfig">
        update post_course_config
        <trim prefix="SET" suffixOverrides=",">
            <if test="postId != null">post_id = #{postId},</if>
            <if test="abilityConfigId != null">ability_config_id = #{abilityConfigId},</if>
            <if test="courseId != null">course_id = #{courseId},</if>
            <if test="courseName != null">course_name = #{courseName},</if>
            <if test="courseObjective != null">course_objective = #{courseObjective},</if>
            <if test="courseType != null">course_type = #{courseType},</if>
            <if test="weight != null">weight = #{weight},</if>
            <if test="createBy != null">create_by = #{createBy},</if>
            <if test="createTime != null">create_time = #{createTime},</if>
            <if test="updateBy != null">update_by = #{updateBy},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
            <if test="remark != null">remark = #{remark},</if>
        </trim>
        where config_id = #{configId}
    </update>

    <delete id="deletePostModelConfigByPostId" parameterType="Long">
        delete from post_model_config where post_id = #{postId}
    </delete>

    <delete id="deletePostModelConfigByPostIds" parameterType="String">
        delete from post_model_config where post_id in 
        <foreach item="postId" collection="postIds" open="(" separator="," close=")">
            #{postId}
        </foreach>
    </delete>

    <delete id="deletePostAbilityConfigByPostId" parameterType="Long">
        delete from post_ability_config where post_id = #{postId}
    </delete>

    <delete id="deletePostCourseConfigByAbilityConfigId" parameterType="Long">
        delete from post_course_config where ability_config_id = #{abilityConfigId}
    </delete>

    <delete id="deletePostCourseConfigByPostId" parameterType="Long">
        delete from post_course_config where post_id = #{postId}
    </delete>

</mapper> 