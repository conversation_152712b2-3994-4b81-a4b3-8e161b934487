<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.project.training.mapper.ProfessionalAbilityMapper">
    
    <resultMap type="ProfessionalAbility" id="ProfessionalAbilityResult">
        <result property="abilityId"    column="ability_id"    />
        <result property="abilityName"    column="ability_name"    />
        <result property="abilityDesc"    column="ability_desc"    />
        <result property="thresholdValue"    column="threshold_value"    />
        <result property="createBy"    column="create_by"    />
        <result property="createTime"    column="create_time"    />
        <result property="updateBy"    column="update_by"    />
        <result property="updateTime"    column="update_time"    />
        <result property="remark"    column="remark"    />
    </resultMap>

    <sql id="selectProfessionalAbilityVo">
        select ability_id, ability_name, ability_desc, threshold_value, create_by, create_time, update_by, update_time, remark from professional_ability
    </sql>

    <select id="selectProfessionalAbilityList" parameterType="ProfessionalAbility" resultMap="ProfessionalAbilityResult">
        <include refid="selectProfessionalAbilityVo"/>
        <where>  
            <if test="abilityName != null  and abilityName != ''"> and ability_name like concat('%', #{abilityName}, '%')</if>
            <if test="abilityDesc != null  and abilityDesc != ''"> and ability_desc like concat('%', #{abilityDesc}, '%')</if>
            <if test="thresholdValue != null "> and threshold_value = #{thresholdValue}</if>
        </where>
    </select>
    
    <select id="selectProfessionalAbilityByAbilityId" parameterType="Long" resultMap="ProfessionalAbilityResult">
        <include refid="selectProfessionalAbilityVo"/>
        where ability_id = #{abilityId}
    </select>
        
    <insert id="insertProfessionalAbility" parameterType="ProfessionalAbility" useGeneratedKeys="true" keyProperty="abilityId">
        insert into professional_ability
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="abilityName != null and abilityName != ''">ability_name,</if>
            <if test="abilityDesc != null">ability_desc,</if>
            <if test="thresholdValue != null">threshold_value,</if>
            <if test="createBy != null">create_by,</if>
            <if test="createTime != null">create_time,</if>
            <if test="updateBy != null">update_by,</if>
            <if test="updateTime != null">update_time,</if>
            <if test="remark != null">remark,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="abilityName != null and abilityName != ''">#{abilityName},</if>
            <if test="abilityDesc != null">#{abilityDesc},</if>
            <if test="thresholdValue != null">#{thresholdValue},</if>
            <if test="createBy != null">#{createBy},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="updateBy != null">#{updateBy},</if>
            <if test="updateTime != null">#{updateTime},</if>
            <if test="remark != null">#{remark},</if>
         </trim>
    </insert>

    <update id="updateProfessionalAbility" parameterType="ProfessionalAbility">
        update professional_ability
        <trim prefix="SET" suffixOverrides=",">
            <if test="abilityName != null and abilityName != ''">ability_name = #{abilityName},</if>
            <if test="abilityDesc != null">ability_desc = #{abilityDesc},</if>
            <if test="thresholdValue != null">threshold_value = #{thresholdValue},</if>
            <if test="createBy != null">create_by = #{createBy},</if>
            <if test="createTime != null">create_time = #{createTime},</if>
            <if test="updateBy != null">update_by = #{updateBy},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
            <if test="remark != null">remark = #{remark},</if>
        </trim>
        where ability_id = #{abilityId}
    </update>

    <delete id="deleteProfessionalAbilityByAbilityId" parameterType="Long">
        delete from professional_ability where ability_id = #{abilityId}
    </delete>

    <delete id="deleteProfessionalAbilityByAbilityIds" parameterType="String">
        delete from professional_ability where ability_id in 
        <foreach item="abilityId" collection="array" open="(" separator="," close=")">
            #{abilityId}
        </foreach>
    </delete>
</mapper> 