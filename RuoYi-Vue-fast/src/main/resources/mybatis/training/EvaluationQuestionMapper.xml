<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.project.training.mapper.EvaluationQuestionMapper">
    
    <resultMap type="EvaluationQuestion" id="EvaluationQuestionResult">
        <result property="questionId"    column="question_id"    />
        <result property="bankId"    column="bank_id"    />
        <result property="questionStem"    column="question_stem"    />
        <result property="questionType"    column="question_type"    />
        <result property="options"    column="options"    />
        <result property="postId"    column="post_id"    />
        <result property="abilityId"    column="ability_id"    />
        <result property="status"    column="status"    />
        <result property="createBy"    column="create_by"    />
        <result property="createTime"    column="create_time"    />
        <result property="updateBy"    column="update_by"    />
        <result property="updateTime"    column="update_time"    />
        <result property="remark"    column="remark"    />
        <result property="bankName"    column="bank_name"    />
        <result property="postName"    column="post_name"    />
        <result property="abilityName"    column="ability_name"    />
    </resultMap>

    <sql id="selectEvaluationQuestionVo">
        select q.question_id, q.bank_id, q.question_stem, q.question_type, q.options, q.post_id, q.ability_id, q.status, q.create_by, q.create_time, q.update_by, q.update_time, q.remark, b.bank_name, p.post_name, a.ability_name
        from evaluation_question q
        left join evaluation_question_bank b on q.bank_id = b.bank_id
        left join post_model p on q.post_id = p.post_id
        left join professional_ability a on q.ability_id = a.ability_id
    </sql>

    <select id="selectEvaluationQuestionList" parameterType="EvaluationQuestion" resultMap="EvaluationQuestionResult">
        <include refid="selectEvaluationQuestionVo"/>
        <where>  
            <if test="bankId != null "> and q.bank_id = #{bankId}</if>
            <if test="questionStem != null  and questionStem != ''"> and q.question_stem like concat('%', #{questionStem}, '%')</if>
            <if test="questionType != null  and questionType != ''"> and q.question_type = #{questionType}</if>
            <if test="status != null  and status != ''"> and q.status = #{status}</if>
        </where>
        order by q.create_time desc
    </select>
    
    <select id="selectEvaluationQuestionByQuestionId" parameterType="Long" resultMap="EvaluationQuestionResult">
        <include refid="selectEvaluationQuestionVo"/>
        where q.question_id = #{questionId}
    </select>
        
    <insert id="insertEvaluationQuestion" parameterType="EvaluationQuestion" useGeneratedKeys="true" keyProperty="questionId">
        insert into evaluation_question
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="bankId != null">bank_id,</if>
            <if test="questionStem != null and questionStem != ''">question_stem,</if>
            <if test="questionType != null and questionType != ''">question_type,</if>
            <if test="options != null and options != ''">options,</if>
            <if test="postId != null">post_id,</if>
            <if test="abilityId != null">ability_id,</if>
            <if test="status != null">status,</if>
            <if test="createBy != null">create_by,</if>
            <if test="createTime != null">create_time,</if>
            <if test="updateBy != null">update_by,</if>
            <if test="updateTime != null">update_time,</if>
            <if test="remark != null">remark,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="bankId != null">#{bankId},</if>
            <if test="questionStem != null and questionStem != ''">#{questionStem},</if>
            <if test="questionType != null and questionType != ''">#{questionType},</if>
            <if test="options != null and options != ''">#{options},</if>
            <if test="postId != null">#{postId},</if>
            <if test="abilityId != null">#{abilityId},</if>
            <if test="status != null">#{status},</if>
            <if test="createBy != null">#{createBy},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="updateBy != null">#{updateBy},</if>
            <if test="updateTime != null">#{updateTime},</if>
            <if test="remark != null">#{remark},</if>
         </trim>
    </insert>

    <update id="updateEvaluationQuestion" parameterType="EvaluationQuestion">
        update evaluation_question
        <trim prefix="SET" suffixOverrides=",">
            <if test="bankId != null">bank_id = #{bankId},</if>
            <if test="questionStem != null and questionStem != ''">question_stem = #{questionStem},</if>
            <if test="questionType != null and questionType != ''">question_type = #{questionType},</if>
            <if test="options != null and options != ''">options = #{options},</if>
            <if test="postId != null">post_id = #{postId},</if>
            <if test="abilityId != null">ability_id = #{abilityId},</if>
            <if test="status != null">status = #{status},</if>
            <if test="createBy != null">create_by = #{createBy},</if>
            <if test="createTime != null">create_time = #{createTime},</if>
            <if test="updateBy != null">update_by = #{updateBy},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
            <if test="remark != null">remark = #{remark},</if>
        </trim>
        where question_id = #{questionId}
    </update>

    <delete id="deleteEvaluationQuestionByQuestionId" parameterType="Long">
        delete from evaluation_question where question_id = #{questionId}
    </delete>

    <delete id="deleteEvaluationQuestionByQuestionIds" parameterType="String">
        delete from evaluation_question where question_id in 
        <foreach item="questionId" collection="array" open="(" separator="," close=")">
            #{questionId}
        </foreach>
    </delete>

    <delete id="deleteEvaluationQuestionByBankId" parameterType="Long">
        delete from evaluation_question where bank_id = #{bankId}
    </delete>
</mapper> 