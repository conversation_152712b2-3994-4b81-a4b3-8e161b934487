package com.ruoyi.project.system.controller;

import java.util.List;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import com.ruoyi.project.system.domain.Semester;
import com.ruoyi.project.system.service.ISemesterService;
import com.ruoyi.framework.web.controller.BaseController;
import com.ruoyi.framework.web.domain.AjaxResult;

/**
 * 学期Controller
 * 
 * <AUTHOR>
 * @date 2024-01-01
 */
@RestController
@RequestMapping("/system/semester")
public class SemesterController extends BaseController
{
    @Autowired
    private ISemesterService semesterService;

    /**
     * 查询学期列表（不分页）
     */
    @PreAuthorize("@ss.hasPermi('system:semester:list')")
    @GetMapping("/list")
    public AjaxResult list(Semester semester)
    {
        List<Semester> list = semesterService.selectSemesterList(semester);
        return success(list);
    }

    /**
     * 获取学期详细信息
     */
    @PreAuthorize("@ss.hasPermi('system:semester:query')")
    @GetMapping(value = "/{semesterId}")
    public AjaxResult getInfo(@PathVariable("semesterId") Long semesterId)
    {
        return success(semesterService.selectSemesterBySemesterId(semesterId));
    }
} 