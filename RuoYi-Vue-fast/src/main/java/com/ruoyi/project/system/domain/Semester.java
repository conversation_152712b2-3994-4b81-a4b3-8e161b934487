package com.ruoyi.project.system.domain;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.Size;

/**
 * 学期对象 semester
 * 
 * <AUTHOR>
 * @date 2024-01-01
 */
public class Semester
{
    private static final long serialVersionUID = 1L;

    /** 学期ID */
    private Long semesterId;

    /** 学期编码（如：2024-2025-1） */
    @NotBlank(message = "学期编码不能为空")
    @Size(min = 0, max = 50, message = "学期编码长度不能超过50个字符")
    private String semesterCode;

    /** 学期名称（如：2024-2025-1） */
    @NotBlank(message = "学期名称不能为空")
    @Size(min = 0, max = 50, message = "学期名称长度不能超过50个字符")
    private String semesterName;

    public void setSemesterId(Long semesterId) 
    {
        this.semesterId = semesterId;
    }

    public Long getSemesterId() 
    {
        return semesterId;
    }

    public void setSemesterCode(String semesterCode) 
    {
        this.semesterCode = semesterCode;
    }

    public String getSemesterCode() 
    {
        return semesterCode;
    }

    public void setSemesterName(String semesterName) 
    {
        this.semesterName = semesterName;
    }

    public String getSemesterName() 
    {
        return semesterName;
    }

    @Override
    public String toString() {
        return new StringBuilder()
            .append("Semester{")
            .append("semesterId=").append(semesterId)
            .append(", semesterCode='").append(semesterCode).append('\'')
            .append(", semesterName='").append(semesterName).append('\'')
            .append("}")
            .toString();
    }
} 