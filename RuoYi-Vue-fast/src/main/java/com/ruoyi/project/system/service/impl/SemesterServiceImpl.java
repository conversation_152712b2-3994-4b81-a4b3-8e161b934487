package com.ruoyi.project.system.service.impl;

import java.util.List;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import com.ruoyi.project.system.mapper.SemesterMapper;
import com.ruoyi.project.system.domain.Semester;
import com.ruoyi.project.system.service.ISemesterService;

/**
 * 学期Service业务层处理
 * 
 * <AUTHOR>
 * @date 2024-01-01
 */
@Service
public class SemesterServiceImpl implements ISemesterService 
{
    @Autowired
    private SemesterMapper semesterMapper;

    /**
     * 查询学期
     * 
     * @param semesterId 学期主键
     * @return 学期
     */
    @Override
    public Semester selectSemesterBySemesterId(Long semesterId)
    {
        return semesterMapper.selectSemesterBySemesterId(semesterId);
    }

    /**
     * 查询学期列表
     * 
     * @param semester 学期
     * @return 学期
     */
    @Override
    public List<Semester> selectSemesterList(Semester semester)
    {
        return semesterMapper.selectSemesterList(semester);
    }
} 