package com.ruoyi.project.training.service;

import java.util.List;
import com.ruoyi.project.training.domain.TrainingProgram;
import com.ruoyi.project.training.domain.TrainingProgramImport;

/**
 * 人才培养方案Service接口
 * 
 * <AUTHOR>
 * @date 2024-01-01
 */
public interface ITrainingProgramService 
{
    /**
     * 查询人才培养方案
     * 
     * @param programId 人才培养方案主键
     * @return 人才培养方案
     */
    public TrainingProgram selectTrainingProgramByProgramId(Long programId);

    /**
     * 查询人才培养方案列表
     * 
     * @param trainingProgram 人才培养方案
     * @return 人才培养方案集合
     */
    public List<TrainingProgram> selectTrainingProgramList(TrainingProgram trainingProgram);

    /**
     * 新增人才培养方案
     * 
     * @param trainingProgram 人才培养方案
     * @return 结果
     */
    public int insertTrainingProgram(TrainingProgram trainingProgram);

    /**
     * 修改人才培养方案
     * 
     * @param trainingProgram 人才培养方案
     * @return 结果
     */
    public int updateTrainingProgram(TrainingProgram trainingProgram);

    /**
     * 批量删除人才培养方案
     * 
     * @param programIds 需要删除的人才培养方案主键集合
     * @return 结果
     */
    public int deleteTrainingProgramByProgramIds(Long[] programIds);

    /**
     * 删除人才培养方案信息
     *
     * @param programId 人才培养方案主键
     * @return 结果
     */
    public int deleteTrainingProgramByProgramId(Long programId);

    /**
     * 导入人才培养方案数据
     *
     * @param programList 人才培养方案数据列表
     * @param isUpdateSupport 是否更新支持，如果已存在，则进行更新数据
     * @param isOverwrite 是否覆盖数据，如果为true则先清空所有数据再导入
     * @param operName 操作用户
     * @return 结果
     */
    public String importProgram(List<TrainingProgramImport> programList, Boolean isUpdateSupport, Boolean isOverwrite, String operName);
} 