package com.ruoyi.project.training.mapper;

import java.util.List;
import com.ruoyi.project.training.domain.EvaluationQuestion;

/**
 * 评价模型题目Mapper接口
 * 
 * <AUTHOR>
 * @date 2024-01-01
 */
public interface EvaluationQuestionMapper 
{
    /**
     * 查询评价模型题目
     * 
     * @param questionId 评价模型题目主键
     * @return 评价模型题目
     */
    public EvaluationQuestion selectEvaluationQuestionByQuestionId(Long questionId);

    /**
     * 查询评价模型题目列表
     * 
     * @param evaluationQuestion 评价模型题目
     * @return 评价模型题目集合
     */
    public List<EvaluationQuestion> selectEvaluationQuestionList(EvaluationQuestion evaluationQuestion);

    /**
     * 新增评价模型题目
     * 
     * @param evaluationQuestion 评价模型题目
     * @return 结果
     */
    public int insertEvaluationQuestion(EvaluationQuestion evaluationQuestion);

    /**
     * 修改评价模型题目
     * 
     * @param evaluationQuestion 评价模型题目
     * @return 结果
     */
    public int updateEvaluationQuestion(EvaluationQuestion evaluationQuestion);

    /**
     * 删除评价模型题目
     * 
     * @param questionId 评价模型题目主键
     * @return 结果
     */
    public int deleteEvaluationQuestionByQuestionId(Long questionId);

    /**
     * 批量删除评价模型题目
     * 
     * @param questionIds 需要删除的数据主键集合
     * @return 结果
     */
    public int deleteEvaluationQuestionByQuestionIds(Long[] questionIds);

    /**
     * 根据题库ID删除题目
     * 
     * @param bankId 题库ID
     * @return 结果
     */
    public int deleteEvaluationQuestionByBankId(Long bankId);
} 