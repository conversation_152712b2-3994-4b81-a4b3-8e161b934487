package com.ruoyi.project.training.mapper;

import java.util.List;
import org.apache.ibatis.annotations.Param;
import com.ruoyi.project.training.domain.PostModelConfig;
import com.ruoyi.project.training.domain.PostAbilityConfig;
import com.ruoyi.project.training.domain.PostCourseConfig;

/**
 * 岗位模型配置Mapper接口
 * 
 * <AUTHOR>
 * @date 2024-01-01
 */
public interface PostModelConfigMapper 
{
    /**
     * 查询岗位模型配置
     * 
     * @param postId 岗位ID
     * @return 岗位模型配置
     */
    public PostModelConfig selectPostModelConfigByPostId(Long postId);

    /**
     * 查询岗位模型配置列表
     * 
     * @param postModelConfig 岗位模型配置
     * @return 岗位模型配置集合
     */
    public List<PostModelConfig> selectPostModelConfigList(PostModelConfig postModelConfig);

    /**
     * 新增岗位模型配置
     * 
     * @param postModelConfig 岗位模型配置
     * @return 结果
     */
    public int insertPostModelConfig(PostModelConfig postModelConfig);

    /**
     * 修改岗位模型配置
     * 
     * @param postModelConfig 岗位模型配置
     * @return 结果
     */
    public int updatePostModelConfig(PostModelConfig postModelConfig);

    /**
     * 删除岗位模型配置
     * 
     * @param postId 岗位ID
     * @return 结果
     */
    public int deletePostModelConfigByPostId(Long postId);

    /**
     * 批量删除岗位模型配置
     *
     * @param postIds 需要删除的岗位ID集合
     * @return 结果
     */
    public int deletePostModelConfigByPostIds(@Param("postIds") Long[] postIds);

    /**
     * 查询岗位职业能力配置列表
     * 
     * @param postId 岗位ID
     * @return 职业能力配置集合
     */
    public List<PostAbilityConfig> selectPostAbilityConfigList(Long postId);

    /**
     * 新增岗位职业能力配置
     * 
     * @param postAbilityConfig 职业能力配置
     * @return 结果
     */
    public int insertPostAbilityConfig(PostAbilityConfig postAbilityConfig);

    /**
     * 修改岗位职业能力配置
     * 
     * @param postAbilityConfig 职业能力配置
     * @return 结果
     */
    public int updatePostAbilityConfig(PostAbilityConfig postAbilityConfig);

    /**
     * 删除岗位职业能力配置
     * 
     * @param postId 岗位ID
     * @return 结果
     */
    public int deletePostAbilityConfigByPostId(Long postId);

    /**
     * 查询岗位课程配置列表
     * 
     * @param abilityConfigId 职业能力配置ID
     * @return 课程配置集合
     */
    public List<PostCourseConfig> selectPostCourseConfigList(Long abilityConfigId);

    /**
     * 新增岗位课程配置
     * 
     * @param postCourseConfig 课程配置
     * @return 结果
     */
    public int insertPostCourseConfig(PostCourseConfig postCourseConfig);

    /**
     * 修改岗位课程配置
     * 
     * @param postCourseConfig 课程配置
     * @return 结果
     */
    public int updatePostCourseConfig(PostCourseConfig postCourseConfig);

    /**
     * 删除岗位课程配置
     * 
     * @param abilityConfigId 职业能力配置ID
     * @return 结果
     */
    public int deletePostCourseConfigByAbilityConfigId(Long abilityConfigId);

    /**
     * 删除岗位所有课程配置
     * 
     * @param postId 岗位ID
     * @return 结果
     */
    public int deletePostCourseConfigByPostId(Long postId);
} 