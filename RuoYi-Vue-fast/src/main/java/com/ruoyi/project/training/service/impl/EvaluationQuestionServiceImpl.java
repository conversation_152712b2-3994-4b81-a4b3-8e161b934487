package com.ruoyi.project.training.service.impl;

import java.util.List;
import com.ruoyi.common.utils.DateUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import com.ruoyi.project.training.mapper.EvaluationQuestionMapper;
import com.ruoyi.project.training.domain.EvaluationQuestion;
import com.ruoyi.project.training.service.IEvaluationQuestionService;
import com.ruoyi.common.utils.SecurityUtils;

/**
 * 评价模型题目Service业务层处理
 * 
 * <AUTHOR>
 * @date 2024-01-01
 */
@Service
public class EvaluationQuestionServiceImpl implements IEvaluationQuestionService
{
    @Autowired
    private EvaluationQuestionMapper evaluationQuestionMapper;

    /**
     * 查询评价模型题目
     * 
     * @param questionId 评价模型题目主键
     * @return 评价模型题目
     */
    @Override
    public EvaluationQuestion selectEvaluationQuestionByQuestionId(Long questionId)
    {
        return evaluationQuestionMapper.selectEvaluationQuestionByQuestionId(questionId);
    }

    /**
     * 查询评价模型题目列表
     * 
     * @param evaluationQuestion 评价模型题目
     * @return 评价模型题目
     */
    @Override
    public List<EvaluationQuestion> selectEvaluationQuestionList(EvaluationQuestion evaluationQuestion)
    {
        return evaluationQuestionMapper.selectEvaluationQuestionList(evaluationQuestion);
    }

    /**
     * 新增评价模型题目
     * 
     * @param evaluationQuestion 评价模型题目
     * @return 结果
     */
    @Override
    public int insertEvaluationQuestion(EvaluationQuestion evaluationQuestion)
    {
        evaluationQuestion.setCreateBy(SecurityUtils.getUsername());
        evaluationQuestion.setCreateTime(DateUtils.getNowDate());
        return evaluationQuestionMapper.insertEvaluationQuestion(evaluationQuestion);
    }

    /**
     * 修改评价模型题目
     * 
     * @param evaluationQuestion 评价模型题目
     * @return 结果
     */
    @Override
    public int updateEvaluationQuestion(EvaluationQuestion evaluationQuestion)
    {
        evaluationQuestion.setUpdateBy(SecurityUtils.getUsername());
        evaluationQuestion.setUpdateTime(DateUtils.getNowDate());
        return evaluationQuestionMapper.updateEvaluationQuestion(evaluationQuestion);
    }

    /**
     * 批量删除评价模型题目
     * 
     * @param questionIds 需要删除的评价模型题目主键
     * @return 结果
     */
    @Override
    public int deleteEvaluationQuestionByQuestionIds(Long[] questionIds)
    {
        return evaluationQuestionMapper.deleteEvaluationQuestionByQuestionIds(questionIds);
    }

    /**
     * 删除评价模型题目信息
     * 
     * @param questionId 评价模型题目主键
     * @return 结果
     */
    @Override
    public int deleteEvaluationQuestionByQuestionId(Long questionId)
    {
        return evaluationQuestionMapper.deleteEvaluationQuestionByQuestionId(questionId);
    }
} 