package com.ruoyi.project.training.domain;

import com.ruoyi.framework.aspectj.lang.annotation.Excel;

/**
 * 人才培养方案导入模板对象
 * 
 * <AUTHOR>
 * @date 2024-01-01
 */
public class TrainingProgramImport
{
    private static final long serialVersionUID = 1L;

    /** 方案名称 */
    @Excel(name = "方案名称")
    private String programName;

    /** 方案简介 */
    @Excel(name = "方案简介")
    private String programIntro;

    /** 培训时间(年) */
    @Excel(name = "培训时间(年)")
    private Integer trainingTime;

    /** 预计培养人数(人) */
    @Excel(name = "预计培养人数(人)")
    private Integer traineeCount;

    /** 培养目标 */
    @Excel(name = "培养目标")
    private String trainingGoal;

    /** 培养规格 */
    @Excel(name = "培养规格")
    private String trainingSpec;

    public String getProgramName() 
    {
        return programName;
    }

    public void setProgramName(String programName) 
    {
        this.programName = programName;
    }

    public String getProgramIntro() 
    {
        return programIntro;
    }

    public void setProgramIntro(String programIntro) 
    {
        this.programIntro = programIntro;
    }

    public Integer getTrainingTime() 
    {
        return trainingTime;
    }

    public void setTrainingTime(Integer trainingTime) 
    {
        this.trainingTime = trainingTime;
    }

    public Integer getTraineeCount() 
    {
        return traineeCount;
    }

    public void setTraineeCount(Integer traineeCount) 
    {
        this.traineeCount = traineeCount;
    }

    public String getTrainingGoal() 
    {
        return trainingGoal;
    }

    public void setTrainingGoal(String trainingGoal) 
    {
        this.trainingGoal = trainingGoal;
    }

    public String getTrainingSpec() 
    {
        return trainingSpec;
    }

    public void setTrainingSpec(String trainingSpec) 
    {
        this.trainingSpec = trainingSpec;
    }

    @Override
    public String toString() {
        return "TrainingProgramImport{" +
                "programName='" + programName + '\'' +
                ", programIntro='" + programIntro + '\'' +
                ", trainingTime=" + trainingTime +
                ", traineeCount=" + traineeCount +
                ", trainingGoal='" + trainingGoal + '\'' +
                ", trainingSpec='" + trainingSpec + '\'' +
                '}';
    }
} 