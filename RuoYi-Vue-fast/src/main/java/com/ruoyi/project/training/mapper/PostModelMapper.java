package com.ruoyi.project.training.mapper;

import java.util.List;
import org.apache.ibatis.annotations.Param;
import com.ruoyi.project.training.domain.PostModel;

/**
 * 岗位模型Mapper接口
 * 
 * <AUTHOR>
 * @date 2024-01-01
 */
public interface PostModelMapper 
{
    /**
     * 查询岗位模型
     * 
     * @param postId 岗位模型主键
     * @return 岗位模型
     */
    public PostModel selectPostModelByPostId(Long postId);

    /**
     * 查询岗位模型列表
     * 
     * @param postModel 岗位模型
     * @return 岗位模型集合
     */
    public List<PostModel> selectPostModelList(PostModel postModel);

    /**
     * 新增岗位模型
     * 
     * @param postModel 岗位模型
     * @return 结果
     */
    public int insertPostModel(PostModel postModel);

    /**
     * 修改岗位模型
     * 
     * @param postModel 岗位模型
     * @return 结果
     */
    public int updatePostModel(PostModel postModel);

    /**
     * 删除岗位模型
     * 
     * @param postId 岗位模型主键
     * @return 结果
     */
    public int deletePostModelByPostId(Long postId);

    /**
     * 批量删除岗位模型
     *
     * @param postIds 需要删除的岗位模型主键集合
     * @return 结果
     */
    public int deletePostModelByPostIds(@Param("postIds") Long[] postIds);

    /**
     * 删除岗位企业关联
     *
     * @param postId 岗位ID
     * @return 结果
     */
    public int deletePostModelCompanyByPostId(Long postId);

    /**
     * 批量删除岗位企业关联
     *
     * @param postIds 岗位ID数组
     * @return 结果
     */
    public int deletePostModelCompanyByPostIds(@Param("postIds") Long[] postIds);

    /**
     * 新增岗位企业关联
     *
     * @param postId 岗位ID
     * @param companyIds 企业ID列表
     * @return 结果
     */
    public int insertPostModelCompany(@Param("postId") Long postId, @Param("companyIds") Long[] companyIds);

    /**
     * 查询岗位关联的企业ID列表
     *
     * @param postId 岗位ID
     * @return 企业ID列表
     */
    public Long[] selectCompanyIdsByPostId(Long postId);
}
