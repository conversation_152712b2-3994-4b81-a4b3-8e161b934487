package com.ruoyi.project.training.controller;

import java.util.List;
import javax.servlet.http.HttpServletResponse;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import com.ruoyi.framework.aspectj.lang.annotation.Log;
import com.ruoyi.framework.aspectj.lang.enums.BusinessType;
import com.ruoyi.project.training.domain.EvaluationQuestion;
import com.ruoyi.project.training.service.IEvaluationQuestionService;
import com.ruoyi.framework.web.controller.BaseController;
import com.ruoyi.framework.web.domain.AjaxResult;
import com.ruoyi.common.utils.poi.ExcelUtil;
import com.ruoyi.framework.web.page.TableDataInfo;

/**
 * 评价模型题目Controller
 * 
 * <AUTHOR>
 * @date 2024-01-01
 */
@RestController
@RequestMapping("/training/question")
public class EvaluationQuestionController extends BaseController
{
    @Autowired
    private IEvaluationQuestionService evaluationQuestionService;

    /**
     * 查询评价模型题目列表
     */
    @PreAuthorize("@ss.hasPermi('training:question:list')")
    @GetMapping("/list")
    public TableDataInfo list(EvaluationQuestion evaluationQuestion)
    {
        startPage();
        List<EvaluationQuestion> list = evaluationQuestionService.selectEvaluationQuestionList(evaluationQuestion);
        return getDataTable(list);
    }

    /**
     * 导出评价模型题目列表
     */
    @PreAuthorize("@ss.hasPermi('training:question:list')")
    @Log(title = "评价模型题目", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, EvaluationQuestion evaluationQuestion)
    {
        List<EvaluationQuestion> list = evaluationQuestionService.selectEvaluationQuestionList(evaluationQuestion);
        ExcelUtil<EvaluationQuestion> util = new ExcelUtil<EvaluationQuestion>(EvaluationQuestion.class);
        util.exportExcel(response, list, "评价模型题目数据");
    }

    /**
     * 获取评价模型题目详细信息
     */
    @PreAuthorize("@ss.hasPermi('training:question:list')")
    @GetMapping(value = "/{questionId}")
    public AjaxResult getInfo(@PathVariable("questionId") Long questionId)
    {
        return success(evaluationQuestionService.selectEvaluationQuestionByQuestionId(questionId));
    }

    /**
     * 新增评价模型题目
     */
    @PreAuthorize("@ss.hasPermi('training:question:list')")
    @Log(title = "评价模型题目", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody EvaluationQuestion evaluationQuestion)
    {
        return toAjax(evaluationQuestionService.insertEvaluationQuestion(evaluationQuestion));
    }

    /**
     * 修改评价模型题目
     */
    @PreAuthorize("@ss.hasPermi('training:question:list')")
    @Log(title = "评价模型题目", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody EvaluationQuestion evaluationQuestion)
    {
        return toAjax(evaluationQuestionService.updateEvaluationQuestion(evaluationQuestion));
    }

    /**
     * 删除评价模型题目
     */
    @PreAuthorize("@ss.hasPermi('training:question:list')")
    @Log(title = "评价模型题目", businessType = BusinessType.DELETE)
	@DeleteMapping("/{questionIds}")
    public AjaxResult remove(@PathVariable Long[] questionIds)
    {
        return toAjax(evaluationQuestionService.deleteEvaluationQuestionByQuestionIds(questionIds));
    }

    /**
     * 更新评价模型题目状态
     */
    @PreAuthorize("@ss.hasPermi('training:question:list')")
    @Log(title = "评价模型题目状态", businessType = BusinessType.UPDATE)
    @PutMapping("/status")
    public AjaxResult updateStatus(@RequestBody EvaluationQuestion evaluationQuestion)
    {
        return toAjax(evaluationQuestionService.updateEvaluationQuestion(evaluationQuestion));
    }
} 