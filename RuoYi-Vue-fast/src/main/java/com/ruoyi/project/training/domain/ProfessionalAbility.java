package com.ruoyi.project.training.domain;

import com.ruoyi.framework.aspectj.lang.annotation.Excel;
import com.ruoyi.framework.web.domain.BaseEntity;

/**
 * 职业能力对象 professional_ability
 * 
 * <AUTHOR>
 * @date 2024-01-01
 */
public class ProfessionalAbility extends BaseEntity
{
    private static final long serialVersionUID = 1L;

    /** 职业能力ID */
    private Long abilityId;

    /** 职业能力名称 */
    @Excel(name = "职业能力名称")
    private String abilityName;

    /** 职业能力描述 */
    @Excel(name = "职业能力描述")
    private String abilityDesc;

    /** 达标阈值 */
    @Excel(name = "达标阈值")
    private Integer thresholdValue;

    public void setAbilityId(Long abilityId) 
    {
        this.abilityId = abilityId;
    }

    public Long getAbilityId() 
    {
        return abilityId;
    }

    public void setAbilityName(String abilityName) 
    {
        this.abilityName = abilityName;
    }

    public String getAbilityName() 
    {
        return abilityName;
    }

    public void setAbilityDesc(String abilityDesc) 
    {
        this.abilityDesc = abilityDesc;
    }

    public String getAbilityDesc() 
    {
        return abilityDesc;
    }

    public void setThresholdValue(Integer thresholdValue) 
    {
        this.thresholdValue = thresholdValue;
    }

    public Integer getThresholdValue() 
    {
        return thresholdValue;
    }

    @Override
    public String toString() {
        return "ProfessionalAbility{" +
                "abilityId=" + abilityId +
                ", abilityName='" + abilityName + '\'' +
                ", abilityDesc='" + abilityDesc + '\'' +
                ", thresholdValue=" + thresholdValue +
                '}';
    }
} 