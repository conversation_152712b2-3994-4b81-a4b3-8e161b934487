package com.ruoyi.project.training.service;

import java.util.List;
import com.ruoyi.project.training.domain.PostModel;

/**
 * 岗位模型Service接口
 * 
 * <AUTHOR>
 * @date 2024-01-01
 */
public interface IPostModelService 
{
    /**
     * 查询岗位模型
     * 
     * @param postId 岗位模型主键
     * @return 岗位模型
     */
    public PostModel selectPostModelByPostId(Long postId);

    /**
     * 查询岗位模型列表
     * 
     * @param postModel 岗位模型
     * @return 岗位模型集合
     */
    public List<PostModel> selectPostModelList(PostModel postModel);

    /**
     * 新增岗位模型
     * 
     * @param postModel 岗位模型
     * @return 结果
     */
    public int insertPostModel(PostModel postModel);

    /**
     * 修改岗位模型
     * 
     * @param postModel 岗位模型
     * @return 结果
     */
    public int updatePostModel(PostModel postModel);

    /**
     * 批量删除岗位模型
     * 
     * @param postIds 需要删除的岗位模型主键集合
     * @return 结果
     */
    public int deletePostModelByPostIds(Long[] postIds);

    /**
     * 删除岗位模型信息
     * 
     * @param postId 岗位模型主键
     * @return 结果
     */
    public int deletePostModelByPostId(Long postId);

    /**
     * 复制岗位模型
     * 
     * @param postModel 岗位模型
     * @return 结果
     */
    public int copyPostModel(PostModel postModel);
}
