package com.ruoyi.project.training.service;

import java.util.List;
import com.ruoyi.project.training.domain.PostModelConfig;
import com.ruoyi.project.training.domain.PostAbilityConfig;
import com.ruoyi.project.training.domain.PostCourseConfig;

/**
 * 岗位模型配置Service接口
 * 
 * <AUTHOR>
 * @date 2024-01-01
 */
public interface IPostModelConfigService 
{
    /**
     * 查询岗位模型配置
     * 
     * @param postId 岗位ID
     * @return 岗位模型配置
     */
    public PostModelConfig selectPostModelConfigByPostId(Long postId);

    /**
     * 查询岗位模型配置列表
     * 
     * @param postModelConfig 岗位模型配置
     * @return 岗位模型配置集合
     */
    public List<PostModelConfig> selectPostModelConfigList(PostModelConfig postModelConfig);

    /**
     * 新增岗位模型配置
     * 
     * @param postModelConfig 岗位模型配置
     * @return 结果
     */
    public int insertPostModelConfig(PostModelConfig postModelConfig);

    /**
     * 修改岗位模型配置
     * 
     * @param postModelConfig 岗位模型配置
     * @return 结果
     */
    public int updatePostModelConfig(PostModelConfig postModelConfig);

    /**
     * 批量删除岗位模型配置
     * 
     * @param postIds 需要删除的岗位ID集合
     * @return 结果
     */
    public int deletePostModelConfigByPostIds(Long[] postIds);

    /**
     * 删除岗位模型配置信息
     * 
     * @param postId 岗位ID
     * @return 结果
     */
    public int deletePostModelConfigByPostId(Long postId);

    /**
     * 保存岗位模型配置（包含职业能力和课程配置）
     * 
     * @param postModelConfig 岗位模型配置
     * @return 结果
     */
    public int savePostModelConfig(PostModelConfig postModelConfig);

    /**
     * 导入岗位模型配置数据
     * 
     * @param configList 岗位模型配置数据列表
     * @param updateSupport 是否支持更新
     * @param overwrite 是否覆盖
     * @param operName 操作用户
     * @return 结果
     */
    public String importPostModelConfig(List<PostModelConfig> configList, Boolean updateSupport, Boolean overwrite, String operName);
} 