package com.ruoyi.project.training.controller;

import java.util.List;
import javax.servlet.http.HttpServletResponse;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.multipart.MultipartFile;
import com.ruoyi.framework.aspectj.lang.annotation.Log;
import com.ruoyi.framework.aspectj.lang.enums.BusinessType;
import com.ruoyi.project.training.domain.TrainingProgram;
import com.ruoyi.project.training.domain.TrainingProgramImport;
import com.ruoyi.project.training.service.ITrainingProgramService;
import com.ruoyi.framework.web.controller.BaseController;
import com.ruoyi.framework.web.domain.AjaxResult;
import com.ruoyi.common.utils.poi.ExcelUtil;
import com.ruoyi.framework.web.page.TableDataInfo;

/**
 * 人才培养方案Controller
 * 
 * <AUTHOR>
 * @date 2024-01-01
 */
@RestController
@RequestMapping("/training/program")
public class TrainingProgramController extends BaseController
{
    @Autowired
    private ITrainingProgramService trainingProgramService;

    /**
     * 查询人才培养方案列表
     */
    @PreAuthorize("@ss.hasPermi('training:program:list')")
    @GetMapping("/list")
    public TableDataInfo list(TrainingProgram trainingProgram)
    {
        startPage();
        List<TrainingProgram> list = trainingProgramService.selectTrainingProgramList(trainingProgram);
        return getDataTable(list);
    }

    /**
     * 导出人才培养方案列表
     */
    @PreAuthorize("@ss.hasPermi('training:program:list')")
    @Log(title = "人才培养方案", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, TrainingProgram trainingProgram)
    {
        List<TrainingProgram> list = trainingProgramService.selectTrainingProgramList(trainingProgram);
        ExcelUtil<TrainingProgram> util = new ExcelUtil<TrainingProgram>(TrainingProgram.class);
        util.exportExcel(response, list, "人才培养方案数据");
    }

    /**
     * 获取人才培养方案详细信息
     */
    @PreAuthorize("@ss.hasPermi('training:program:list')")
    @GetMapping(value = "/{programId}")
    public AjaxResult getInfo(@PathVariable("programId") Long programId)
    {
        return success(trainingProgramService.selectTrainingProgramByProgramId(programId));
    }

    /**
     * 新增人才培养方案
     */
    @PreAuthorize("@ss.hasPermi('training:program:add')")
    @Log(title = "人才培养方案", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody TrainingProgram trainingProgram)
    {
        trainingProgram.setCreateBy(getUsername());
        return toAjax(trainingProgramService.insertTrainingProgram(trainingProgram));
    }

    /**
     * 修改人才培养方案
     */
    @PreAuthorize("@ss.hasPermi('training:program:edit')")
    @Log(title = "人才培养方案", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody TrainingProgram trainingProgram)
    {
        trainingProgram.setUpdateBy(getUsername());
        return toAjax(trainingProgramService.updateTrainingProgram(trainingProgram));
    }

    /**
     * 删除人才培养方案
     */
    @PreAuthorize("@ss.hasPermi('training:program:remove')")
    @Log(title = "人才培养方案", businessType = BusinessType.DELETE)
    @PostMapping("/remove")
    public AjaxResult remove(@RequestBody Long[] programIds)
    {
        return toAjax(trainingProgramService.deleteTrainingProgramByProgramIds(programIds));
    }

    /**
     * 导入人才培养方案数据
     */
    @Log(title = "人才培养方案", businessType = BusinessType.IMPORT)
    @PreAuthorize("@ss.hasPermi('training:program:import')")
    @PostMapping("/importData")
    public AjaxResult importData(MultipartFile file,
                                @RequestParam(value = "updateSupport", defaultValue = "false") boolean updateSupport,
                                @RequestParam(value = "overwrite", defaultValue = "false") boolean overwrite) throws Exception
    {
        ExcelUtil<TrainingProgramImport> util = new ExcelUtil<TrainingProgramImport>(TrainingProgramImport.class);
        List<TrainingProgramImport> programList = util.importExcel(file.getInputStream());
        String operName = getUsername();
        String message = trainingProgramService.importProgram(programList, updateSupport, overwrite, operName);
        return success(message);
    }

    /**
     * 下载人才培养方案导入模板
     */
    @PostMapping("/importTemplate")
    public void importTemplate(HttpServletResponse response)
    {
        ExcelUtil<TrainingProgramImport> util = new ExcelUtil<TrainingProgramImport>(TrainingProgramImport.class);
        util.importTemplateExcel(response, "人才培养方案数据");
    }
} 