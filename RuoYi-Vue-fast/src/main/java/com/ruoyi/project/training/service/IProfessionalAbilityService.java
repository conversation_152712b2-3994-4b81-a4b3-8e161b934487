package com.ruoyi.project.training.service;

import java.util.List;
import com.ruoyi.project.training.domain.ProfessionalAbility;

/**
 * 职业能力Service接口
 * 
 * <AUTHOR>
 * @date 2024-01-01
 */
public interface IProfessionalAbilityService 
{
    /**
     * 查询职业能力
     * 
     * @param abilityId 职业能力主键
     * @return 职业能力
     */
    public ProfessionalAbility selectProfessionalAbilityByAbilityId(Long abilityId);

    /**
     * 查询职业能力列表
     * 
     * @param professionalAbility 职业能力
     * @return 职业能力集合
     */
    public List<ProfessionalAbility> selectProfessionalAbilityList(ProfessionalAbility professionalAbility);

    /**
     * 新增职业能力
     * 
     * @param professionalAbility 职业能力
     * @return 结果
     */
    public int insertProfessionalAbility(ProfessionalAbility professionalAbility);

    /**
     * 修改职业能力
     * 
     * @param professionalAbility 职业能力
     * @return 结果
     */
    public int updateProfessionalAbility(ProfessionalAbility professionalAbility);

    /**
     * 批量删除职业能力
     * 
     * @param abilityIds 需要删除的职业能力主键集合
     * @return 结果
     */
    public int deleteProfessionalAbilityByAbilityIds(Long[] abilityIds);

    /**
     * 删除职业能力信息
     *
     * @param abilityId 职业能力主键
     * @return 结果
     */
    public int deleteProfessionalAbilityByAbilityId(Long abilityId);

    /**
     * 导入职业能力数据
     *
     * @param abilityList 职业能力数据列表
     * @param isUpdateSupport 是否更新支持，如果已存在，则进行更新数据
     * @param isOverwrite 是否覆盖数据，如果为true则先清空所有数据再导入
     * @param operName 操作用户
     * @return 结果
     */
    public String importAbility(List<ProfessionalAbility> abilityList, Boolean isUpdateSupport, Boolean isOverwrite, String operName);
}