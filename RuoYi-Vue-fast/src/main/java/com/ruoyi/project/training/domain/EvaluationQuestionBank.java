package com.ruoyi.project.training.domain;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.Size;
import com.ruoyi.framework.aspectj.lang.annotation.Excel;
import com.ruoyi.framework.web.domain.BaseEntity;

/**
 * 评价模型题库对象 evaluation_question_bank
 * 
 * <AUTHOR>
 * @date 2024-01-01
 */
public class EvaluationQuestionBank extends BaseEntity
{
    private static final long serialVersionUID = 1L;

    /** 题库ID */
    private Long bankId;

    /** 题库名称 */
    @Excel(name = "题库名称")
    @NotBlank(message = "题库名称不能为空")
    @Size(min = 0, max = 60, message = "题库名称长度不能超过60个字符")
    private String bankName;

    /** 状态（0正常 1停用） */
    @Excel(name = "状态", readConverterExp = "0=正常,1=停用")
    private String status;

    public void setBankId(Long bankId) 
    {
        this.bankId = bankId;
    }

    public Long getBankId() 
    {
        return bankId;
    }

    public void setBankName(String bankName) 
    {
        this.bankName = bankName;
    }

    public String getBankName() 
    {
        return bankName;
    }

    public void setStatus(String status) 
    {
        this.status = status;
    }

    public String getStatus() 
    {
        return status;
    }
} 