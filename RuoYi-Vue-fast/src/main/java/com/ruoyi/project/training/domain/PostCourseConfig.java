package com.ruoyi.project.training.domain;

import com.ruoyi.framework.aspectj.lang.annotation.Excel;
import com.ruoyi.framework.web.domain.BaseEntity;

/**
 * 岗位课程配置对象 post_course_config
 * 
 * <AUTHOR>
 * @date 2024-01-01
 */
public class PostCourseConfig extends BaseEntity
{
    private static final long serialVersionUID = 1L;

    /** 配置ID */
    private Long configId;

    /** 岗位ID */
    @Excel(name = "岗位ID")
    private Long postId;

    /** 职业能力配置ID */
    @Excel(name = "职业能力配置ID")
    private Long abilityConfigId;

    /** 课程ID */
    @Excel(name = "课程ID")
    private Long courseId;

    /** 课程名称 */
    @Excel(name = "课程名称")
    private String courseName;

    /** 课程目标 */
    @Excel(name = "课程目标")
    private String courseObjective;

    /** 课程类型（1-学校课程，2-企业课程） */
    @Excel(name = "课程类型")
    private Integer courseType;

    /** 权重（%） */
    @Excel(name = "权重")
    private Integer weight;

    public void setConfigId(Long configId) 
    {
        this.configId = configId;
    }

    public Long getConfigId() 
    {
        return configId;
    }

    public void setPostId(Long postId) 
    {
        this.postId = postId;
    }

    public Long getPostId() 
    {
        return postId;
    }

    public void setAbilityConfigId(Long abilityConfigId) 
    {
        this.abilityConfigId = abilityConfigId;
    }

    public Long getAbilityConfigId() 
    {
        return abilityConfigId;
    }

    public void setCourseId(Long courseId) 
    {
        this.courseId = courseId;
    }

    public Long getCourseId() 
    {
        return courseId;
    }

    public void setCourseName(String courseName) 
    {
        this.courseName = courseName;
    }

    public String getCourseName() 
    {
        return courseName;
    }

    public void setCourseObjective(String courseObjective) 
    {
        this.courseObjective = courseObjective;
    }

    public String getCourseObjective() 
    {
        return courseObjective;
    }

    public void setCourseType(Integer courseType) 
    {
        this.courseType = courseType;
    }

    public Integer getCourseType() 
    {
        return courseType;
    }

    public void setWeight(Integer weight) 
    {
        this.weight = weight;
    }

    public Integer getWeight() 
    {
        return weight;
    }

    @Override
    public String toString() {
        return "PostCourseConfig{" +
                "configId=" + configId +
                ", postId=" + postId +
                ", abilityConfigId=" + abilityConfigId +
                ", courseId=" + courseId +
                ", courseName='" + courseName + '\'' +
                ", courseObjective='" + courseObjective + '\'' +
                ", courseType=" + courseType +
                ", weight=" + weight +
                '}';
    }
} 