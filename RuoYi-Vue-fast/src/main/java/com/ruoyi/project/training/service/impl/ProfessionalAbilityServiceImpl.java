package com.ruoyi.project.training.service.impl;

import java.util.List;
import javax.validation.Validator;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import com.ruoyi.common.exception.ServiceException;
import com.ruoyi.common.utils.StringUtils;
import com.ruoyi.common.utils.bean.BeanValidators;
import com.ruoyi.project.training.mapper.ProfessionalAbilityMapper;
import com.ruoyi.project.training.domain.ProfessionalAbility;
import com.ruoyi.project.training.service.IProfessionalAbilityService;

/**
 * 职业能力Service业务层处理
 * 
 * <AUTHOR>
 * @date 2024-01-01
 */
@Service
public class ProfessionalAbilityServiceImpl implements IProfessionalAbilityService
{
    private static final Logger log = LoggerFactory.getLogger(ProfessionalAbilityServiceImpl.class);

    @Autowired
    private Validator validator;

    @Autowired
    private ProfessionalAbilityMapper professionalAbilityMapper;

    /**
     * 查询职业能力
     * 
     * @param abilityId 职业能力主键
     * @return 职业能力
     */
    @Override
    public ProfessionalAbility selectProfessionalAbilityByAbilityId(Long abilityId)
    {
        return professionalAbilityMapper.selectProfessionalAbilityByAbilityId(abilityId);
    }

    /**
     * 查询职业能力列表
     * 
     * @param professionalAbility 职业能力
     * @return 职业能力
     */
    @Override
    public List<ProfessionalAbility> selectProfessionalAbilityList(ProfessionalAbility professionalAbility)
    {
        return professionalAbilityMapper.selectProfessionalAbilityList(professionalAbility);
    }

    /**
     * 新增职业能力
     * 
     * @param professionalAbility 职业能力
     * @return 结果
     */
    @Override
    public int insertProfessionalAbility(ProfessionalAbility professionalAbility)
    {
        return professionalAbilityMapper.insertProfessionalAbility(professionalAbility);
    }

    /**
     * 修改职业能力
     * 
     * @param professionalAbility 职业能力
     * @return 结果
     */
    @Override
    public int updateProfessionalAbility(ProfessionalAbility professionalAbility)
    {
        return professionalAbilityMapper.updateProfessionalAbility(professionalAbility);
    }

    /**
     * 批量删除职业能力
     * 
     * @param abilityIds 需要删除的职业能力主键
     * @return 结果
     */
    @Override
    public int deleteProfessionalAbilityByAbilityIds(Long[] abilityIds)
    {
        return professionalAbilityMapper.deleteProfessionalAbilityByAbilityIds(abilityIds);
    }

    /**
     * 删除职业能力信息
     *
     * @param abilityId 职业能力主键
     * @return 结果
     */
    @Override
    public int deleteProfessionalAbilityByAbilityId(Long abilityId)
    {
        return professionalAbilityMapper.deleteProfessionalAbilityByAbilityId(abilityId);
    }

    /**
     * 导入职业能力数据
     *
     * @param abilityList 职业能力数据列表
     * @param isUpdateSupport 是否更新支持，如果已存在，则进行更新数据
     * @param isOverwrite 是否覆盖数据，如果为true则先清空所有数据再导入
     * @param operName 操作用户
     * @return 结果
     */
    @Override
    public String importAbility(List<ProfessionalAbility> abilityList, Boolean isUpdateSupport, Boolean isOverwrite, String operName)
    {
        if (StringUtils.isNull(abilityList) || abilityList.size() == 0)
        {
            throw new ServiceException("导入职业能力数据不能为空！");
        }

        // 如果是覆盖模式，先删除所有现有数据
        if (isOverwrite)
        {
            // 查询所有现有数据的ID
            List<ProfessionalAbility> existingList = professionalAbilityMapper.selectProfessionalAbilityList(new ProfessionalAbility());
            if (!existingList.isEmpty())
            {
                Long[] existingIds = existingList.stream().map(ProfessionalAbility::getAbilityId).toArray(Long[]::new);
                professionalAbilityMapper.deleteProfessionalAbilityByAbilityIds(existingIds);
                log.info("覆盖模式：已删除 {} 条现有职业能力数据", existingIds.length);
            }
        }

        int successNum = 0;
        int failureNum = 0;
        StringBuilder successMsg = new StringBuilder();
        StringBuilder failureMsg = new StringBuilder();

        for (ProfessionalAbility ability : abilityList)
        {
            try
            {
                if (isOverwrite)
                {
                    // 覆盖模式：直接插入新数据
                    BeanValidators.validateWithException(validator, ability);
                    ability.setCreateBy(operName);
                    professionalAbilityMapper.insertProfessionalAbility(ability);
                    successNum++;
                    successMsg.append("<br/>" + successNum + "、职业能力 " + ability.getAbilityName() + " 导入成功");
                }
                else
                {
                    // 非覆盖模式：检查是否存在
                    ProfessionalAbility existAbility = new ProfessionalAbility();
                    existAbility.setAbilityName(ability.getAbilityName());
                    List<ProfessionalAbility> existList = professionalAbilityMapper.selectProfessionalAbilityList(existAbility);

                    if (StringUtils.isEmpty(existList))
                    {
                        BeanValidators.validateWithException(validator, ability);
                        ability.setCreateBy(operName);
                        professionalAbilityMapper.insertProfessionalAbility(ability);
                        successNum++;
                        successMsg.append("<br/>" + successNum + "、职业能力 " + ability.getAbilityName() + " 导入成功");
                    }
                    else if (isUpdateSupport)
                    {
                        BeanValidators.validateWithException(validator, ability);
                        ProfessionalAbility updateAbility = existList.get(0);
                        ability.setAbilityId(updateAbility.getAbilityId());
                        ability.setUpdateBy(operName);
                        professionalAbilityMapper.updateProfessionalAbility(ability);
                        successNum++;
                        successMsg.append("<br/>" + successNum + "、职业能力 " + ability.getAbilityName() + " 更新成功");
                    }
                    else
                    {
                        failureNum++;
                        failureMsg.append("<br/>" + failureNum + "、职业能力 " + ability.getAbilityName() + " 已存在");
                    }
                }
            }
            catch (Exception e)
            {
                failureNum++;
                String msg = "<br/>" + failureNum + "、职业能力 " + ability.getAbilityName() + " 导入失败：";
                failureMsg.append(msg + e.getMessage());
                log.error(msg, e);
            }
        }

        if (failureNum > 0)
        {
            failureMsg.insert(0, "很抱歉，导入失败！共 " + failureNum + " 条数据格式不正确，错误如下：");
            throw new ServiceException(failureMsg.toString());
        }
        else
        {
            String modeText = isOverwrite ? "覆盖导入" : "增量导入";
            successMsg.insert(0, "恭喜您，数据已全部" + modeText + "成功！共 " + successNum + " 条，数据如下：");
        }
        return successMsg.toString();
    }
}