package com.ruoyi.project.training.service;

import java.util.List;
import com.ruoyi.project.training.domain.EvaluationQuestionBank;

/**
 * 评价模型题库Service接口
 * 
 * <AUTHOR>
 * @date 2024-01-01
 */
public interface IEvaluationQuestionBankService 
{
    /**
     * 查询评价模型题库
     * 
     * @param bankId 评价模型题库主键
     * @return 评价模型题库
     */
    public EvaluationQuestionBank selectEvaluationQuestionBankByBankId(Long bankId);

    /**
     * 查询评价模型题库列表
     * 
     * @param evaluationQuestionBank 评价模型题库
     * @return 评价模型题库集合
     */
    public List<EvaluationQuestionBank> selectEvaluationQuestionBankList(EvaluationQuestionBank evaluationQuestionBank);

    /**
     * 新增评价模型题库
     * 
     * @param evaluationQuestionBank 评价模型题库
     * @return 结果
     */
    public int insertEvaluationQuestionBank(EvaluationQuestionBank evaluationQuestionBank);

    /**
     * 修改评价模型题库
     * 
     * @param evaluationQuestionBank 评价模型题库
     * @return 结果
     */
    public int updateEvaluationQuestionBank(EvaluationQuestionBank evaluationQuestionBank);

    /**
     * 批量删除评价模型题库
     * 
     * @param bankIds 需要删除的评价模型题库主键集合
     * @return 结果
     */
    public int deleteEvaluationQuestionBankByBankIds(Long[] bankIds);

    /**
     * 删除评价模型题库信息
     * 
     * @param bankId 评价模型题库主键
     * @return 结果
     */
    public int deleteEvaluationQuestionBankByBankId(Long bankId);
} 