package com.ruoyi.project.training.mapper;

import java.util.List;
import com.ruoyi.project.training.domain.ProfessionalAbility;

/**
 * 职业能力Mapper接口
 * 
 * <AUTHOR>
 * @date 2024-01-01
 */
public interface ProfessionalAbilityMapper 
{
    /**
     * 查询职业能力
     * 
     * @param abilityId 职业能力主键
     * @return 职业能力
     */
    public ProfessionalAbility selectProfessionalAbilityByAbilityId(Long abilityId);

    /**
     * 查询职业能力列表
     * 
     * @param professionalAbility 职业能力
     * @return 职业能力集合
     */
    public List<ProfessionalAbility> selectProfessionalAbilityList(ProfessionalAbility professionalAbility);

    /**
     * 新增职业能力
     * 
     * @param professionalAbility 职业能力
     * @return 结果
     */
    public int insertProfessionalAbility(ProfessionalAbility professionalAbility);

    /**
     * 修改职业能力
     * 
     * @param professionalAbility 职业能力
     * @return 结果
     */
    public int updateProfessionalAbility(ProfessionalAbility professionalAbility);

    /**
     * 删除职业能力
     * 
     * @param abilityId 职业能力主键
     * @return 结果
     */
    public int deleteProfessionalAbilityByAbilityId(Long abilityId);

    /**
     * 批量删除职业能力
     * 
     * @param abilityIds 需要删除的数据主键集合
     * @return 结果
     */
    public int deleteProfessionalAbilityByAbilityIds(Long[] abilityIds);
} 