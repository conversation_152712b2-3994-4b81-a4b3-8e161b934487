package com.ruoyi.project.training.domain;

import com.ruoyi.framework.aspectj.lang.annotation.Excel;
import com.ruoyi.framework.web.domain.BaseEntity;

/**
 * 人才培养方案对象 training_program
 * 
 * <AUTHOR>
 * @date 2024-01-01
 */
public class TrainingProgram extends BaseEntity
{
    private static final long serialVersionUID = 1L;

    /** 方案ID */
    private Long programId;

    /** 方案名称 */
    @Excel(name = "方案名称")
    private String programName;

    /** 方案简介 */
    @Excel(name = "方案简介")
    private String programIntro;

    /** 培训时间(年) */
    @Excel(name = "培训时间(年)")
    private Integer trainingTime;

    /** 预计培养人数(人) */
    @Excel(name = "预计培养人数(人)")
    private Integer traineeCount;

    /** 培养目标 */
    @Excel(name = "培养目标")
    private String trainingGoal;

    /** 培养规格 */
    @Excel(name = "培养规格")
    private String trainingSpec;

    /** 修订次数 */
    @Excel(name = "修订次数")
    private Integer revisionCount;

    public void setProgramId(Long programId) 
    {
        this.programId = programId;
    }

    public Long getProgramId() 
    {
        return programId;
    }

    public void setProgramName(String programName) 
    {
        this.programName = programName;
    }

    public String getProgramName() 
    {
        return programName;
    }

    public void setProgramIntro(String programIntro) 
    {
        this.programIntro = programIntro;
    }

    public String getProgramIntro() 
    {
        return programIntro;
    }

    public void setTrainingTime(Integer trainingTime) 
    {
        this.trainingTime = trainingTime;
    }

    public Integer getTrainingTime() 
    {
        return trainingTime;
    }

    public void setTraineeCount(Integer traineeCount) 
    {
        this.traineeCount = traineeCount;
    }

    public Integer getTraineeCount() 
    {
        return traineeCount;
    }

    public void setTrainingGoal(String trainingGoal) 
    {
        this.trainingGoal = trainingGoal;
    }

    public String getTrainingGoal() 
    {
        return trainingGoal;
    }

    public void setTrainingSpec(String trainingSpec) 
    {
        this.trainingSpec = trainingSpec;
    }

    public String getTrainingSpec() 
    {
        return trainingSpec;
    }

    public void setRevisionCount(Integer revisionCount) 
    {
        this.revisionCount = revisionCount;
    }

    public Integer getRevisionCount() 
    {
        return revisionCount;
    }

    @Override
    public String toString() {
        return "TrainingProgram{" +
                "programId=" + programId +
                ", programName='" + programName + '\'' +
                ", programIntro='" + programIntro + '\'' +
                ", trainingTime=" + trainingTime +
                ", traineeCount=" + traineeCount +
                ", trainingGoal='" + trainingGoal + '\'' +
                ", trainingSpec='" + trainingSpec + '\'' +
                ", revisionCount=" + revisionCount +
                '}';
    }
} 