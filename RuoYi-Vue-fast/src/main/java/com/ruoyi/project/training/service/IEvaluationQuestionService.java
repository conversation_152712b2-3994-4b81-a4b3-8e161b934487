package com.ruoyi.project.training.service;

import java.util.List;
import com.ruoyi.project.training.domain.EvaluationQuestion;

/**
 * 评价模型题目Service接口
 * 
 * <AUTHOR>
 * @date 2024-01-01
 */
public interface IEvaluationQuestionService 
{
    /**
     * 查询评价模型题目
     * 
     * @param questionId 评价模型题目主键
     * @return 评价模型题目
     */
    public EvaluationQuestion selectEvaluationQuestionByQuestionId(Long questionId);

    /**
     * 查询评价模型题目列表
     * 
     * @param evaluationQuestion 评价模型题目
     * @return 评价模型题目集合
     */
    public List<EvaluationQuestion> selectEvaluationQuestionList(EvaluationQuestion evaluationQuestion);

    /**
     * 新增评价模型题目
     * 
     * @param evaluationQuestion 评价模型题目
     * @return 结果
     */
    public int insertEvaluationQuestion(EvaluationQuestion evaluationQuestion);

    /**
     * 修改评价模型题目
     * 
     * @param evaluationQuestion 评价模型题目
     * @return 结果
     */
    public int updateEvaluationQuestion(EvaluationQuestion evaluationQuestion);

    /**
     * 批量删除评价模型题目
     * 
     * @param questionIds 需要删除的评价模型题目主键集合
     * @return 结果
     */
    public int deleteEvaluationQuestionByQuestionIds(Long[] questionIds);

    /**
     * 删除评价模型题目信息
     * 
     * @param questionId 评价模型题目主键
     * @return 结果
     */
    public int deleteEvaluationQuestionByQuestionId(Long questionId);
} 