package com.ruoyi.project.training.domain;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import javax.validation.constraints.Size;
import com.ruoyi.framework.aspectj.lang.annotation.Excel;
import com.ruoyi.framework.web.domain.BaseEntity;

/**
 * 评价模型题目对象 evaluation_question
 * 
 * <AUTHOR>
 * @date 2024-01-01
 */
public class EvaluationQuestion extends BaseEntity
{
    private static final long serialVersionUID = 1L;

    /** 题目ID */
    private Long questionId;

    /** 题库ID */
    @Excel(name = "题库ID")
    @NotNull(message = "题库ID不能为空")
    private Long bankId;

    /** 题干 */
    @Excel(name = "题干")
    @NotBlank(message = "题干不能为空")
    @Size(min = 0, max = 500, message = "题干长度不能超过500个字符")
    private String questionStem;

    /** 题目类型（0单选题 1多选题） */
    @Excel(name = "题目类型", readConverterExp = "0=单选题,1=多选题")
    @NotBlank(message = "题目类型不能为空")
    private String questionType;

    /** 选项（JSON格式） */
    @Excel(name = "选项")
    @NotBlank(message = "选项不能为空")
    private String options;

    /** 状态（0正常 1停用） */
    @Excel(name = "状态", readConverterExp = "0=正常,1=停用")
    private String status;

    /** 岗位ID */
    private Long postId;

    /** 职业能力ID */
    private Long abilityId;

    /** 题库名称 */
    private String bankName;

    /** 岗位名称 */
    private String postName;

    /** 职业能力名称 */
    private String abilityName;

    public void setQuestionId(Long questionId) 
    {
        this.questionId = questionId;
    }

    public Long getQuestionId() 
    {
        return questionId;
    }

    public void setBankId(Long bankId) 
    {
        this.bankId = bankId;
    }

    public Long getBankId() 
    {
        return bankId;
    }

    public void setQuestionStem(String questionStem) 
    {
        this.questionStem = questionStem;
    }

    public String getQuestionStem() 
    {
        return questionStem;
    }

    public void setQuestionType(String questionType) 
    {
        this.questionType = questionType;
    }

    public String getQuestionType() 
    {
        return questionType;
    }

    public void setOptions(String options) 
    {
        this.options = options;
    }

    public String getOptions() 
    {
        return options;
    }

    public void setStatus(String status) 
    {
        this.status = status;
    }

    public String getStatus() 
    {
        return status;
    }

    public void setPostId(Long postId) 
    {
        this.postId = postId;
    }

    public Long getPostId() 
    {
        return postId;
    }

    public void setAbilityId(Long abilityId) 
    {
        this.abilityId = abilityId;
    }

    public Long getAbilityId() 
    {
        return abilityId;
    }

    public void setBankName(String bankName) 
    {
        this.bankName = bankName;
    }

    public String getBankName() 
    {
        return bankName;
    }

    public void setPostName(String postName) 
    {
        this.postName = postName;
    }

    public String getPostName() 
    {
        return postName;
    }

    public void setAbilityName(String abilityName) 
    {
        this.abilityName = abilityName;
    }

    public String getAbilityName() 
    {
        return abilityName;
    }
} 