package com.ruoyi.project.training.controller;

import java.util.List;
import javax.servlet.http.HttpServletResponse;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.multipart.MultipartFile;
import com.ruoyi.framework.aspectj.lang.annotation.Log;
import com.ruoyi.framework.aspectj.lang.enums.BusinessType;
import com.ruoyi.project.training.domain.ProfessionalAbility;
import com.ruoyi.project.training.service.IProfessionalAbilityService;
import com.ruoyi.framework.web.controller.BaseController;
import com.ruoyi.framework.web.domain.AjaxResult;
import com.ruoyi.common.utils.poi.ExcelUtil;
import com.ruoyi.framework.web.page.TableDataInfo;

/**
 * 职业能力Controller
 * 
 * <AUTHOR>
 * @date 2024-01-01
 */
@RestController
@RequestMapping("/training/ability")
public class ProfessionalAbilityController extends BaseController
{
    @Autowired
    private IProfessionalAbilityService professionalAbilityService;

    /**
     * 查询职业能力列表
     */
    @PreAuthorize("@ss.hasPermi('training:ability:list')")
    @GetMapping("/list")
    public TableDataInfo list(ProfessionalAbility professionalAbility)
    {
        startPage();
        List<ProfessionalAbility> list = professionalAbilityService.selectProfessionalAbilityList(professionalAbility);
        return getDataTable(list);
    }

    /**
     * 导出职业能力列表
     */
    @PreAuthorize("@ss.hasPermi('training:ability:list')")
    @Log(title = "职业能力", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, ProfessionalAbility professionalAbility)
    {
        List<ProfessionalAbility> list = professionalAbilityService.selectProfessionalAbilityList(professionalAbility);
        ExcelUtil<ProfessionalAbility> util = new ExcelUtil<ProfessionalAbility>(ProfessionalAbility.class);
        util.exportExcel(response, list, "职业能力数据");
    }

    /**
     * 获取职业能力详细信息
     */
    @PreAuthorize("@ss.hasPermi('training:ability:list')")
    @GetMapping(value = "/{abilityId}")
    public AjaxResult getInfo(@PathVariable("abilityId") Long abilityId)
    {
        return success(professionalAbilityService.selectProfessionalAbilityByAbilityId(abilityId));
    }

    /**
     * 新增职业能力
     */
    @PreAuthorize("@ss.hasPermi('training:ability:list')")
    @Log(title = "职业能力", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody ProfessionalAbility professionalAbility)
    {
        return toAjax(professionalAbilityService.insertProfessionalAbility(professionalAbility));
    }

    /**
     * 修改职业能力
     */
    @PreAuthorize("@ss.hasPermi('training:ability:list')")
    @Log(title = "职业能力", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody ProfessionalAbility professionalAbility)
    {
        return toAjax(professionalAbilityService.updateProfessionalAbility(professionalAbility));
    }

    /**
     * 删除职业能力
     */
    @PreAuthorize("@ss.hasPermi('training:ability:list')")
    @Log(title = "职业能力", businessType = BusinessType.DELETE)
	@PostMapping("/remove")
    public AjaxResult remove(@RequestBody String abilityIds)
    {
        String[] ids = abilityIds.split(",");
        Long[] abilityIdArray = new Long[ids.length];
        for (int i = 0; i < ids.length; i++) {
            abilityIdArray[i] = Long.parseLong(ids[i]);
        }
        return toAjax(professionalAbilityService.deleteProfessionalAbilityByAbilityIds(abilityIdArray));
    }

    /**
     * 导入职业能力数据
     */
    @Log(title = "职业能力", businessType = BusinessType.IMPORT)
    @PreAuthorize("@ss.hasPermi('training:ability:import')")
    @PostMapping("/importData")
    public AjaxResult importData(MultipartFile file,
                                @RequestParam(value = "updateSupport", defaultValue = "false") boolean updateSupport,
                                @RequestParam(value = "overwrite", defaultValue = "false") boolean overwrite) throws Exception
    {
        ExcelUtil<ProfessionalAbility> util = new ExcelUtil<ProfessionalAbility>(ProfessionalAbility.class);
        List<ProfessionalAbility> abilityList = util.importExcel(file.getInputStream());
        String operName = getUsername();
        String message = professionalAbilityService.importAbility(abilityList, updateSupport, overwrite, operName);
        return success(message);
    }

    /**
     * 下载职业能力导入模板
     */
    @PostMapping("/importTemplate")
    public void importTemplate(HttpServletResponse response)
    {
        ExcelUtil<ProfessionalAbility> util = new ExcelUtil<ProfessionalAbility>(ProfessionalAbility.class);
        util.importTemplateExcel(response, "职业能力数据");
    }
}