package com.ruoyi.project.training.service.impl;

import java.util.List;
import com.ruoyi.common.utils.DateUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import com.ruoyi.project.training.mapper.EvaluationQuestionBankMapper;
import com.ruoyi.project.training.mapper.EvaluationQuestionMapper;
import com.ruoyi.project.training.domain.EvaluationQuestionBank;
import com.ruoyi.project.training.service.IEvaluationQuestionBankService;
import com.ruoyi.common.utils.SecurityUtils;

/**
 * 评价模型题库Service业务层处理
 * 
 * <AUTHOR>
 * @date 2024-01-01
 */
@Service
public class EvaluationQuestionBankServiceImpl implements IEvaluationQuestionBankService
{
    @Autowired
    private EvaluationQuestionBankMapper evaluationQuestionBankMapper;
    
    @Autowired
    private EvaluationQuestionMapper evaluationQuestionMapper;

    /**
     * 查询评价模型题库
     * 
     * @param bankId 评价模型题库主键
     * @return 评价模型题库
     */
    @Override
    public EvaluationQuestionBank selectEvaluationQuestionBankByBankId(Long bankId)
    {
        return evaluationQuestionBankMapper.selectEvaluationQuestionBankByBankId(bankId);
    }

    /**
     * 查询评价模型题库列表
     * 
     * @param evaluationQuestionBank 评价模型题库
     * @return 评价模型题库
     */
    @Override
    public List<EvaluationQuestionBank> selectEvaluationQuestionBankList(EvaluationQuestionBank evaluationQuestionBank)
    {
        return evaluationQuestionBankMapper.selectEvaluationQuestionBankList(evaluationQuestionBank);
    }

    /**
     * 新增评价模型题库
     * 
     * @param evaluationQuestionBank 评价模型题库
     * @return 结果
     */
    @Override
    public int insertEvaluationQuestionBank(EvaluationQuestionBank evaluationQuestionBank)
    {
        evaluationQuestionBank.setCreateBy(SecurityUtils.getUsername());
        evaluationQuestionBank.setCreateTime(DateUtils.getNowDate());
        return evaluationQuestionBankMapper.insertEvaluationQuestionBank(evaluationQuestionBank);
    }

    /**
     * 修改评价模型题库
     * 
     * @param evaluationQuestionBank 评价模型题库
     * @return 结果
     */
    @Override
    public int updateEvaluationQuestionBank(EvaluationQuestionBank evaluationQuestionBank)
    {
        evaluationQuestionBank.setUpdateBy(SecurityUtils.getUsername());
        evaluationQuestionBank.setUpdateTime(DateUtils.getNowDate());
        return evaluationQuestionBankMapper.updateEvaluationQuestionBank(evaluationQuestionBank);
    }

    /**
     * 批量删除评价模型题库
     * 
     * @param bankIds 需要删除的评价模型题库主键
     * @return 结果
     */
    @Override
    public int deleteEvaluationQuestionBankByBankIds(Long[] bankIds)
    {
        return evaluationQuestionBankMapper.deleteEvaluationQuestionBankByBankIds(bankIds);
    }

    /**
     * 删除评价模型题库信息
     * 
     * @param bankId 评价模型题库主键
     * @return 结果
     */
    @Override
    public int deleteEvaluationQuestionBankByBankId(Long bankId)
    {
        // 先删除题库下的所有题目
        evaluationQuestionMapper.deleteEvaluationQuestionByBankId(bankId);
        // 再删除题库
        return evaluationQuestionBankMapper.deleteEvaluationQuestionBankByBankId(bankId);
    }
} 