package com.ruoyi.project.training.controller;

import java.util.List;
import java.util.ArrayList;
import java.util.Arrays;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;
import com.ruoyi.framework.aspectj.lang.annotation.Log;
import com.ruoyi.framework.aspectj.lang.enums.BusinessType;
import com.ruoyi.project.training.domain.PostModel;
import com.ruoyi.project.training.service.IPostModelService;
import com.ruoyi.framework.web.controller.BaseController;
import com.ruoyi.framework.web.domain.AjaxResult;
import com.ruoyi.framework.web.page.TableDataInfo;

/**
 * 岗位模型Controller
 * 
 * <AUTHOR>
 * @date 2024-01-01
 */
@RestController
@RequestMapping("/training/post")
public class PostModelController extends BaseController
{
    @Autowired
    private IPostModelService postModelService;

    /**
     * 查询岗位模型列表
     */
    @PreAuthorize("@ss.hasPermi('training:post:list')")
    @GetMapping("/list")
    public TableDataInfo list(PostModel postModel)
    {
        startPage();
        List<PostModel> list = postModelService.selectPostModelList(postModel);
        return getDataTable(list);
    }

    /**
     * 获取岗位模型详细信息
     */
    @PreAuthorize("@ss.hasPermi('training:post:list')")
    @GetMapping(value = "/{postId}")
    public AjaxResult getInfo(@PathVariable("postId") Long postId)
    {
        return success(postModelService.selectPostModelByPostId(postId));
    }

    /**
     * 新增岗位模型
     */
    @PreAuthorize("@ss.hasPermi('training:post:list')")
    @Log(title = "岗位模型", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody PostModel postModel)
    {
        return toAjax(postModelService.insertPostModel(postModel));
    }

    /**
     * 修改岗位模型
     */
    @PreAuthorize("@ss.hasPermi('training:post:list')")
    @Log(title = "岗位模型", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody PostModel postModel)
    {
        return toAjax(postModelService.updatePostModel(postModel));
    }

    /**
     * 删除岗位模型
     */
    @PreAuthorize("@ss.hasPermi('training:post:list')")
    @Log(title = "岗位模型", businessType = BusinessType.DELETE)
    @PostMapping("/remove")
    public AjaxResult remove(@RequestParam String postIds)
    {
        String[] ids = postIds.split(",");
        Long[] postIdArray = new Long[ids.length];
        for (int i = 0; i < ids.length; i++) {
            postIdArray[i] = Long.parseLong(ids[i]);
        }
        return toAjax(postModelService.deletePostModelByPostIds(postIdArray));
    }

    /**
     * 复制岗位模型
     */
    @PreAuthorize("@ss.hasPermi('training:post:list')")
    @Log(title = "岗位模型", businessType = BusinessType.INSERT)
    @PostMapping("/copy")
    public AjaxResult copy(@RequestBody PostModel postModel)
    {
        return toAjax(postModelService.copyPostModel(postModel));
    }
}
