package com.ruoyi.project.training.controller;

import java.util.List;
import javax.servlet.http.HttpServletResponse;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import com.ruoyi.framework.aspectj.lang.annotation.Log;
import com.ruoyi.framework.aspectj.lang.enums.BusinessType;
import com.ruoyi.project.training.domain.EvaluationQuestionBank;
import com.ruoyi.project.training.service.IEvaluationQuestionBankService;
import com.ruoyi.framework.web.controller.BaseController;
import com.ruoyi.framework.web.domain.AjaxResult;
import com.ruoyi.common.utils.poi.ExcelUtil;
import com.ruoyi.framework.web.page.TableDataInfo;

/**
 * 评价模型题库Controller
 * 
 * <AUTHOR>
 * @date 2024-01-01
 */
@RestController
@RequestMapping("/training/bank")
public class EvaluationQuestionBankController extends BaseController
{
    @Autowired
    private IEvaluationQuestionBankService evaluationQuestionBankService;

    /**
     * 查询评价模型题库列表
     */
    @PreAuthorize("@ss.hasPermi('training:bank:list')")
    @GetMapping("/list")
    public AjaxResult list(EvaluationQuestionBank evaluationQuestionBank)
    {
        List<EvaluationQuestionBank> list = evaluationQuestionBankService.selectEvaluationQuestionBankList(evaluationQuestionBank);
        return success(list);
    }

    /**
     * 导出评价模型题库列表
     */
    @PreAuthorize("@ss.hasPermi('training:bank:list')")
    @Log(title = "评价模型题库", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, EvaluationQuestionBank evaluationQuestionBank)
    {
        List<EvaluationQuestionBank> list = evaluationQuestionBankService.selectEvaluationQuestionBankList(evaluationQuestionBank);
        ExcelUtil<EvaluationQuestionBank> util = new ExcelUtil<EvaluationQuestionBank>(EvaluationQuestionBank.class);
        util.exportExcel(response, list, "评价模型题库数据");
    }

    /**
     * 获取评价模型题库详细信息
     */
    @PreAuthorize("@ss.hasPermi('training:bank:list')")
    @GetMapping(value = "/{bankId}")
    public AjaxResult getInfo(@PathVariable("bankId") Long bankId)
    {
        return success(evaluationQuestionBankService.selectEvaluationQuestionBankByBankId(bankId));
    }

    /**
     * 新增评价模型题库
     */
    @PreAuthorize("@ss.hasPermi('training:bank:list')")
    @Log(title = "评价模型题库", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody EvaluationQuestionBank evaluationQuestionBank)
    {
        return toAjax(evaluationQuestionBankService.insertEvaluationQuestionBank(evaluationQuestionBank));
    }

    /**
     * 修改评价模型题库
     */
    @PreAuthorize("@ss.hasPermi('training:bank:list')")
    @Log(title = "评价模型题库", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody EvaluationQuestionBank evaluationQuestionBank)
    {
        return toAjax(evaluationQuestionBankService.updateEvaluationQuestionBank(evaluationQuestionBank));
    }

    /**
     * 删除评价模型题库
     */
    @PreAuthorize("@ss.hasPermi('training:bank:list')")
    @Log(title = "评价模型题库", businessType = BusinessType.DELETE)
	@DeleteMapping("/{bankIds}")
    public AjaxResult remove(@PathVariable Long[] bankIds)
    {
        return toAjax(evaluationQuestionBankService.deleteEvaluationQuestionBankByBankIds(bankIds));
    }
} 