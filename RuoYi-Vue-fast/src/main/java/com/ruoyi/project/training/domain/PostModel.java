package com.ruoyi.project.training.domain;

import com.ruoyi.framework.aspectj.lang.annotation.Excel;
import com.ruoyi.framework.web.domain.BaseEntity;

import java.util.List;

/**
 * 岗位模型对象 post_model
 * 
 * <AUTHOR>
 * @date 2024-01-01
 */
public class PostModel extends BaseEntity
{
    private static final long serialVersionUID = 1L;

    /** 岗位ID */
    private Long postId;

    /** 岗位名称 */
    @Excel(name = "岗位名称")
    private String postName;

    /** 岗位描述 */
    @Excel(name = "岗位描述")
    private String postDesc;

    /** 需求企业IDs（用于前端传递和显示） */
    private List<Long> companyIds;

    /** 需求企业名称（用于显示） */
    private String companyNames;

    public void setPostId(Long postId) 
    {
        this.postId = postId;
    }

    public Long getPostId() 
    {
        return postId;
    }

    public void setPostName(String postName) 
    {
        this.postName = postName;
    }

    public String getPostName() 
    {
        return postName;
    }

    public void setPostDesc(String postDesc) 
    {
        this.postDesc = postDesc;
    }

    public String getPostDesc() 
    {
        return postDesc;
    }

    public List<Long> getCompanyIds() {
        return companyIds;
    }

    public void setCompanyIds(List<Long> companyIds) {
        this.companyIds = companyIds;
    }

    public String getCompanyNames() {
        return companyNames;
    }

    public void setCompanyNames(String companyNames) {
        this.companyNames = companyNames;
    }

    @Override
    public String toString() {
        return "PostModel{" +
                "postId=" + postId +
                ", postName='" + postName + '\'' +
                ", postDesc='" + postDesc + '\'' +
                ", companyIds=" + companyIds +
                ", companyNames='" + companyNames + '\'' +
                '}';
    }
}
