package com.ruoyi.project.training.service.impl;

import java.util.List;
import java.util.ArrayList;

import com.ruoyi.common.utils.DateUtils;
import com.ruoyi.common.utils.SecurityUtils;
import com.ruoyi.common.exception.ServiceException;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import com.ruoyi.project.training.mapper.PostModelConfigMapper;
import com.ruoyi.project.training.domain.PostModelConfig;
import com.ruoyi.project.training.domain.PostAbilityConfig;
import com.ruoyi.project.training.domain.PostCourseConfig;
import com.ruoyi.project.training.service.IPostModelConfigService;

/**
 * 岗位模型配置Service业务层处理
 * 
 * <AUTHOR>
 * @date 2024-01-01
 */
@Service
public class PostModelConfigServiceImpl implements IPostModelConfigService 
{
    @Autowired
    private PostModelConfigMapper postModelConfigMapper;

    /**
     * 查询岗位模型配置
     *
     * @param postId 岗位ID
     * @return 岗位模型配置
     */
    @Override
    public PostModelConfig selectPostModelConfigByPostId(Long postId)
    {
        PostModelConfig config = postModelConfigMapper.selectPostModelConfigByPostId(postId);
        if (config != null) {
            // 查询职业能力配置
            List<PostAbilityConfig> abilityConfigs = postModelConfigMapper.selectPostAbilityConfigList(postId);
            for (PostAbilityConfig abilityConfig : abilityConfigs) {
                // 查询课程配置
                List<PostCourseConfig> courseConfigs = postModelConfigMapper.selectPostCourseConfigList(abilityConfig.getConfigId());
                abilityConfig.setCourseConfigs(courseConfigs);
            }
            config.setAbilityConfigs(abilityConfigs);
        }
        return config;
    }

    /**
     * 查询岗位模型配置列表
     *
     * @param postModelConfig 岗位模型配置
     * @return 岗位模型配置
     */
    @Override
    public List<PostModelConfig> selectPostModelConfigList(PostModelConfig postModelConfig)
    {
        return postModelConfigMapper.selectPostModelConfigList(postModelConfig);
    }

    /**
     * 新增岗位模型配置
     *
     * @param postModelConfig 岗位模型配置
     * @return 结果
     */
    @Override
    public int insertPostModelConfig(PostModelConfig postModelConfig)
    {
        postModelConfig.setCreateTime(DateUtils.getNowDate());
        postModelConfig.setCreateBy(SecurityUtils.getUsername());
        return postModelConfigMapper.insertPostModelConfig(postModelConfig);
    }

    /**
     * 修改岗位模型配置
     *
     * @param postModelConfig 岗位模型配置
     * @return 结果
     */
    @Override
    public int updatePostModelConfig(PostModelConfig postModelConfig)
    {
        postModelConfig.setUpdateTime(DateUtils.getNowDate());
        postModelConfig.setUpdateBy(SecurityUtils.getUsername());
        return postModelConfigMapper.updatePostModelConfig(postModelConfig);
    }

    /**
     * 批量删除岗位模型配置
     *
     * @param postIds 需要删除的岗位ID集合
     * @return 结果
     */
    @Override
    public int deletePostModelConfigByPostIds(Long[] postIds)
    {
        for (Long postId : postIds) {
            deletePostModelConfigByPostId(postId);
        }
        return postIds.length;
    }

    /**
     * 删除岗位模型配置信息
     *
     * @param postId 岗位ID
     * @return 结果
     */
    @Override
    public int deletePostModelConfigByPostId(Long postId)
    {
        // 删除课程配置
        postModelConfigMapper.deletePostCourseConfigByPostId(postId);
        // 删除职业能力配置
        postModelConfigMapper.deletePostAbilityConfigByPostId(postId);
        // 删除岗位模型配置
        return postModelConfigMapper.deletePostModelConfigByPostId(postId);
    }

    /**
     * 保存岗位模型配置（包含职业能力和课程配置）
     *
     * @param postModelConfig 岗位模型配置
     * @return 结果
     */
    @Override
    @Transactional
    public int savePostModelConfig(PostModelConfig postModelConfig)
    {
        // 先删除原有配置
        deletePostModelConfigByPostId(postModelConfig.getPostId());
        
        // 插入岗位模型配置
        postModelConfig.setCreateTime(DateUtils.getNowDate());
        postModelConfig.setCreateBy(SecurityUtils.getUsername());
        postModelConfigMapper.insertPostModelConfig(postModelConfig);
        
        // 插入职业能力配置
        if (postModelConfig.getAbilityConfigs() != null) {
            for (PostAbilityConfig abilityConfig : postModelConfig.getAbilityConfigs()) {
                abilityConfig.setPostId(postModelConfig.getPostId());
                abilityConfig.setCreateTime(DateUtils.getNowDate());
                abilityConfig.setCreateBy(SecurityUtils.getUsername());
                postModelConfigMapper.insertPostAbilityConfig(abilityConfig);
                
                // 插入课程配置
                if (abilityConfig.getCourseConfigs() != null) {
                    for (PostCourseConfig courseConfig : abilityConfig.getCourseConfigs()) {
                        courseConfig.setPostId(postModelConfig.getPostId());
                        courseConfig.setAbilityConfigId(abilityConfig.getConfigId());
                        courseConfig.setCreateTime(DateUtils.getNowDate());
                        courseConfig.setCreateBy(SecurityUtils.getUsername());
                        postModelConfigMapper.insertPostCourseConfig(courseConfig);
                    }
                }
            }
        }
        
        return 1;
    }

    /**
     * 导入岗位模型配置数据
     *
     * @param configList 岗位模型配置数据列表
     * @param updateSupport 是否支持更新
     * @param overwrite 是否覆盖
     * @param operName 操作用户
     * @return 结果
     */
    @Override
    public String importPostModelConfig(List<PostModelConfig> configList, Boolean updateSupport, Boolean overwrite, String operName)
    {
        if (configList == null || configList.size() == 0)
        {
            throw new ServiceException("导入数据不能为空！");
        }
        int successNum = 0;
        int failureNum = 0;
        StringBuilder successMsg = new StringBuilder();
        StringBuilder failureMsg = new StringBuilder();
        for (PostModelConfig config : configList)
        {
            try
            {
                // 验证是否存在这个岗位模型配置
                PostModelConfig existConfig = postModelConfigMapper.selectPostModelConfigByPostId(config.getPostId());
                if (existConfig == null)
                {
                    config.setCreateBy(operName);
                    config.setCreateTime(DateUtils.getNowDate());
                    this.insertPostModelConfig(config);
                    successNum++;
                    successMsg.append("<br/>" + successNum + "、岗位 " + config.getPostName() + " 导入成功");
                }
                else if (updateSupport)
                {
                    config.setUpdateBy(operName);
                    config.setUpdateTime(DateUtils.getNowDate());
                    this.updatePostModelConfig(config);
                    successNum++;
                    successMsg.append("<br/>" + successNum + "、岗位 " + config.getPostName() + " 更新成功");
                }
                else
                {
                    failureNum++;
                    failureMsg.append("<br/>" + failureNum + "、岗位 " + config.getPostName() + " 已存在");
                }
            }
            catch (Exception e)
            {
                failureNum++;
                String msg = "<br/>" + failureNum + "、岗位 " + config.getPostName() + " 导入失败：";
                failureMsg.append(msg + e.getMessage());
            }
        }
        if (failureNum > 0)
        {
            failureMsg.insert(0, "很抱歉，导入失败！共 " + failureNum + " 条数据格式不正确，错误如下：");
            throw new ServiceException(failureMsg.toString());
        }
        else
        {
            successMsg.insert(0, "恭喜您，数据已全部导入成功！共 " + successNum + " 条，数据如下：");
        }
        return successMsg.toString();
    }
} 