package com.ruoyi.project.training.mapper;

import java.util.List;
import com.ruoyi.project.training.domain.TrainingProgram;

/**
 * 人才培养方案Mapper接口
 * 
 * <AUTHOR>
 * @date 2024-01-01
 */
public interface TrainingProgramMapper 
{
    /**
     * 查询人才培养方案
     * 
     * @param programId 人才培养方案主键
     * @return 人才培养方案
     */
    public TrainingProgram selectTrainingProgramByProgramId(Long programId);

    /**
     * 查询人才培养方案列表
     * 
     * @param trainingProgram 人才培养方案
     * @return 人才培养方案集合
     */
    public List<TrainingProgram> selectTrainingProgramList(TrainingProgram trainingProgram);

    /**
     * 新增人才培养方案
     * 
     * @param trainingProgram 人才培养方案
     * @return 结果
     */
    public int insertTrainingProgram(TrainingProgram trainingProgram);

    /**
     * 修改人才培养方案
     * 
     * @param trainingProgram 人才培养方案
     * @return 结果
     */
    public int updateTrainingProgram(TrainingProgram trainingProgram);

    /**
     * 删除人才培养方案
     * 
     * @param programId 人才培养方案主键
     * @return 结果
     */
    public int deleteTrainingProgramByProgramId(Long programId);

    /**
     * 批量删除人才培养方案
     * 
     * @param programIds 需要删除的数据主键集合
     * @return 结果
     */
    public int deleteTrainingProgramByProgramIds(Long[] programIds);

    /**
     * 清空所有人才培养方案数据
     * 
     * @return 结果
     */
    public int deleteAllTrainingPrograms();
} 