package com.ruoyi.project.training.domain;

import com.ruoyi.framework.aspectj.lang.annotation.Excel;
import com.ruoyi.framework.web.domain.BaseEntity;

import java.util.List;

/**
 * 岗位职业能力配置对象 post_ability_config
 * 
 * <AUTHOR>
 * @date 2024-01-01
 */
public class PostAbilityConfig extends BaseEntity
{
    private static final long serialVersionUID = 1L;

    /** 配置ID */
    private Long configId;

    /** 岗位ID */
    @Excel(name = "岗位ID")
    private Long postId;

    /** 职业能力ID */
    @Excel(name = "职业能力ID")
    private Long abilityId;

    /** 职业能力名称 */
    @Excel(name = "职业能力名称")
    private String abilityName;

    /** 达标阈值 */
    @Excel(name = "达标阈值")
    private Integer threshold;

    /** 计算权重 */
    @Excel(name = "计算权重")
    private Integer weight;

    /** 关联课程配置列表 */
    private List<PostCourseConfig> courseConfigs;

    public void setConfigId(Long configId) 
    {
        this.configId = configId;
    }

    public Long getConfigId() 
    {
        return configId;
    }

    public void setPostId(Long postId) 
    {
        this.postId = postId;
    }

    public Long getPostId() 
    {
        return postId;
    }

    public void setAbilityId(Long abilityId) 
    {
        this.abilityId = abilityId;
    }

    public Long getAbilityId() 
    {
        return abilityId;
    }

    public void setAbilityName(String abilityName) 
    {
        this.abilityName = abilityName;
    }

    public String getAbilityName() 
    {
        return abilityName;
    }

    public void setThreshold(Integer threshold) 
    {
        this.threshold = threshold;
    }

    public Integer getThreshold() 
    {
        return threshold;
    }

    public void setWeight(Integer weight) 
    {
        this.weight = weight;
    }

    public Integer getWeight() 
    {
        return weight;
    }

    public List<PostCourseConfig> getCourseConfigs() {
        return courseConfigs;
    }

    public void setCourseConfigs(List<PostCourseConfig> courseConfigs) {
        this.courseConfigs = courseConfigs;
    }

    @Override
    public String toString() {
        return "PostAbilityConfig{" +
                "configId=" + configId +
                ", postId=" + postId +
                ", abilityId=" + abilityId +
                ", abilityName='" + abilityName + '\'' +
                ", threshold=" + threshold +
                ", weight=" + weight +
                ", courseConfigs=" + courseConfigs +
                '}';
    }
} 