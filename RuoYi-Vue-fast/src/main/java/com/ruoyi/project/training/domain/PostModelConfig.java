package com.ruoyi.project.training.domain;

import com.ruoyi.framework.aspectj.lang.annotation.Excel;
import com.ruoyi.framework.web.domain.BaseEntity;

import java.util.List;

/**
 * 岗位模型配置对象 post_model_config
 * 
 * <AUTHOR>
 * @date 2024-01-01
 */
public class PostModelConfig extends BaseEntity
{
    private static final long serialVersionUID = 1L;

    /** 配置ID */
    private Long configId;

    /** 岗位ID */
    @Excel(name = "岗位ID")
    private Long postId;

    /** 岗位名称 */
    @Excel(name = "岗位名称")
    private String postName;

    /** 职业能力配置列表 */
    private List<PostAbilityConfig> abilityConfigs;

    public void setConfigId(Long configId) 
    {
        this.configId = configId;
    }

    public Long getConfigId() 
    {
        return configId;
    }

    public void setPostId(Long postId) 
    {
        this.postId = postId;
    }

    public Long getPostId() 
    {
        return postId;
    }

    public void setPostName(String postName) 
    {
        this.postName = postName;
    }

    public String getPostName() 
    {
        return postName;
    }

    public List<PostAbilityConfig> getAbilityConfigs() {
        return abilityConfigs;
    }

    public void setAbilityConfigs(List<PostAbilityConfig> abilityConfigs) {
        this.abilityConfigs = abilityConfigs;
    }

    @Override
    public String toString() {
        return "PostModelConfig{" +
                "configId=" + configId +
                ", postId=" + postId +
                ", postName='" + postName + '\'' +
                ", abilityConfigs=" + abilityConfigs +
                '}';
    }
} 