package com.ruoyi.project.training.service.impl;

import java.util.List;
import javax.validation.Validator;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import com.ruoyi.common.exception.ServiceException;
import com.ruoyi.common.utils.StringUtils;
import com.ruoyi.common.utils.bean.BeanValidators;
import com.ruoyi.common.utils.DateUtils;
import com.ruoyi.project.training.mapper.TrainingProgramMapper;
import com.ruoyi.project.training.domain.TrainingProgram;
import com.ruoyi.project.training.domain.TrainingProgramImport;
import com.ruoyi.project.training.service.ITrainingProgramService;

/**
 * 人才培养方案Service业务层处理
 * 
 * <AUTHOR>
 * @date 2024-01-01
 */
@Service
public class TrainingProgramServiceImpl implements ITrainingProgramService
{
    private static final Logger log = LoggerFactory.getLogger(TrainingProgramServiceImpl.class);

    @Autowired
    private Validator validator;

    @Autowired
    private TrainingProgramMapper trainingProgramMapper;

    /**
     * 查询人才培养方案
     * 
     * @param programId 人才培养方案主键
     * @return 人才培养方案
     */
    @Override
    public TrainingProgram selectTrainingProgramByProgramId(Long programId)
    {
        return trainingProgramMapper.selectTrainingProgramByProgramId(programId);
    }

    /**
     * 查询人才培养方案列表
     * 
     * @param trainingProgram 人才培养方案
     * @return 人才培养方案
     */
    @Override
    public List<TrainingProgram> selectTrainingProgramList(TrainingProgram trainingProgram)
    {
        return trainingProgramMapper.selectTrainingProgramList(trainingProgram);
    }

    /**
     * 新增人才培养方案
     * 
     * @param trainingProgram 人才培养方案
     * @return 结果
     */
    @Override
    public int insertTrainingProgram(TrainingProgram trainingProgram)
    {
        trainingProgram.setCreateTime(DateUtils.getNowDate());
        return trainingProgramMapper.insertTrainingProgram(trainingProgram);
    }

    /**
     * 修改人才培养方案
     * 
     * @param trainingProgram 人才培养方案
     * @return 结果
     */
    @Override
    public int updateTrainingProgram(TrainingProgram trainingProgram)
    {
        trainingProgram.setUpdateTime(DateUtils.getNowDate());
        return trainingProgramMapper.updateTrainingProgram(trainingProgram);
    }

    /**
     * 批量删除人才培养方案
     * 
     * @param programIds 需要删除的人才培养方案主键
     * @return 结果
     */
    @Override
    public int deleteTrainingProgramByProgramIds(Long[] programIds)
    {
        return trainingProgramMapper.deleteTrainingProgramByProgramIds(programIds);
    }

    /**
     * 删除人才培养方案信息
     *
     * @param programId 人才培养方案主键
     * @return 结果
     */
    @Override
    public int deleteTrainingProgramByProgramId(Long programId)
    {
        return trainingProgramMapper.deleteTrainingProgramByProgramId(programId);
    }

    /**
     * 导入人才培养方案数据
     *
     * @param programList 人才培养方案数据列表
     * @param isUpdateSupport 是否更新支持，如果已存在，则进行更新数据
     * @param isOverwrite 是否覆盖数据，如果为true则先清空所有数据再导入
     * @param operName 操作用户
     * @return 结果
     */
    @Override
    public String importProgram(List<TrainingProgramImport> programList, Boolean isUpdateSupport, Boolean isOverwrite, String operName)
    {
        if (StringUtils.isNull(programList) || programList.size() == 0)
        {
            throw new ServiceException("导入人才培养方案数据不能为空！");
        }
        int successNum = 0;
        int failureNum = 0;
        StringBuilder successMsg = new StringBuilder();
        StringBuilder failureMsg = new StringBuilder();
        
        // 如果需要覆盖数据，先清空所有数据
        if (isOverwrite)
        {
            trainingProgramMapper.deleteAllTrainingPrograms();
        }
        
        for (TrainingProgramImport importProgram : programList)
        {
            try
            {
                // 转换为TrainingProgram对象
                TrainingProgram program = new TrainingProgram();
                program.setProgramName(importProgram.getProgramName());
                program.setProgramIntro(importProgram.getProgramIntro());
                program.setTrainingTime(importProgram.getTrainingTime());
                program.setTraineeCount(importProgram.getTraineeCount());
                program.setTrainingGoal(importProgram.getTrainingGoal());
                program.setTrainingSpec(importProgram.getTrainingSpec());
                program.setRevisionCount(0); // 新建时修订次数为0
                
                // 验证必填字段
                if (StringUtils.isEmpty(program.getProgramName())) {
                    throw new ServiceException("方案名称不能为空");
                }
                
                // 调试信息
                log.info("导入数据 - 方案名称: {}, 培训时间: {} (类型: {}), 培养人数: {} (类型: {})", 
                    program.getProgramName(), 
                    program.getTrainingTime(), 
                    program.getTrainingTime() != null ? program.getTrainingTime().getClass().getSimpleName() : "null",
                    program.getTraineeCount(),
                    program.getTraineeCount() != null ? program.getTraineeCount().getClass().getSimpleName() : "null"
                );
                
                if (program.getTrainingTime() == null) {
                    throw new ServiceException("培训时间不能为空");
                }
                if (program.getTrainingTime() < 1 || program.getTrainingTime() > 10) {
                    throw new ServiceException("培训时间必须在1-10年之间，当前值: " + program.getTrainingTime());
                }
                
                if (program.getTraineeCount() == null) {
                    throw new ServiceException("预计培养人数不能为空");
                }
                if (program.getTraineeCount() < 1 || program.getTraineeCount() > 1000) {
                    throw new ServiceException("预计培养人数必须在1-1000人之间，当前值: " + program.getTraineeCount());
                }
                
                // 确保可选字段不为null
                if (StringUtils.isEmpty(program.getProgramIntro())) {
                    program.setProgramIntro("");
                }
                if (StringUtils.isEmpty(program.getTrainingGoal())) {
                    program.setTrainingGoal("");
                }
                if (StringUtils.isEmpty(program.getTrainingSpec())) {
                    program.setTrainingSpec("");
                }
                
                // 验证数据
                BeanValidators.validateWithException(validator, program);
                program.setCreateBy(operName);
                program.setCreateTime(DateUtils.getNowDate());
                trainingProgramMapper.insertTrainingProgram(program);
                successNum++;
                successMsg.append("<br/>" + successNum + "、方案名称 " + program.getProgramName() + " 导入成功");
            }
            catch (Exception e)
            {
                failureNum++;
                String msg = "<br/>" + failureNum + "、方案名称 " + importProgram.getProgramName() + " 导入失败：";
                failureMsg.append(msg + e.getMessage());
                log.error(msg, e);
            }
        }
        if (failureNum > 0)
        {
            failureMsg.insert(0, "很抱歉，导入失败！共 " + failureNum + " 条数据格式不正确，错误如下：");
            throw new ServiceException(failureMsg.toString());
        }
        else
        {
            successMsg.insert(0, "恭喜您，数据已全部导入成功！共 " + successNum + " 条，数据如下：");
        }
        return successMsg.toString();
    }
} 