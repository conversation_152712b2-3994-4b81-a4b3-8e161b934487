package com.ruoyi.project.student.mapper;

import java.util.List;
import com.ruoyi.project.student.domain.StudentGrowthPath;

/**
 * 学生成长路径Mapper接口
 * 
 * <AUTHOR>
 * @date 2024-01-01
 */
public interface StudentGrowthPathMapper 
{
    /**
     * 查询学生成长路径
     * 
     * @param pathId 学生成长路径主键
     * @return 学生成长路径
     */
    public StudentGrowthPath selectStudentGrowthPathByPathId(Long pathId);

    /**
     * 查询学生成长路径列表
     * 
     * @param studentGrowthPath 学生成长路径
     * @return 学生成长路径集合
     */
    public List<StudentGrowthPath> selectStudentGrowthPathList(StudentGrowthPath studentGrowthPath);

    /**
     * 根据学生ID查询成长路径列表
     * 
     * @param studentId 学生ID
     * @return 学生成长路径集合
     */
    public List<StudentGrowthPath> selectStudentGrowthPathByStudentId(Long studentId);

    /**
     * 新增学生成长路径
     * 
     * @param studentGrowthPath 学生成长路径
     * @return 结果
     */
    public int insertStudentGrowthPath(StudentGrowthPath studentGrowthPath);

    /**
     * 修改学生成长路径
     * 
     * @param studentGrowthPath 学生成长路径
     * @return 结果
     */
    public int updateStudentGrowthPath(StudentGrowthPath studentGrowthPath);

    /**
     * 删除学生成长路径
     * 
     * @param pathId 学生成长路径主键
     * @return 结果
     */
    public int deleteStudentGrowthPathByPathId(Long pathId);

    /**
     * 批量删除学生成长路径
     * 
     * @param pathIds 需要删除的数据主键集合
     * @return 结果
     */
    public int deleteStudentGrowthPathByPathIds(Long[] pathIds);

    /**
     * 根据学生ID删除成长路径
     * 
     * @param studentId 学生ID
     * @return 结果
     */
    public int deleteStudentGrowthPathByStudentId(Long studentId);

    /**
     * 根据学生ID批量删除成长路径
     * 
     * @param studentIds 学生ID数组
     * @return 结果
     */
    public int deleteStudentGrowthPathByStudentIds(Long[] studentIds);
} 