package com.ruoyi.project.student.domain;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import javax.validation.constraints.Size;
import com.ruoyi.framework.aspectj.lang.annotation.Excel;
import com.ruoyi.framework.web.domain.BaseEntity;

/**
 * 学生成长路径对象 student_growth_path
 * 
 * <AUTHOR>
 * @date 2024-01-01
 */
public class StudentGrowthPath extends BaseEntity
{
    private static final long serialVersionUID = 1L;

    /** 路径ID */
    private Long pathId;

    /** 学生ID */
    @Excel(name = "学生ID")
    @NotNull(message = "学生ID不能为空")
    private Long studentId;

    /** 学期编码（如：2024-2025-1） */
    @Excel(name = "学期编码")
    @NotBlank(message = "学期编码不能为空")
    @Size(min = 0, max = 50, message = "学期编码长度不能超过50个字符")
    private String semesterCode;

    /** 学习情况描述 */
    @Excel(name = "学习情况描述")
    private String learningSituation;

    public void setPathId(Long pathId) 
    {
        this.pathId = pathId;
    }

    public Long getPathId() 
    {
        return pathId;
    }

    public void setStudentId(Long studentId) 
    {
        this.studentId = studentId;
    }

    public Long getStudentId() 
    {
        return studentId;
    }

    public void setSemesterCode(String semesterCode) 
    {
        this.semesterCode = semesterCode;
    }

    public String getSemesterCode() 
    {
        return semesterCode;
    }

    public void setLearningSituation(String learningSituation) 
    {
        this.learningSituation = learningSituation;
    }

    public String getLearningSituation() 
    {
        return learningSituation;
    }

    @Override
    public String toString() {
        return new StringBuilder()
            .append("StudentGrowthPath{")
            .append("pathId=").append(pathId)
            .append(", studentId=").append(studentId)
            .append(", semesterCode='").append(semesterCode).append('\'')
            .append(", learningSituation='").append(learningSituation).append('\'')
            .append("}")
            .toString();
    }
} 