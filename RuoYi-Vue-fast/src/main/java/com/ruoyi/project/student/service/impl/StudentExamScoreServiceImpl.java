package com.ruoyi.project.student.service.impl;

import java.util.List;
import com.ruoyi.common.utils.DateUtils;
import com.ruoyi.framework.web.controller.BaseController;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import com.ruoyi.project.student.mapper.StudentExamScoreMapper;
import com.ruoyi.project.student.domain.StudentExamScore;
import com.ruoyi.project.student.service.IStudentExamScoreService;

/**
 * 学生考试成绩Service业务层处理
 * 
 * <AUTHOR>
 * @date 2024-01-01
 */
@Service
public class StudentExamScoreServiceImpl extends BaseController implements IStudentExamScoreService 
{
    @Autowired
    private StudentExamScoreMapper studentExamScoreMapper;

    /**
     * 查询学生考试成绩
     * 
     * @param scoreId 学生考试成绩主键
     * @return 学生考试成绩
     */
    @Override
    public StudentExamScore selectStudentExamScoreByScoreId(Long scoreId)
    {
        return studentExamScoreMapper.selectStudentExamScoreByScoreId(scoreId);
    }

    /**
     * 查询学生考试成绩列表
     * 
     * @param studentExamScore 学生考试成绩
     * @return 学生考试成绩
     */
    @Override
    public List<StudentExamScore> selectStudentExamScoreList(StudentExamScore studentExamScore)
    {
        return studentExamScoreMapper.selectStudentExamScoreList(studentExamScore);
    }

    /**
     * 根据学生ID查询考试成绩列表
     * 
     * @param studentId 学生ID
     * @return 学生考试成绩集合
     */
    @Override
    public List<StudentExamScore> selectStudentExamScoreListByStudentId(Long studentId)
    {
        return studentExamScoreMapper.selectStudentExamScoreListByStudentId(studentId);
    }

    /**
     * 新增学生考试成绩
     * 
     * @param studentExamScore 学生考试成绩
     * @return 结果
     */
    @Override
    public int insertStudentExamScore(StudentExamScore studentExamScore)
    {
        studentExamScore.setCreateBy(getUsername());
        studentExamScore.setCreateTime(DateUtils.getNowDate());
        return studentExamScoreMapper.insertStudentExamScore(studentExamScore);
    }

    /**
     * 修改学生考试成绩
     * 
     * @param studentExamScore 学生考试成绩
     * @return 结果
     */
    @Override
    public int updateStudentExamScore(StudentExamScore studentExamScore)
    {
        studentExamScore.setUpdateBy(getUsername());
        studentExamScore.setUpdateTime(DateUtils.getNowDate());
        return studentExamScoreMapper.updateStudentExamScore(studentExamScore);
    }

    /**
     * 批量删除学生考试成绩
     * 
     * @param scoreIds 需要删除的学生考试成绩主键
     * @return 结果
     */
    @Override
    public int deleteStudentExamScoreByScoreIds(Long[] scoreIds)
    {
        return studentExamScoreMapper.deleteStudentExamScoreByScoreIds(scoreIds);
    }

    /**
     * 删除学生考试成绩信息
     * 
     * @param scoreId 学生考试成绩主键
     * @return 结果
     */
    @Override
    public int deleteStudentExamScoreByScoreId(Long scoreId)
    {
        return studentExamScoreMapper.deleteStudentExamScoreByScoreId(scoreId);
    }

    /**
     * 根据学生ID删除考试成绩
     * 
     * @param studentId 学生ID
     * @return 结果
     */
    @Override
    public int deleteStudentExamScoreByStudentId(Long studentId)
    {
        return studentExamScoreMapper.deleteStudentExamScoreByStudentId(studentId);
    }

    /**
     * 根据学生ID批量删除考试成绩
     * 
     * @param studentIds 学生ID数组
     * @return 结果
     */
    @Override
    public int deleteStudentExamScoreByStudentIds(Long[] studentIds)
    {
        return studentExamScoreMapper.deleteStudentExamScoreByStudentIds(studentIds);
    }
} 