package com.ruoyi.project.student.controller;

import java.util.List;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.validation.annotation.Validated;
import com.ruoyi.framework.aspectj.lang.annotation.Log;
import com.ruoyi.framework.aspectj.lang.enums.BusinessType;
import com.ruoyi.project.student.domain.StudentGrowthPath;
import com.ruoyi.project.student.service.IStudentGrowthPathService;
import com.ruoyi.framework.web.controller.BaseController;
import com.ruoyi.framework.web.domain.AjaxResult;
import com.ruoyi.framework.web.page.TableDataInfo;

/**
 * 学生成长路径Controller
 * 
 * <AUTHOR>
 * @date 2024-01-01
 */
@RestController
@RequestMapping("/student/growthPath")
public class StudentGrowthPathController extends BaseController
{
    @Autowired
    private IStudentGrowthPathService studentGrowthPathService;

    /**
     * 查询学生成长路径列表
     */
    @PreAuthorize("@ss.hasPermi('student:info:list')")
    @GetMapping("/list")
    public AjaxResult list(StudentGrowthPath studentGrowthPath)
    {
        List<StudentGrowthPath> list = studentGrowthPathService.selectStudentGrowthPathList(studentGrowthPath);
        return success(list);
    }

    /**
     * 根据学生ID查询成长路径列表
     */
    @PreAuthorize("@ss.hasPermi('student:info:list')")
    @GetMapping("/listByStudentId/{studentId}")
    public TableDataInfo listByStudentId(@PathVariable("studentId") Long studentId, StudentGrowthPath studentGrowthPath)
    {
        studentGrowthPath.setStudentId(studentId);
        startPage();
        List<StudentGrowthPath> list = studentGrowthPathService.selectStudentGrowthPathList(studentGrowthPath);
        return getDataTable(list);
    }

    /**
     * 获取学生成长路径详细信息
     */
    @PreAuthorize("@ss.hasPermi('student:info:list')")
    @GetMapping(value = "/{pathId}")
    public AjaxResult getInfo(@PathVariable("pathId") Long pathId)
    {
        return success(studentGrowthPathService.selectStudentGrowthPathByPathId(pathId));
    }

    /**
     * 新增学生成长路径
     */
    @PreAuthorize("@ss.hasPermi('student:info:list')")
    @Log(title = "学生成长路径", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@Validated @RequestBody StudentGrowthPath studentGrowthPath)
    {
        return toAjax(studentGrowthPathService.insertStudentGrowthPath(studentGrowthPath));
    }

    /**
     * 修改学生成长路径
     */
    @PreAuthorize("@ss.hasPermi('student:info:list')")
    @Log(title = "学生成长路径", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@Validated @RequestBody StudentGrowthPath studentGrowthPath)
    {
        return toAjax(studentGrowthPathService.updateStudentGrowthPath(studentGrowthPath));
    }

    /**
     * 删除学生成长路径
     */
    @PreAuthorize("@ss.hasPermi('student:info:list')")
    @Log(title = "学生成长路径", businessType = BusinessType.DELETE)
    @DeleteMapping("/{pathIds}")
    public AjaxResult remove(@PathVariable Long[] pathIds)
    {
        return toAjax(studentGrowthPathService.deleteStudentGrowthPathByPathIds(pathIds));
    }
} 