package com.ruoyi.project.student.service;

import java.util.List;
import com.ruoyi.project.student.domain.StudentInfo;

/**
 * 学生信息Service接口
 * 
 * <AUTHOR>
 * @date 2024-01-01
 */
public interface IStudentInfoService 
{
    /**
     * 查询学生信息
     * 
     * @param studentId 学生信息主键
     * @return 学生信息
     */
    public StudentInfo selectStudentInfoByStudentId(Long studentId);

    /**
     * 查询学生信息列表
     * 
     * @param studentInfo 学生信息
     * @return 学生信息集合
     */
    public List<StudentInfo> selectStudentInfoList(StudentInfo studentInfo);

    /**
     * 新增学生信息
     * 
     * @param studentInfo 学生信息
     * @return 结果
     */
    public int insertStudentInfo(StudentInfo studentInfo);

    /**
     * 修改学生信息
     * 
     * @param studentInfo 学生信息
     * @return 结果
     */
    public int updateStudentInfo(StudentInfo studentInfo);

    /**
     * 批量删除学生信息
     * 
     * @param studentIds 需要删除的学生信息主键集合
     * @return 结果
     */
    public int deleteStudentInfoByStudentIds(Long[] studentIds);

    /**
     * 删除学生信息信息
     * 
     * @param studentId 学生信息主键
     * @return 结果
     */
    public int deleteStudentInfoByStudentId(Long studentId);

    /**
     * 校验学号是否唯一
     * 
     * @param studentInfo 学生信息
     * @return 结果
     */
    public boolean checkStudentNumberUnique(StudentInfo studentInfo);

    /**
     * 导入学生信息数据
     * 
     * @param studentInfoList 学生信息数据列表
     * @param isUpdateSupport 是否更新支持，如果已存在，则进行更新数据
     * @param operName 操作用户
     * @return 结果
     */
    public String importStudentInfo(List<StudentInfo> studentInfoList, Boolean isUpdateSupport, String operName);
} 