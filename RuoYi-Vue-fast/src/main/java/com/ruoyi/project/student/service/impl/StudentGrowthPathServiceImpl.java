package com.ruoyi.project.student.service.impl;

import java.util.List;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import com.ruoyi.common.utils.DateUtils;
import com.ruoyi.framework.web.controller.BaseController;
import com.ruoyi.project.student.mapper.StudentGrowthPathMapper;
import com.ruoyi.project.student.domain.StudentGrowthPath;
import com.ruoyi.project.student.service.IStudentGrowthPathService;

/**
 * 学生成长路径Service业务层处理
 * 
 * <AUTHOR>
 * @date 2024-01-01
 */
@Service
public class StudentGrowthPathServiceImpl extends BaseController implements IStudentGrowthPathService 
{
    @Autowired
    private StudentGrowthPathMapper studentGrowthPathMapper;

    /**
     * 查询学生成长路径
     * 
     * @param pathId 学生成长路径主键
     * @return 学生成长路径
     */
    @Override
    public StudentGrowthPath selectStudentGrowthPathByPathId(Long pathId)
    {
        return studentGrowthPathMapper.selectStudentGrowthPathByPathId(pathId);
    }

    /**
     * 查询学生成长路径列表
     * 
     * @param studentGrowthPath 学生成长路径
     * @return 学生成长路径
     */
    @Override
    public List<StudentGrowthPath> selectStudentGrowthPathList(StudentGrowthPath studentGrowthPath)
    {
        return studentGrowthPathMapper.selectStudentGrowthPathList(studentGrowthPath);
    }

    /**
     * 根据学生ID查询成长路径列表
     * 
     * @param studentId 学生ID
     * @return 学生成长路径集合
     */
    @Override
    public List<StudentGrowthPath> selectStudentGrowthPathByStudentId(Long studentId)
    {
        return studentGrowthPathMapper.selectStudentGrowthPathByStudentId(studentId);
    }

    /**
     * 新增学生成长路径
     * 
     * @param studentGrowthPath 学生成长路径
     * @return 结果
     */
    @Override
    public int insertStudentGrowthPath(StudentGrowthPath studentGrowthPath)
    {
        // 设置创建人和创建时间
        studentGrowthPath.setCreateBy(getUsername());
        studentGrowthPath.setCreateTime(DateUtils.getNowDate());
        return studentGrowthPathMapper.insertStudentGrowthPath(studentGrowthPath);
    }

    /**
     * 修改学生成长路径
     * 
     * @param studentGrowthPath 学生成长路径
     * @return 结果
     */
    @Override
    public int updateStudentGrowthPath(StudentGrowthPath studentGrowthPath)
    {
        // 设置更新人和更新时间
        studentGrowthPath.setUpdateBy(getUsername());
        studentGrowthPath.setUpdateTime(DateUtils.getNowDate());
        return studentGrowthPathMapper.updateStudentGrowthPath(studentGrowthPath);
    }

    /**
     * 批量删除学生成长路径
     * 
     * @param pathIds 需要删除的学生成长路径主键
     * @return 结果
     */
    @Override
    public int deleteStudentGrowthPathByPathIds(Long[] pathIds)
    {
        return studentGrowthPathMapper.deleteStudentGrowthPathByPathIds(pathIds);
    }

    /**
     * 删除学生成长路径信息
     * 
     * @param pathId 学生成长路径主键
     * @return 结果
     */
    @Override
    public int deleteStudentGrowthPathByPathId(Long pathId)
    {
        return studentGrowthPathMapper.deleteStudentGrowthPathByPathId(pathId);
    }

    /**
     * 根据学生ID删除成长路径
     * 
     * @param studentId 学生ID
     * @return 结果
     */
    @Override
    public int deleteStudentGrowthPathByStudentId(Long studentId)
    {
        return studentGrowthPathMapper.deleteStudentGrowthPathByStudentId(studentId);
    }

    /**
     * 根据学生ID批量删除成长路径
     * 
     * @param studentIds 学生ID数组
     * @return 结果
     */
    @Override
    public int deleteStudentGrowthPathByStudentIds(Long[] studentIds)
    {
        return studentGrowthPathMapper.deleteStudentGrowthPathByStudentIds(studentIds);
    }
} 