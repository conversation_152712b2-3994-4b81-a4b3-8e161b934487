package com.ruoyi.project.student.controller;

import java.util.List;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.validation.annotation.Validated;
import com.ruoyi.framework.aspectj.lang.annotation.Log;
import com.ruoyi.framework.aspectj.lang.enums.BusinessType;
import com.ruoyi.project.student.domain.StudentContract;
import com.ruoyi.project.student.service.IStudentContractService;
import com.ruoyi.framework.web.controller.BaseController;
import com.ruoyi.framework.web.domain.AjaxResult;
import com.ruoyi.framework.web.page.TableDataInfo;

/**
 * 学生合同信息Controller
 * 
 * <AUTHOR>
 * @date 2024-01-01
 */
@RestController
@RequestMapping("/student/contract")
public class StudentContractController extends BaseController
{
    @Autowired
    private IStudentContractService studentContractService;

    /**
     * 查询学生合同信息列表
     */
    @PreAuthorize("@ss.hasPermi('student:contract:list')")
    @GetMapping("/list")
    public TableDataInfo list(StudentContract studentContract)
    {
        startPage();
        List<StudentContract> list = studentContractService.selectStudentContractList(studentContract);
        return getDataTable(list);
    }



    /**
     * 获取学生合同信息详细信息
     */
    @PreAuthorize("@ss.hasPermi('student:contract:list')")
    @GetMapping(value = "/{contractId}")
    public AjaxResult getInfo(@PathVariable("contractId") Long contractId)
    {
        return success(studentContractService.selectStudentContractByContractId(contractId));
    }

    /**
     * 新增学生合同信息
     */
    @PreAuthorize("@ss.hasPermi('student:contract:list')")
    @Log(title = "学生合同信息", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@Validated @RequestBody StudentContract studentContract)
    {
        if (!studentContractService.checkContractNumberUnique(studentContract))
        {
            return error("新增学生合同'" + studentContract.getContractNumber() + "'失败，合同编号已存在");
        }
        return toAjax(studentContractService.insertStudentContract(studentContract));
    }

    /**
     * 修改学生合同信息
     */
    @PreAuthorize("@ss.hasPermi('student:contract:list')")
    @Log(title = "学生合同信息", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@Validated @RequestBody StudentContract studentContract)
    {
        if (!studentContractService.checkContractNumberUnique(studentContract))
        {
            return error("修改学生合同'" + studentContract.getContractNumber() + "'失败，合同编号已存在");
        }
        return toAjax(studentContractService.updateStudentContract(studentContract));
    }

    /**
     * 删除学生合同信息
     */
    @PreAuthorize("@ss.hasPermi('student:contract:list')")
    @Log(title = "学生合同信息", businessType = BusinessType.DELETE)
    @DeleteMapping("/{contractIds}")
    public AjaxResult remove(@PathVariable Long[] contractIds)
    {
        return toAjax(studentContractService.deleteStudentContractByContractIds(contractIds));
    }
} 