package com.ruoyi.project.student.domain;

import java.math.BigDecimal;
import java.util.Date;
import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import javax.validation.constraints.Size;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.ruoyi.framework.aspectj.lang.annotation.Excel;
import com.ruoyi.framework.web.domain.BaseEntity;

/**
 * 学生合同信息对象 student_contract
 * 
 * <AUTHOR>
 * @date 2024-01-01
 */
public class StudentContract extends BaseEntity
{
    private static final long serialVersionUID = 1L;

    /** 合同ID */
    private Long contractId;

    /** 合同编号 */
    @Excel(name = "合同编号")
    @NotBlank(message = "合同编号不能为空")
    @Size(min = 0, max = 50, message = "合同编号长度不能超过50个字符")
    private String contractNumber;

    /** 合同名称 */
    @Excel(name = "合同名称")
    @NotBlank(message = "合同名称不能为空")
    @Size(min = 0, max = 100, message = "合同名称长度不能超过100个字符")
    private String contractName;

    /** 签约日期 */
    @JsonFormat(pattern = "yyyy-MM-dd")
    @Excel(name = "签约日期", width = 30, dateFormat = "yyyy-MM-dd")
    @NotNull(message = "签约日期不能为空")
    private Date signDate;

    /** 学生ID */
    @Excel(name = "学生ID")
    @NotNull(message = "学生ID不能为空")
    private Long studentId;

    /** 企业名称 */
    @Excel(name = "企业名称")
    @NotBlank(message = "企业名称不能为空")
    @Size(min = 0, max = 200, message = "企业名称长度不能超过200个字符")
    private String companyName;

    /** 岗位名称 */
    @Excel(name = "岗位名称")
    @Size(min = 0, max = 200, message = "岗位名称长度不能超过200个字符")
    private String postName;

    /** 薪酬 */
    @Excel(name = "薪酬")
    private BigDecimal salary;

    // 关联查询的学生信息（用于列表显示）
    /** 学生姓名 */
    private String studentName;

    /** 学号 */
    private String studentNumber;

    /** 专业 */
    private String major;

    /** 校内教师 */
    private String schoolTeacher;

    /** 企业导师 */
    private String companyTeacher;

    public void setContractId(Long contractId) 
    {
        this.contractId = contractId;
    }

    public Long getContractId() 
    {
        return contractId;
    }

    public void setContractNumber(String contractNumber) 
    {
        this.contractNumber = contractNumber;
    }

    public String getContractNumber() 
    {
        return contractNumber;
    }

    public void setContractName(String contractName) 
    {
        this.contractName = contractName;
    }

    public String getContractName() 
    {
        return contractName;
    }

    public void setSignDate(Date signDate) 
    {
        this.signDate = signDate;
    }

    public Date getSignDate() 
    {
        return signDate;
    }

    public void setStudentId(Long studentId) 
    {
        this.studentId = studentId;
    }

    public Long getStudentId() 
    {
        return studentId;
    }

    public void setCompanyName(String companyName) 
    {
        this.companyName = companyName;
    }

    public String getCompanyName() 
    {
        return companyName;
    }

    public void setPostName(String postName) 
    {
        this.postName = postName;
    }

    public String getPostName() 
    {
        return postName;
    }

    public void setSalary(BigDecimal salary) 
    {
        this.salary = salary;
    }

    public BigDecimal getSalary() 
    {
        return salary;
    }

    public String getStudentName() 
    {
        return studentName;
    }

    public void setStudentName(String studentName) 
    {
        this.studentName = studentName;
    }

    public String getStudentNumber() 
    {
        return studentNumber;
    }

    public void setStudentNumber(String studentNumber) 
    {
        this.studentNumber = studentNumber;
    }

    public String getMajor() 
    {
        return major;
    }

    public void setMajor(String major) 
    {
        this.major = major;
    }

    public String getSchoolTeacher() 
    {
        return schoolTeacher;
    }

    public void setSchoolTeacher(String schoolTeacher) 
    {
        this.schoolTeacher = schoolTeacher;
    }

    public String getCompanyTeacher() 
    {
        return companyTeacher;
    }

    public void setCompanyTeacher(String companyTeacher) 
    {
        this.companyTeacher = companyTeacher;
    }

    @Override
    public String toString() {
        return "StudentContract{" +
                "contractId=" + contractId +
                ", contractNumber='" + contractNumber + '\'' +
                ", contractName='" + contractName + '\'' +
                ", signDate=" + signDate +
                ", studentId=" + studentId +
                ", companyName='" + companyName + '\'' +
                ", postName='" + postName + '\'' +
                ", salary=" + salary +
                ", studentName='" + studentName + '\'' +
                ", studentNumber='" + studentNumber + '\'' +
                ", major='" + major + '\'' +
                ", schoolTeacher='" + schoolTeacher + '\'' +
                ", companyTeacher='" + companyTeacher + '\'' +
                '}';
    }
} 