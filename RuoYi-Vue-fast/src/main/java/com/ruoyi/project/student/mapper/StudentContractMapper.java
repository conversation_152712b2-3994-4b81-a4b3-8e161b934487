package com.ruoyi.project.student.mapper;

import java.util.List;
import com.ruoyi.project.student.domain.StudentContract;

/**
 * 学生合同信息Mapper接口
 * 
 * <AUTHOR>
 * @date 2024-01-01
 */
public interface StudentContractMapper 
{
    /**
     * 查询学生合同信息
     * 
     * @param contractId 学生合同信息主键
     * @return 学生合同信息
     */
    public StudentContract selectStudentContractByContractId(Long contractId);

    /**
     * 查询学生合同信息列表（含关联的学生信息）
     * 
     * @param studentContract 学生合同信息
     * @return 学生合同信息集合
     */
    public List<StudentContract> selectStudentContractList(StudentContract studentContract);

    /**
     * 新增学生合同信息
     * 
     * @param studentContract 学生合同信息
     * @return 结果
     */
    public int insertStudentContract(StudentContract studentContract);

    /**
     * 修改学生合同信息
     * 
     * @param studentContract 学生合同信息
     * @return 结果
     */
    public int updateStudentContract(StudentContract studentContract);

    /**
     * 删除学生合同信息
     * 
     * @param contractId 学生合同信息主键
     * @return 结果
     */
    public int deleteStudentContractByContractId(Long contractId);

    /**
     * 批量删除学生合同信息
     * 
     * @param contractIds 需要删除的数据主键集合
     * @return 结果
     */
    public int deleteStudentContractByContractIds(Long[] contractIds);

    /**
     * 校验合同编号是否唯一
     * 
     * @param contractNumber 合同编号
     * @return 结果
     */
    public StudentContract checkContractNumberUnique(String contractNumber);
} 