package com.ruoyi.project.student.controller;

import java.util.List;
import javax.servlet.http.HttpServletResponse;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import com.ruoyi.framework.aspectj.lang.annotation.Log;
import com.ruoyi.framework.aspectj.lang.enums.BusinessType;
import com.ruoyi.project.student.domain.StudentExamScore;
import com.ruoyi.project.student.service.IStudentExamScoreService;
import com.ruoyi.framework.web.controller.BaseController;
import com.ruoyi.framework.web.domain.AjaxResult;
import com.ruoyi.common.utils.poi.ExcelUtil;
import com.ruoyi.framework.web.page.TableDataInfo;

/**
 * 学生考试成绩Controller
 * 
 * <AUTHOR>
 * @date 2024-01-01
 */
@RestController
@RequestMapping("/student/score")
public class StudentExamScoreController extends BaseController
{
    @Autowired
    private IStudentExamScoreService studentExamScoreService;

    /**
     * 查询学生考试成绩列表
     */
    @PreAuthorize("@ss.hasPermi('student:score:list')")
    @GetMapping("/list")
    public TableDataInfo list(StudentExamScore studentExamScore)
    {
        startPage();
        List<StudentExamScore> list = studentExamScoreService.selectStudentExamScoreList(studentExamScore);
        return getDataTable(list);
    }

    /**
     * 根据学生ID查询考试成绩列表
     */
    @PreAuthorize("@ss.hasPermi('student:score:list')")
    @GetMapping("/list/{studentId}")
    public AjaxResult listByStudentId(@PathVariable("studentId") Long studentId)
    {
        List<StudentExamScore> list = studentExamScoreService.selectStudentExamScoreListByStudentId(studentId);
        return success(list);
    }

    /**
     * 导出学生考试成绩列表
     */
    @PreAuthorize("@ss.hasPermi('student:score:list')")
    @Log(title = "学生考试成绩", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, StudentExamScore studentExamScore)
    {
        List<StudentExamScore> list = studentExamScoreService.selectStudentExamScoreList(studentExamScore);
        ExcelUtil<StudentExamScore> util = new ExcelUtil<StudentExamScore>(StudentExamScore.class);
        util.exportExcel(response, list, "学生考试成绩数据");
    }

    /**
     * 获取学生考试成绩详细信息
     */
    @PreAuthorize("@ss.hasPermi('student:score:list')")
    @GetMapping(value = "/{scoreId}")
    public AjaxResult getInfo(@PathVariable("scoreId") Long scoreId)
    {
        return success(studentExamScoreService.selectStudentExamScoreByScoreId(scoreId));
    }

    /**
     * 新增学生考试成绩
     */
    @PreAuthorize("@ss.hasPermi('student:score:list')")
    @Log(title = "学生考试成绩", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody StudentExamScore studentExamScore)
    {
        return toAjax(studentExamScoreService.insertStudentExamScore(studentExamScore));
    }

    /**
     * 修改学生考试成绩
     */
    @PreAuthorize("@ss.hasPermi('student:score:list')")
    @Log(title = "学生考试成绩", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody StudentExamScore studentExamScore)
    {
        return toAjax(studentExamScoreService.updateStudentExamScore(studentExamScore));
    }

    /**
     * 删除学生考试成绩
     */
    @PreAuthorize("@ss.hasPermi('student:score:list')")
    @Log(title = "学生考试成绩", businessType = BusinessType.DELETE)
	@DeleteMapping("/{scoreIds}")
    public AjaxResult remove(@PathVariable Long[] scoreIds)
    {
        return toAjax(studentExamScoreService.deleteStudentExamScoreByScoreIds(scoreIds));
    }
} 