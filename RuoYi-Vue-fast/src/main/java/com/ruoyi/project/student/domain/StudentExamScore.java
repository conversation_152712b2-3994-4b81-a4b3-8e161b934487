package com.ruoyi.project.student.domain;

import java.math.BigDecimal;
import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import javax.validation.constraints.Size;
import com.ruoyi.framework.aspectj.lang.annotation.Excel;
import com.ruoyi.framework.web.domain.BaseEntity;

/**
 * 学生考试成绩对象 student_exam_score
 * 
 * <AUTHOR>
 * @date 2024-01-01
 */
public class StudentExamScore extends BaseEntity
{
    private static final long serialVersionUID = 1L;

    /** 成绩ID */
    private Long scoreId;

    /** 学生ID */
    @Excel(name = "学生ID")
    @NotNull(message = "学生ID不能为空")
    private Long studentId;

    /** 课程名称 */
    @Excel(name = "课程名称")
    @NotBlank(message = "课程名称不能为空")
    @Size(min = 0, max = 100, message = "课程名称长度不能超过100个字符")
    private String courseName;

    /** 课程类型（0校内课程 1企业课程） */
    @Excel(name = "课程类型", readConverterExp = "0=校内课程,1=企业课程")
    @NotBlank(message = "课程类型不能为空")
    private String courseType;

    /** 考试类型 */
    @Excel(name = "考试类型")
    @Size(min = 0, max = 50, message = "考试类型长度不能超过50个字符")
    private String examType;

    /** 成绩 */
    @Excel(name = "成绩")
    @NotNull(message = "成绩不能为空")
    private BigDecimal score;

    public void setScoreId(Long scoreId) 
    {
        this.scoreId = scoreId;
    }

    public Long getScoreId() 
    {
        return scoreId;
    }

    public void setStudentId(Long studentId) 
    {
        this.studentId = studentId;
    }

    public Long getStudentId() 
    {
        return studentId;
    }

    public void setCourseName(String courseName) 
    {
        this.courseName = courseName;
    }

    public String getCourseName() 
    {
        return courseName;
    }

    public void setCourseType(String courseType) 
    {
        this.courseType = courseType;
    }

    public String getCourseType() 
    {
        return courseType;
    }

    public void setExamType(String examType) 
    {
        this.examType = examType;
    }

    public String getExamType() 
    {
        return examType;
    }

    public void setScore(BigDecimal score) 
    {
        this.score = score;
    }

    public BigDecimal getScore() 
    {
        return score;
    }

    @Override
    public String toString() {
        return "StudentExamScore{" +
                "scoreId=" + scoreId +
                ", studentId=" + studentId +
                ", courseName='" + courseName + '\'' +
                ", courseType='" + courseType + '\'' +
                ", examType='" + examType + '\'' +
                ", score=" + score +
                '}';
    }
} 