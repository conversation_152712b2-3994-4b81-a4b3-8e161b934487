package com.ruoyi.project.student.service;

import java.util.List;
import com.ruoyi.project.student.domain.StudentContract;

/**
 * 学生合同信息Service接口
 * 
 * <AUTHOR>
 * @date 2024-01-01
 */
public interface IStudentContractService 
{
    /**
     * 查询学生合同信息
     * 
     * @param contractId 学生合同信息主键
     * @return 学生合同信息
     */
    public StudentContract selectStudentContractByContractId(Long contractId);

    /**
     * 查询学生合同信息列表
     * 
     * @param studentContract 学生合同信息
     * @return 学生合同信息集合
     */
    public List<StudentContract> selectStudentContractList(StudentContract studentContract);

    /**
     * 新增学生合同信息
     * 
     * @param studentContract 学生合同信息
     * @return 结果
     */
    public int insertStudentContract(StudentContract studentContract);

    /**
     * 修改学生合同信息
     * 
     * @param studentContract 学生合同信息
     * @return 结果
     */
    public int updateStudentContract(StudentContract studentContract);

    /**
     * 批量删除学生合同信息
     * 
     * @param contractIds 需要删除的学生合同信息主键集合
     * @return 结果
     */
    public int deleteStudentContractByContractIds(Long[] contractIds);

    /**
     * 删除学生合同信息信息
     * 
     * @param contractId 学生合同信息主键
     * @return 结果
     */
    public int deleteStudentContractByContractId(Long contractId);

    /**
     * 校验合同编号是否唯一
     * 
     * @param studentContract 学生合同信息
     * @return 结果
     */
    public boolean checkContractNumberUnique(StudentContract studentContract);
} 