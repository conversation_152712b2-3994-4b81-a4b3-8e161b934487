package com.ruoyi.project.student.service.impl;

import java.util.List;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import com.ruoyi.common.constant.UserConstants;
import com.ruoyi.common.utils.DateUtils;
import com.ruoyi.common.utils.StringUtils;
import com.ruoyi.framework.web.controller.BaseController;
import com.ruoyi.project.student.domain.StudentContract;
import com.ruoyi.project.student.mapper.StudentContractMapper;
import com.ruoyi.project.student.service.IStudentContractService;

/**
 * 学生合同信息Service业务层处理
 * 
 * <AUTHOR>
 * @date 2024-01-01
 */
@Service
public class StudentContractServiceImpl extends BaseController implements IStudentContractService 
{
    @Autowired
    private StudentContractMapper studentContractMapper;

    /**
     * 查询学生合同信息
     * 
     * @param contractId 学生合同信息主键
     * @return 学生合同信息
     */
    @Override
    public StudentContract selectStudentContractByContractId(Long contractId)
    {
        return studentContractMapper.selectStudentContractByContractId(contractId);
    }

    /**
     * 查询学生合同信息列表
     * 
     * @param studentContract 学生合同信息
     * @return 学生合同信息
     */
    @Override
    public List<StudentContract> selectStudentContractList(StudentContract studentContract)
    {
        return studentContractMapper.selectStudentContractList(studentContract);
    }

    /**
     * 新增学生合同信息
     * 
     * @param studentContract 学生合同信息
     * @return 结果
     */
    @Override
    public int insertStudentContract(StudentContract studentContract)
    {
        // 设置创建人和创建时间
        studentContract.setCreateBy(getUsername());
        studentContract.setCreateTime(DateUtils.getNowDate());
        return studentContractMapper.insertStudentContract(studentContract);
    }

    /**
     * 修改学生合同信息
     * 
     * @param studentContract 学生合同信息
     * @return 结果
     */
    @Override
    public int updateStudentContract(StudentContract studentContract)
    {
        // 设置更新人和更新时间
        studentContract.setUpdateBy(getUsername());
        studentContract.setUpdateTime(DateUtils.getNowDate());
        return studentContractMapper.updateStudentContract(studentContract);
    }

    /**
     * 批量删除学生合同信息
     * 
     * @param contractIds 需要删除的学生合同信息主键
     * @return 结果
     */
    @Override
    public int deleteStudentContractByContractIds(Long[] contractIds)
    {
        return studentContractMapper.deleteStudentContractByContractIds(contractIds);
    }

    /**
     * 删除学生合同信息信息
     * 
     * @param contractId 学生合同信息主键
     * @return 结果
     */
    @Override
    public int deleteStudentContractByContractId(Long contractId)
    {
        return studentContractMapper.deleteStudentContractByContractId(contractId);
    }

    /**
     * 校验合同编号是否唯一
     * 
     * @param studentContract 学生合同信息
     * @return 结果
     */
    @Override
    public boolean checkContractNumberUnique(StudentContract studentContract)
    {
        Long contractId = StringUtils.isNull(studentContract.getContractId()) ? -1L : studentContract.getContractId();
        StudentContract info = studentContractMapper.checkContractNumberUnique(studentContract.getContractNumber());
        if (StringUtils.isNotNull(info) && info.getContractId().longValue() != contractId.longValue())
        {
            return UserConstants.NOT_UNIQUE;
        }
        return UserConstants.UNIQUE;
    }
} 