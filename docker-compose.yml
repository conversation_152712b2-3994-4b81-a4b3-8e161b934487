version: '3.8'

services:
  # MySQL 数据库服务
  mysql:
    image: mysql:8.0
    container_name: ruoyi-mysql
    restart: always
    environment:
      MYSQL_ROOT_PASSWORD: 123456
      MYSQL_DATABASE: ry-vue
      MYSQL_USER: ruoyi
      MYSQL_PASSWORD: ruoyi123
      TZ: Asia/Shanghai
      # 设置字符编码
      MYSQL_CHARACTER_SET_SERVER: utf8mb4
      MYSQL_COLLATION_SERVER: utf8mb4_unicode_ci
    ports:
      - "3307:3306"
    volumes:
      - mysql_data:/var/lib/mysql
      - ./RuoYi-Vue-fast/sql:/docker-entrypoint-initdb.d
      - ./database/conf/my.cnf:/etc/mysql/conf.d/my.cnf
    command: --default-authentication-plugin=mysql_native_password --character-set-server=utf8mb4 --collation-server=utf8mb4_unicode_ci
    networks:
      - ruoyi-network

  # Redis缓存服务
  redis:
    image: redis:7-alpine
    container_name: ruoyi-redis
    restart: always
    ports:
      - "6379:6379"
    volumes:
      - redis_data:/data
      - ./database/conf/redis.conf:/usr/local/etc/redis/redis.conf
    command: redis-server /usr/local/etc/redis/redis.conf
    networks:
      - ruoyi-network

volumes:
  mysql_data:
    driver: local
  redis_data:
    driver: local

networks:
  ruoyi-network:
    driver: bridge 